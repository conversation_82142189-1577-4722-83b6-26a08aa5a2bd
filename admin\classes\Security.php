<?php
class Security {
    private $db;
    private $maxLoginAttempts = 5;
    private $lockoutTime = 900; // 15 minutes in seconds

    public function __construct($db) {
        $this->db = $db;
        $this->createTables();
    }

    private function createTables() {
        try {
            // Create activity_logs table
            $this->db->exec("CREATE TABLE IF NOT EXISTS activity_logs (
                log_id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT,
                activity_type VARCHAR(50) NOT NULL,
                description TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
            )");

            // Create login_attempts table
            $this->db->exec("CREATE TABLE IF NOT EXISTS login_attempts (
                attempt_id INT PRIMARY KEY AUTO_INCREMENT,
                email VARCHAR(100) NOT NULL,
                ip_address VARCHAR(45) NOT NULL,
                attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX (email, ip_address)
            )");
        } catch (PDOException $e) {
            error_log("Error creating security tables: " . $e->getMessage());
        }
    }

    public function getClientIP() {
        $ipAddress = '';
        if (isset($_SERVER['HTTP_CLIENT_IP']))
            $ipAddress = $_SERVER['HTTP_CLIENT_IP'];
        else if(isset($_SERVER['HTTP_X_FORWARDED_FOR']))
            $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
        else if(isset($_SERVER['HTTP_X_FORWARDED']))
            $ipAddress = $_SERVER['HTTP_X_FORWARDED'];
        else if(isset($_SERVER['HTTP_FORWARDED_FOR']))
            $ipAddress = $_SERVER['HTTP_FORWARDED_FOR'];
        else if(isset($_SERVER['HTTP_FORWARDED']))
            $ipAddress = $_SERVER['HTTP_FORWARDED'];
        else if(isset($_SERVER['REMOTE_ADDR']))
            $ipAddress = $_SERVER['REMOTE_ADDR'];
        else
            $ipAddress = 'UNKNOWN';
        return $ipAddress;
    }

    public function isIPBlocked($ip) {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM login_attempts WHERE ip_address = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)");
            $stmt->execute([$ip, $this->lockoutTime]);
            $attempts = $stmt->fetchColumn();
            return $attempts >= $this->maxLoginAttempts;
        } catch (PDOException $e) {
            error_log("Error checking IP block: " . $e->getMessage());
            return false;
        }
    }

    public function logActivity($userId, $activityType, $description = '') {
        try {
            $stmt = $this->db->prepare("INSERT INTO activity_logs (user_id, activity_type, description, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([
                $userId,
                $activityType,
                $description,
                $this->getClientIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
            ]);
            return true;
        } catch (PDOException $e) {
            error_log("Error logging activity: " . $e->getMessage());
            return false;
        }
    }

    public function logLoginAttempt($email) {
        try {
            $stmt = $this->db->prepare("INSERT INTO login_attempts (email, ip_address) VALUES (?, ?)");
            $stmt->execute([$email, $this->getClientIP()]);
            return true;
        } catch (PDOException $e) {
            error_log("Error logging login attempt: " . $e->getMessage());
            return false;
        }
    }

    public function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeInput'], $data);
        }
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }

    public function generateToken($length = 32) {
        return bin2hex(random_bytes($length));
    }

    public function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }

    public function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }

    public function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    public function validatePassword($password) {
        // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
        return strlen($password) >= 8 &&
               preg_match('/[A-Z]/', $password) &&
               preg_match('/[a-z]/', $password) &&
               preg_match('/[0-9]/', $password);
    }

    public function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = $this->generateToken();
        }
        return $_SESSION['csrf_token'];
    }

    public function validateCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }

    public function checkBruteForce($email) {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) FROM login_attempts WHERE email = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL ? SECOND)");
            $stmt->execute([$email, $this->lockoutTime]);
            $attempts = $stmt->fetchColumn();
            return $attempts >= $this->maxLoginAttempts;
        } catch (PDOException $e) {
            error_log("Error checking brute force: " . $e->getMessage());
            return false;
        }
    }

    public function clearLoginAttempts($email) {
        try {
            $stmt = $this->db->prepare("DELETE FROM login_attempts WHERE email = ?");
            $stmt->execute([$email]);
            return true;
        } catch (PDOException $e) {
            error_log("Error clearing login attempts: " . $e->getMessage());
            return false;
        }
    }
} 