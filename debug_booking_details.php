<?php
// Debug script to check booking details issue
require_once 'admin/includes/config.php';

try {
    echo "=== DEBUGGING BOOKING DETAILS ISSUE ===\n\n";
    
    // Check if customers table exists
    $tables_query = "SHOW TABLES LIKE 'customers'";
    $result = $con->query($tables_query);
    echo "Customers table exists: " . ($result->num_rows > 0 ? "YES" : "NO") . "\n\n";
    
    // Check bookings table structure
    echo "BOOKINGS TABLE STRUCTURE:\n";
    $structure_query = "DESCRIBE bookings";
    $result = $con->query($structure_query);
    while ($row = $result->fetch_assoc()) {
        echo "- {$row['Field']}: {$row['Type']}\n";
    }
    
    // Check if there are any bookings
    echo "\nBOOKINGS COUNT:\n";
    $count_query = "SELECT COUNT(*) as total FROM bookings";
    $result = $con->query($count_query);
    $count = $result->fetch_assoc()['total'];
    echo "Total bookings: $count\n\n";
    
    if ($count > 0) {
        echo "SAMPLE BOOKING DATA:\n";
        $sample_query = "SELECT booking_id, first_name, last_name, email, booking_status FROM bookings LIMIT 3";
        $result = $con->query($sample_query);
        while ($row = $result->fetch_assoc()) {
            echo "ID: {$row['booking_id']}, Name: {$row['first_name']} {$row['last_name']}, Status: {$row['booking_status']}\n";
        }
    }
    
    // Test the problematic query with a specific booking ID
    if ($count > 0) {
        echo "\nTESTING BOOKING DETAILS QUERY:\n";
        $test_query = "SELECT booking_id FROM bookings LIMIT 1";
        $result = $con->query($test_query);
        $test_booking = $result->fetch_assoc();
        $test_id = $test_booking['booking_id'];
        
        echo "Testing with booking ID: $test_id\n";
        
        // Simple query without joins first
        $simple_query = "SELECT * FROM bookings WHERE booking_id = ?";
        $stmt = $con->prepare($simple_query);
        $stmt->bind_param("i", $test_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            echo "Simple query works: YES\n";
            $booking = $result->fetch_assoc();
            echo "Booking found: {$booking['first_name']} {$booking['last_name']}\n";
        } else {
            echo "Simple query works: NO\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

$con->close();
?>
