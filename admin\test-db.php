<?php
// Test database connection and check for pending bookings
include('includes/config.php');

echo "<h2>Database Connection Test</h2>";

try {
    // Test connection
    if (!$con) {
        echo "❌ Database connection failed<br>";
        exit;
    }
    
    echo "✅ Database connection successful<br><br>";
    
    // Check for pending bookings
    $sql = "SELECT booking_id, booking_code, first_name, last_name, booking_status FROM bookings WHERE booking_status = 'pending' LIMIT 5";
    $result = $con->query($sql);
    
    echo "<h3>Pending Bookings:</h3>";
    if ($result && $result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Status</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['booking_id'] . "</td>";
            echo "<td>" . $row['booking_code'] . "</td>";
            echo "<td>" . $row['first_name'] . " " . $row['last_name'] . "</td>";
            echo "<td>" . $row['booking_status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No pending bookings found.<br>";
        
        // Check all bookings
        $all_sql = "SELECT booking_id, booking_code, first_name, last_name, booking_status FROM bookings LIMIT 5";
        $all_result = $con->query($all_sql);
        
        echo "<h3>All Bookings (first 5):</h3>";
        if ($all_result && $all_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Status</th></tr>";
            while ($row = $all_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['booking_id'] . "</td>";
                echo "<td>" . $row['booking_code'] . "</td>";
                echo "<td>" . $row['first_name'] . " " . $row['last_name'] . "</td>";
                echo "<td>" . $row['booking_status'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Create a test pending booking
            echo "<br><h3>Creating test pending booking...</h3>";
            $insert_sql = "INSERT INTO bookings (first_name, last_name, email, contact_number, booking_status, booking_code, total, start_date, end_date) 
                          VALUES ('Test', 'User', '<EMAIL>', '09123456789', 'pending', 'TEST-" . date('YmdHis') . "', 1500.00, NOW(), NOW())";
            
            if ($con->query($insert_sql)) {
                echo "✅ Test pending booking created with ID: " . $con->insert_id . "<br>";
            } else {
                echo "❌ Failed to create test booking: " . $con->error . "<br>";
            }
        } else {
            echo "No bookings found at all.<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}
?>
