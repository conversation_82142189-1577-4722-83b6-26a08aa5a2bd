<?php
// Integrated verification process for Carles Tourism Booking System
// This file handles booking saving, email verification, and admin notifications in one process

// Include database connection
require_once '../config/db_connect.php';

// Include PHPMailer classes
use PHPMailer\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Include PHPMailer files
require_once 'phpmailer/src/Exception.php';
require_once 'phpmailer/src/PHPMailer.php';
require_once 'phpmailer/src/SMTP.php';

// Function to log debug information
function debug_log($message, $data = null) {
    $log_file = 'logs/integrated_' . date('Y-m-d') . '.log';
    $log_dir = dirname($log_file);

    // Create logs directory if it doesn't exist
    if (!file_exists($log_dir)) {
        mkdir($log_dir, 0777, true);
    }

    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[$timestamp] $message";
    
    if ($data !== null) {
        $log_message .= ': ' . print_r($data, true);
    }
    
    $log_message .= PHP_EOL;

    file_put_contents($log_file, $log_message, FILE_APPEND);
}

// Log start of script
debug_log('Integrated verification process started');

// Get booking data from POST
$booking = $_POST;

// Debug log the booking data
debug_log('Received booking data', $booking);

// Generate a unique booking code
$booking_code = 'BOAT-' . date('Ymd') . '-' . rand(10000, 99999);

// Extract booking details
$first_name = isset($booking['firstName']) ? $con->real_escape_string($booking['firstName']) : '';
$last_name = isset($booking['lastName']) ? $con->real_escape_string($booking['lastName']) : '';
$suffix = isset($booking['suffix']) ? $con->real_escape_string($booking['suffix']) : '';
$age = isset($booking['age']) ? intval($booking['age']) : 0;
$sex = isset($booking['sex']) ? $con->real_escape_string($booking['sex']) : '';
$email = isset($booking['emailAddress']) ? $con->real_escape_string($booking['emailAddress']) : '';
$contact = isset($booking['contactNumber']) ? $con->real_escape_string($booking['contactNumber']) : '';
$complete_address = isset($booking['completeAddress']) ? $con->real_escape_string($booking['completeAddress']) : '';
$destination = isset($booking['locationTourDestination']) ? $con->real_escape_string($booking['locationTourDestination']) : '';
$drop_off = isset($booking['dropOffLocation']) ? $con->real_escape_string($booking['dropOffLocation']) : '';
$pax = isset($booking['numberOfPax']) ? intval($booking['numberOfPax']) : 0;
$total_passengers = isset($booking['totalPassengers']) ? intval($booking['totalPassengers']) : $pax;

// Passenger breakdown
$regular_pax = isset($booking['regularPax']) ? intval($booking['regularPax']) : 0;
$discounted_pax = isset($booking['discountedPax']) ? intval($booking['discountedPax']) : 0;
$children_pax = isset($booking['childrenPax']) ? intval($booking['childrenPax']) : 0;
$infants_pax = isset($booking['infantsPax']) ? intval($booking['infantsPax']) : 0;

// Don't auto-set current date - require proper date from form
$start_date = isset($booking['startDate']) && !empty($booking['startDate']) ? $booking['startDate'] : '';
$end_date = isset($booking['endDate']) && !empty($booking['endDate']) ? $booking['endDate'] : '';
$payment_method = isset($booking['paymentMethod']) ? $con->real_escape_string($booking['paymentMethod']) : 'Manual Payment';
$total = isset($booking['total']) ? floatval($booking['total']) : 0;
$environmental_fee = isset($booking['totalEnvironmentalFee']) ? floatval($booking['totalEnvironmentalFee']) : $total;
$emergency_name = isset($booking['emergencyName']) ? $con->real_escape_string($booking['emergencyName']) : '';
$emergency_number = isset($booking['emergencyNumber']) ? $con->real_escape_string($booking['emergencyNumber']) : '';

// Handle passenger details (companions only - starting from passenger 2)
$passenger_details = array();

// Add main booker as passenger 1
$passenger_details[] = array(
    'name' => $first_name . ' ' . $last_name . ($suffix ? ' ' . $suffix : ''),
    'first_name' => $first_name,
    'last_name' => $last_name,
    'age' => $age,
    'sex' => $sex,
    'complete_address' => $complete_address,
    'contact_number' => $contact,
    'type' => 'main_booker'
);

// Add companions (passenger 2 onwards)
for ($i = 2; $i <= $total_passengers; $i++) {
    $firstName = isset($booking["passenger{$i}FirstName"]) ? $con->real_escape_string($booking["passenger{$i}FirstName"]) : '';
    $lastName = isset($booking["passenger{$i}LastName"]) ? $con->real_escape_string($booking["passenger{$i}LastName"]) : '';
    $age = isset($booking["passenger{$i}Age"]) ? intval($booking["passenger{$i}Age"]) : 0;
    $sex = isset($booking["passenger{$i}Sex"]) ? $con->real_escape_string($booking["passenger{$i}Sex"]) : '';
    $completeAddress = isset($booking["passenger{$i}CompleteAddress"]) ? $con->real_escape_string($booking["passenger{$i}CompleteAddress"]) : '';
    $contactNumber = isset($booking["passenger{$i}ContactNumber"]) ? $con->real_escape_string($booking["passenger{$i}ContactNumber"]) : '';

    if (!empty($firstName) && !empty($lastName)) {
        $passenger_details[] = array(
            'name' => $firstName . ' ' . $lastName,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'age' => $age,
            'sex' => $sex,
            'complete_address' => $completeAddress,
            'contact_number' => $contactNumber,
            'type' => 'companion'
        );
    }
}
$passenger_details_json = json_encode($passenger_details);

// Handle file upload for payment proof
$payment_proof = '';
if (isset($_FILES['gcashProofUpload']) && $_FILES['gcashProofUpload']['error'] == 0) {
    $upload_dir = '../uploads/payment_proofs/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }

    // Validate file type
    $allowed_types = array('jpg', 'jpeg', 'png');
    $file_extension = strtolower(pathinfo($_FILES['gcashProofUpload']['name'], PATHINFO_EXTENSION));

    if (in_array($file_extension, $allowed_types)) {
        $payment_proof = $booking_code . '_proof.' . $file_extension;
        $upload_path = $upload_dir . $payment_proof;

        if (move_uploaded_file($_FILES['gcashProofUpload']['tmp_name'], $upload_path)) {
            debug_log('Payment proof uploaded successfully', $payment_proof);
        } else {
            debug_log('Failed to upload payment proof');
            $payment_proof = '';
        }
    } else {
        debug_log('Invalid file type for payment proof', $file_extension);
        $payment_proof = '';
    }
}

// Insert booking into database
$query = "INSERT INTO bookings (
    booking_code, first_name, last_name, suffix, age, sex, email, contact_number, address,
    tour_destination, drop_off_location, no_of_pax, total_passengers,
    regular_pax, discounted_pax, children_pax, infants_pax,
    start_date, end_date, booking_time, environmental_fee, payment_method, payment_proof, total,
    booking_status, created_at, is_today_booking, emergency_name, emergency_number,
    passenger_details, boat_id, destination_id
) VALUES (
    '$booking_code', '$first_name', '$last_name', '$suffix', $age, '$sex', '$email', '$contact', '$complete_address',
    '$destination', '$drop_off', $pax, $total_passengers,
    $regular_pax, $discounted_pax, $children_pax, $infants_pax,
    '$start_date', '$end_date', NOW(), $environmental_fee, '$payment_method', '$payment_proof', $total,
    'pending', NOW(), 1, '$emergency_name', '$emergency_number',
    '$passenger_details_json', 1, 1
)";

debug_log('Executing database query', $query);

try {
    // Execute query to save booking
    if ($con->query($query)) {
        $booking_id = $con->insert_id;
        debug_log('Booking saved successfully with ID', $booking_id);

        // Save individual passenger details to booking_passengers table
        if (!empty($passenger_details)) {
            foreach ($passenger_details as $passenger) {
                // Determine passenger type based on age
                $passenger_type = 'regular';
                if ($passenger['age'] <= 5) {
                    $passenger_type = 'infants';
                } elseif ($passenger['age'] <= 12) {
                    $passenger_type = 'children';
                } elseif ($passenger['age'] >= 60) {
                    $passenger_type = 'discounted'; // Senior citizen
                }

                // Override with main booker type if specified
                if (isset($passenger['type']) && $passenger['type'] == 'main_booker') {
                    $passenger_type = 'main_booker';
                }

                $passenger_sex = isset($passenger['sex']) ? $con->real_escape_string($passenger['sex']) : '';

                // Use individual fields from passenger data
                $first_name = isset($passenger['first_name']) ? $con->real_escape_string($passenger['first_name']) : '';
                $last_name = isset($passenger['last_name']) ? $con->real_escape_string($passenger['last_name']) : '';
                $complete_address = isset($passenger['complete_address']) ? $con->real_escape_string($passenger['complete_address']) : '';
                $contact_number = isset($passenger['contact_number']) ? $con->real_escape_string($passenger['contact_number']) : '';

                // Determine if this is main contact (first passenger)
                $is_main_contact = ($passenger['type'] == 'main_booker') ? 1 : 0;

                $passenger_query = "INSERT INTO booking_passengers (
                    booking_id, first_name, last_name, age, sex, complete_address, contact_number, passenger_type, is_main_contact
                ) VALUES (
                    $booking_id, '$first_name', '$last_name', {$passenger['age']}, '$passenger_sex', '$complete_address', '$contact_number', '$passenger_type', $is_main_contact
                )";

                if (!$con->query($passenger_query)) {
                    debug_log('Failed to save passenger details', $con->error);
                } else {
                    debug_log('Saved passenger', $passenger['name']);
                }
            }
            debug_log('All passenger details saved successfully');
        }
        
        // Create notification for admin
        $notif_message = "New booking #$booking_code from $first_name $last_name for $destination. " .
                        "Date: $start_date to $end_date, Passengers: $pax";
        
        $notif_sql = "INSERT INTO notifications (
            user_id, message, type, reference_id, is_read, created_at
        ) VALUES (
            1, '$notif_message', 'new_booking', $booking_id, 0, NOW()
        )";
        
        if ($con->query($notif_sql)) {
            debug_log('Admin notification created successfully');
        } else {
            debug_log('Error creating admin notification', $con->error);
        }
        
        // Send verification email
        try {
            // Create a new PHPMailer instance
            $mail = new PHPMailer(true);
            
            // Server settings
            $mail->SMTPDebug = SMTP::DEBUG_SERVER;                  // Enable verbose debug output
            $mail->Debugoutput = function($str, $level) {
                debug_log("PHPMailer Debug: $str");
            };
            $mail->isSMTP();                                        // Send using SMTP
            $mail->Host       = 'smtp.gmail.com';                   // Set the SMTP server to send through
            $mail->SMTPAuth   = true;                               // Enable SMTP authentication
            $mail->Username   = '<EMAIL>';    // SMTP username
            $mail->Password   = 'nvdk kolr vcbb lyut';              // SMTP password (app password)
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;     // Enable explicit TLS encryption
            $mail->Port       = 587;                                // TCP port to connect to
            
            // Set timeout to avoid long waits
            $mail->Timeout    = 60;
            
            // Recipients
            $mail->setFrom('<EMAIL>', 'Carles Tourism Booking');
            $mail->addAddress($email, "$first_name $last_name");
            $mail->addReplyTo('<EMAIL>', 'Carles Tourism Support');
            
            // Content
            $mail->isHTML(true);
            $mail->Subject = "Carles Tourism Booking Confirmation - $booking_code";
            
            // Create email body
            $mail_body = "
            <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd;'>
                <div style='background-color: #218c5b; color: white; padding: 15px; text-align: center;'>
                    <h2 style='margin: 0;'>BOOKING CONFIRMATION</h2>
                </div>
                
                <div style='padding: 20px;'>
                    <p>Dear <strong>$first_name $last_name</strong>,</p>
                    
                    <p>Thank you for booking with <strong>Carles Tourism</strong>. Your booking has been received and is pending confirmation.</p>
                    
                    <div style='background-color: #f5f5f5; padding: 15px; margin: 15px 0; border-left: 4px solid #218c5b;'>
                        <p><strong>Booking ID:</strong> $booking_code</p>
                        <p><strong>Destination:</strong> $destination</p>
                        <p><strong>Date:</strong> $start_date to $end_date</p>
                        <p><strong>Passengers:</strong> $pax</p>
                        <p><strong>Drop-off Location:</strong> $drop_off</p>
                        <p><strong>Payment Method:</strong> $payment_method</p>
                        <p><strong>Total Amount:</strong> ₱" . number_format($total, 2) . "</p>
                    </div>
                    
                    <p>We will review your booking and get back to you shortly. If you have any questions, please contact us at <a href='mailto:<EMAIL>'><EMAIL></a> or call us at 0945 799 3491.</p>
                    
                    <p>Thank you for choosing Carles Tourism!</p>
                </div>
                
                <div style='background-color: #f5f5f5; padding: 10px; text-align: center; font-size: 12px;'>
                    <p>&copy; " . date('Y') . " Carles Tourism. All rights reserved.</p>
                </div>
            </div>
            ";
            
            $mail->Body = $mail_body;
            $mail->AltBody = strip_tags(str_replace('<br>', "\n", $mail_body));
            
            // Send the email
            debug_log('Attempting to send verification email to', $email);
            
            if ($mail->send()) {
                debug_log('Verification email sent successfully');
                
                // Return success response
                echo json_encode([
                    'success' => true,
                    'message' => 'Booking saved and verification email sent successfully.',
                    'booking_code' => $booking_code,
                    'booking_id' => $booking_id
                ]);
            } else {
                debug_log('Failed to send verification email', $mail->ErrorInfo);
                
                // Try with alternative settings (SSL on port 465)
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
                $mail->Port = 465;
                debug_log('Retrying with SMTPS on port 465');
                
                if ($mail->send()) {
                    debug_log('Verification email sent successfully on second attempt');
                    
                    // Return success response
                    echo json_encode([
                        'success' => true,
                        'message' => 'Booking saved and verification email sent successfully.',
                        'booking_code' => $booking_code,
                        'booking_id' => $booking_id
                    ]);
                } else {
                    debug_log('Failed to send verification email on second attempt', $mail->ErrorInfo);
                    
                    // Return partial success (booking saved but email failed)
                    echo json_encode([
                        'success' => true,
                        'message' => 'Booking saved successfully, but there was an issue sending the verification email. Please check your email later.',
                        'booking_code' => $booking_code,
                        'booking_id' => $booking_id,
                        'email_error' => $mail->ErrorInfo
                    ]);
                }
            }
        } catch (Exception $e) {
            debug_log('Exception in email sending', $e->getMessage());
            
            // Return partial success (booking saved but email failed)
            echo json_encode([
                'success' => true,
                'message' => 'Booking saved successfully, but there was an issue sending the verification email. Please check your email later.',
                'booking_code' => $booking_code,
                'booking_id' => $booking_id,
                'email_error' => $e->getMessage()
            ]);
        }
    } else {
        debug_log('Error saving booking', $con->error);
        
        // Return error response
        echo json_encode([
            'success' => false,
            'message' => 'Error saving booking: ' . $con->error
        ]);
    }
} catch (Exception $e) {
    debug_log('Exception in booking process', $e->getMessage());
    
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => 'Exception in booking process: ' . $e->getMessage()
    ]);
}
?>
