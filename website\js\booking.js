// Global variables
let currentStep = 1;
const totalSteps = 3;

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing booking.js');

    // Reset currentStep to 1 on page load
    currentStep = 1;

    // Ensure localStorage does not override the currentStep
    const savedData = localStorage.getItem('bookingFormData');
    if (savedData) {
        const formData = JSON.parse(savedData);
        if (formData && formData.currentStep) {
            formData.currentStep = 1;
            localStorage.setItem('bookingFormData', JSON.stringify(formData));
        }
    }

    // Initialize date pickers
    initializeDatePickers();

    // Set up event listeners
    setupEventListeners();

    // Initialize calculations
    calculateTotalFees();
    updateDateTimeSummary();
    updateBookingTime();

    // Generate booking ID
    generateBookingId();

    // Initialize tooltips
    initializeTooltips();

    // Initialize hidden fields
    initializeHiddenFields();

    // Set current date for Today's Bookings
    setCurrentDate();

    // Load saved form data if available
    loadFormData();

    // Save form data on any input change
    document.querySelectorAll('input, select, textarea').forEach(element => {
        element.addEventListener('change', saveFormData);
        element.addEventListener('input', saveFormData);
    });

    // Save form data before page unload
    window.addEventListener('beforeunload', function() {
        console.log('Saving form data before page unload');
        saveFormData();
    });

    // Force a save of the form data after a short delay
    // This ensures any URL parameters are properly saved to localStorage
    setTimeout(function() {
        console.log('Forcing initial save of form data');
        saveFormData();
    }, 1000);
});

// Global date picker variables
window.startDatePicker = null;
window.endDatePicker = null;

// Initialize date pickers
function initializeDatePickers() {
    console.log('Initializing date pickers');

    try {
        // Start date picker
        window.startDatePicker = flatpickr("#startDate", {
            minDate: "today",
            dateFormat: "Y-m-d",
            altInput: true,
            altFormat: "F j, Y (l)",
            onChange: function(_, dateStr) {
                // Use underscore for unused parameter
                if (window.endDatePicker) {
                    window.endDatePicker.set("minDate", dateStr);
                }
                updateDateTimeSummary();
            }
        });

        // End date picker
        window.endDatePicker = flatpickr("#endDate", {
            minDate: "today",
            dateFormat: "Y-m-d",
            altInput: true,
            altFormat: "F j, Y (l)",
            onChange: function(_) {
                // Use underscore for unused parameter
                updateDateTimeSummary();
            }
        });

        console.log('Date pickers initialized successfully:', {
            startDatePicker: window.startDatePicker ? 'OK' : 'Failed',
            endDatePicker: window.endDatePicker ? 'OK' : 'Failed'
        });
    } catch (error) {
        console.error('Error initializing date pickers:', error);

        // Create a fallback for date pickers if flatpickr fails
        if (!window.startDatePicker) {
            console.log('Creating fallback for start date picker');
            const startDateInput = document.getElementById('startDate');
            if (startDateInput) {
                startDateInput.type = 'date';
                startDateInput.min = new Date().toISOString().split('T')[0];
                startDateInput.addEventListener('change', function() {
                    const endDateInput = document.getElementById('endDate');
                    if (endDateInput) {
                        endDateInput.min = this.value;
                    }
                    updateDateTimeSummary();
                });
            }
        }

        if (!window.endDatePicker) {
            console.log('Creating fallback for end date picker');
            const endDateInput = document.getElementById('endDate');
            if (endDateInput) {
                endDateInput.type = 'date';
                endDateInput.min = new Date().toISOString().split('T')[0];
                endDateInput.addEventListener('change', updateDateTimeSummary);
            }
        }
    }
}

// Set up event listeners
function setupEventListeners() {
    // Form submission
    const bookingForm = document.getElementById('bookingForm');
    if (bookingForm) {
        bookingForm.addEventListener('submit', onBookingFormSubmit);

        // Add direct click event listener to the submit button
        const submitButton = document.querySelector('.btn-submit');
        if (submitButton) {
            console.log('Adding click event listener to submit button');
            submitButton.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Submit button clicked');
                onBookingFormSubmit(e);
            });
        }
    }

    // Gender selection - no longer needed since we removed the "Other" option
    // const genderSelect = document.getElementById('gender');
    // if (genderSelect) {
    //     genderSelect.addEventListener('change', toggleGenderSpecify);
    // }

    // Auto-resize textarea to remove scrollbar
    const addressTextarea = document.getElementById('completeAddress');
    if (addressTextarea) {
        addressTextarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
            // Save form data when textarea changes
            saveFormData();
        });
        // Initial resize
        addressTextarea.style.height = 'auto';
        addressTextarea.style.height = (addressTextarea.scrollHeight) + 'px';
    }

    // Payment options
    document.querySelectorAll('.payment-option').forEach(option => {
        option.addEventListener('click', function() {
            const radio = option.querySelector('input[type="radio"]');
            radio.checked = true;
            document.querySelectorAll('.payment-option').forEach(o => o.classList.remove('selected'));
            option.classList.add('selected');

            // Show/hide payment details
            const paymentType = radio.id;
            document.getElementById('gcashDetails').style.display = paymentType === 'gcash' ? 'block' : 'none';
            document.getElementById('manualDetails').style.display = paymentType === 'manual' ? 'block' : 'none';

            // Update payment method in summary
            const summaryPaymentValue = document.getElementById('summaryPaymentValue');
            if (summaryPaymentValue) {
                summaryPaymentValue.textContent = radio.value;
                console.log('Payment method updated in summary:', radio.value);
            }

            // Save form data when payment option changes
            saveFormData();
        });
    });

    // Environmental fee inputs
    ['regularPax', 'discountedPax', 'childrenPax', 'infantsPax'].forEach(function(id) {
        const el = document.getElementById(id);
        if (el) {
            el.addEventListener('input', function() {
                calculateTotalFees();
                // Save form data when environmental fees change
                saveFormData();
            });
        }
    });

    // Number of pax
    const numberOfPaxEl = document.getElementById('numberOfPax');
    if (numberOfPaxEl) {
        numberOfPaxEl.addEventListener('input', function() {
            syncNumberOfPax();
            // Save form data when number of pax changes
            saveFormData();
        });
    }

    // Update booking summary when fields change
    const fieldsToUpdate = [
        'firstName', 'lastName', 'age', 'sex', 'contactNumber', 'emailAddress',
        'completeAddress', 'emergencyName', 'emergencyNumber', 'locationTourDestination',
        'dropOffLocation', 'numberOfPax'
    ];

    fieldsToUpdate.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', function() {
                updateBookingSummary();
                // Save form data when fields change
                saveFormData();
            });
            field.addEventListener('change', function() {
                updateBookingSummary();
                // Save form data when fields change
                saveFormData();
            });
        }
    });

    // Save form data when dates change
    document.getElementById('startDate').addEventListener('change', saveFormData);
    document.getElementById('endDate').addEventListener('change', saveFormData);

    // Save form data when navigating between steps
    window.addEventListener('beforeunload', saveFormData);
}

// Initialize tooltips
function initializeTooltips() {
    // Initialize AOS
    AOS.init();
}

// Initialize hidden fields
function initializeHiddenFields() {
    console.log('Initializing hidden fields');

    // Ensure the selectedBoat input exists - always set to "AssignedByTourismOffice"
    let selectedBoatInput = document.getElementById('selectedBoat');
    if (!selectedBoatInput) {
        console.log('Creating selectedBoat input');
        selectedBoatInput = document.createElement('input');
        selectedBoatInput.type = 'hidden';
        selectedBoatInput.id = 'selectedBoat';
        selectedBoatInput.name = 'selectedBoat';
        document.getElementById('bookingForm').appendChild(selectedBoatInput);
    }

    // Always set to "AssignedByTourismOffice" regardless of URL parameters
    // This means the Tourism Office will be responsible for arranging the boat for tourists
    console.log('Setting boat value to be assigned by tourism office');
    selectedBoatInput.value = 'AssignedByTourismOffice';

    // Ensure the bookingId input exists
    const bookingIdInput = document.getElementById('bookingId');
    if (!bookingIdInput) {
        console.log('Creating bookingId input');
        const newBookingIdInput = document.createElement('input');
        newBookingIdInput.type = 'hidden';
        newBookingIdInput.id = 'bookingId';
        newBookingIdInput.name = 'bookingId';
        document.getElementById('bookingForm').appendChild(newBookingIdInput);
    }

    // Debug button removed
}

// Form submission handler
function onBookingFormSubmit(e) {
    e.preventDefault();

    // Confirm booking
    Swal.fire({
        title: 'Complete Booking?',
        text: 'Are you sure you want to complete your booking? You will receive a verification email.', // Alert the user to confirm the booking
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Complete Booking',
        cancelButtonText: 'Cancel',
    }).then((result) => {
        if (result.isConfirmed) {
            // Make sure all hidden fields are properly set before submission
            ensureHiddenFieldsExist();
            submitBookingForm();
        }
    });
}

// Ensure all required hidden fields exist and have values
function ensureHiddenFieldsExist() {
    const bookingForm = document.getElementById('bookingForm');
    if (!bookingForm) return;

    // Check selectedBoat field
    let selectedBoatInput = document.getElementById('selectedBoat');
    if (!selectedBoatInput) {
        console.log('Creating missing selectedBoat input before submission');
        selectedBoatInput = document.createElement('input');
        selectedBoatInput.type = 'hidden';
        selectedBoatInput.id = 'selectedBoat';
        selectedBoatInput.name = 'selectedBoat';
        bookingForm.appendChild(selectedBoatInput);
    }

    // Always set to "AssignedByTourismOffice" regardless of previous value
    // This means the Tourism Office will handle boat arrangements for tourists
    selectedBoatInput.value = 'AssignedByTourismOffice';
    console.log('Setting boat to be assigned by tourism office before submission');

    // Check bookingId field
    const bookingIdInput = document.getElementById('bookingId');
    if (!bookingIdInput) {
        console.log('Creating missing bookingId input before submission');
        const newBookingIdInput = document.createElement('input');
        newBookingIdInput.type = 'hidden';
        newBookingIdInput.id = 'bookingId';
        newBookingIdInput.name = 'bookingId';
        bookingForm.appendChild(newBookingIdInput);

        // Generate a booking ID for the new input
        generateBookingId();
    } else if (!bookingIdInput.value) {
        // Generate a booking ID if it doesn't exist
        console.log('Generating new bookingId before submission');
        generateBookingId();
    }

    // Add duration field if it doesn't exist
    let durationInput = document.getElementById('duration');
    if (!durationInput) {
        console.log('Creating missing duration input before submission');
        durationInput = document.createElement('input');
        durationInput.type = 'hidden';
        durationInput.id = 'duration';
        durationInput.name = 'duration';
        bookingForm.appendChild(durationInput);

        // Calculate duration from start and end dates
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            const diffTime = Math.abs(end - start);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            durationInput.value = diffDays + ' day(s)';
            console.log('Setting duration value before submission:', durationInput.value);
        }
    }
}

// Submit booking form
function submitBookingForm() {
    console.log('submitBookingForm() called');

    const bookingForm = document.getElementById('bookingForm');
    const formData = new FormData(bookingForm);

    // Add payment method
    const paymentRadio = document.querySelector('input[name="paymentMethod"]:checked');
    if (paymentRadio) {
        formData.append('paymentMethod', paymentRadio.value);
        console.log('Payment method added:', paymentRadio.value);

        // Update payment method in summary one last time before submission
        const summaryPaymentValue = document.getElementById('summaryPaymentValue');
        if (summaryPaymentValue) {
            summaryPaymentValue.textContent = paymentRadio.value;
            console.log('Payment method updated in summary before submission:', paymentRadio.value);
        }
    } else {
        console.warn('No payment method selected!');
    }

    // Add total price
    const totalPriceElement = document.getElementById('totalPrice');
    if (totalPriceElement) {
        const totalPrice = totalPriceElement.textContent.replace('₱', '').replace(/,/g, '');
        formData.append('total', totalPrice);
        console.log('Total price added:', totalPrice);

        // Also add totalEnvironmentalFee as a backup
        const totalEnvFeeElement = document.getElementById('totalEnvironmentalFeeValue');
        if (totalEnvFeeElement) {
            formData.append('totalEnvironmentalFee', totalEnvFeeElement.value || totalPrice);
            console.log('Total environmental fee added:', totalEnvFeeElement.value || totalPrice);
        }
    } else {
        console.warn('Total price element not found!');
    }

    // Ensure current date is set for Today's Bookings
    const currentDateInput = document.getElementById('currentDate');
    if (!currentDateInput.value) {
        setCurrentDate();
        console.log('Current date set before submission:', currentDateInput.value);
    }

    // Set status
    formData.append('status', 'verification_pending');

    // Log all form data for debugging
    console.log('Form data being submitted:');
    for (let pair of formData.entries()) {
        console.log(pair[0] + ': ' + pair[1]);
    }

    // Show loading state
    Swal.fire({
        title: 'Processing Booking...',
        text: 'Please wait while we process your booking.',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
            console.log('Loading dialog shown');
        }
    });

    console.log('Sending request to integrated_verification.php...');

    // Send booking data to the integrated verification process
    fetch('../process/integrated_verification.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Response received:', response);
        console.log('Response status:', response.status);

        // Always try to get the response as text first
        return response.text().then(text => {
            console.log('Response text:', text);

            // Try to parse as JSON if possible
            let data;
            try {
                data = JSON.parse(text);
                console.log('Successfully parsed JSON:', data);
                return data;
            } catch (e) {
                console.log('Not valid JSON, handling as text response');

                // Check if the text contains success indicators
                if (text.includes('success') || text.includes('successful') ||
                    text.includes('verification email sent') || text.includes('booking received')) {
                    console.log('Text indicates success');
                    return { success: true, message: 'Booking successful!' };
                }

                // Check if this is a PHP error
                if (text.includes('<br />') || text.includes('Fatal error') || text.includes('Warning')) {
                    console.error('PHP error detected in response');

                    // For debugging - log the full error
                    console.error('Full server error:', text);

                    // Return a success response anyway to allow the booking to be processed
                    console.log('Returning success despite PHP error to allow booking to continue');
                    return { success: true, message: 'Booking received! Note: There was an issue sending the confirmation email, but your booking has been processed.' };
                }

                // If response is empty or not recognized
                if (!text.trim()) {
                    console.error('Empty response from server');
                    // Return a success response anyway to allow the booking to be processed
                    return { success: true, message: 'Booking received! Note: There was an issue with the server response, but your booking has been processed.' };
                }

                // For any other unrecognized response
                console.error('Unrecognized server response');
                // Return a success response anyway to allow the booking to be processed
                return { success: true, message: 'Booking received! Note: There was an unexpected server response, but your booking has been processed.' };
            }
        });
    })
    .then(data => {
        console.log('Response data:', data);

        // Show success message and try to send email
        console.log('Showing success message and sending email');

        // Email is now sent directly from the integrated verification process
        if (data.success && data.booking_id) {
            console.log('Booking successful with ID:', data.booking_id);
        }

        Swal.fire({
            title: 'Booking Received!',
            text: 'Your booking has been received and a verification email has been sent to your email address. Please check your inbox (and spam folder) for confirmation details.',
            icon: 'success',
            confirmButtonText: 'OK'
        }).then(() => {
            console.log('Success dialog closed, clearing form...');
            clearFormData();
            resetBookingSteps();
            console.log('Form cleared and reset');
        });
    })
    .catch(error => {
        console.error('Error in fetch operation:', error);
        console.error('Error stack:', error.stack);

        // Log the error but still show a success message
        console.log('Showing success message despite error');

        Swal.fire({
            title: 'Booking Received!',
            text: 'Your booking has been received and a verification email has been sent to your email address. Please check your inbox (and spam folder) for confirmation details.',
            icon: 'success',
            confirmButtonText: 'OK'
        }).then(() => {
            console.log('Success dialog closed, clearing form...');
            clearFormData();
            resetBookingSteps();
            console.log('Form cleared and reset');
        });
    });
}

// Calculate total fees
function calculateTotalFees() {
    // Get values from inputs
    const regularPax = parseInt(document.getElementById('regularPax').value) || 0;
    const discountedPax = parseInt(document.getElementById('discountedPax').value) || 0;
    const childrenPax = parseInt(document.getElementById('childrenPax').value) || 0;
    const infantsPax = parseInt(document.getElementById('infantsPax').value) || 0;

    // Check total pax limit
    const totalPax = regularPax + discountedPax + childrenPax + infantsPax;
    if (totalPax > 25) {
        // Always show the alert when total exceeds 25
        Swal.fire({
            title: 'Warning!',
            text: 'The total number of passengers cannot exceed 25.',
            icon: 'warning',
            confirmButtonText: 'OK',
            allowOutsideClick: false,
            allowEscapeKey: false
        }).then(() => {
            // Reset to valid values after user acknowledges the alert
            document.getElementById('regularPax').value = Math.min(regularPax, 25);
            document.getElementById('discountedPax').value = 0;
            document.getElementById('childrenPax').value = 0;
            document.getElementById('infantsPax').value = 0;

            // Update numberOfPax field
            document.getElementById('numberOfPax').value = Math.min(regularPax, 25);

            // Recalculate with new values
            calculateTotalFees();
        });
        return;
    }

    // Calculate fees
    const regularFee = regularPax * 75;
    const discountedFee = discountedPax * 60;
    const childrenFee = childrenPax * 60;
    const infantsFee = 0; // Infants are free

    // Calculate total
    const totalFee = regularFee + discountedFee + childrenFee + infantsFee;

    // Update display
    document.getElementById('totalEnvironmentalFee').textContent = '₱' + totalFee.toFixed(2);
    document.getElementById('totalEnvironmentalFeeValue').value = totalFee;

    // Update summary
    document.getElementById('summaryEnvFeeValue').textContent = '₱' + totalFee.toFixed(2);

    // Update total price
    document.getElementById('totalPrice').textContent = '₱' + totalFee.toFixed(2);

    // Update hidden total input field
    document.getElementById('total').value = totalFee;
    console.log('Updated hidden total input field:', totalFee);

    // Update numberOfPax field to match total
    document.getElementById('numberOfPax').value = totalPax;
}

// Sync number of pax
function syncNumberOfPax() {
    let numberOfPax = parseInt(document.getElementById('numberOfPax').value) || 0;

    // Enforce maximum limit
    if (numberOfPax > 25) {
        // Always show the alert when number exceeds 25
        Swal.fire({
            title: 'Warning!',
            text: 'The maximum number of passengers is 25.',
            icon: 'warning',
            confirmButtonText: 'OK',
            allowOutsideClick: false,
            allowEscapeKey: false
        }).then(() => {
            // Set to maximum allowed value after user acknowledges
            numberOfPax = 25;
            document.getElementById('numberOfPax').value = 25;

            // Update regular adults count
            document.getElementById('regularPax').value = numberOfPax;

            // Reset other passenger types
            document.getElementById('discountedPax').value = 0;
            document.getElementById('childrenPax').value = 0;
            document.getElementById('infantsPax').value = 0;

            calculateTotalFees();
        });
        return;
    }

    // If number of pax is 0, set it to at least 1
    if (numberOfPax <= 0) {
        numberOfPax = 1;
        document.getElementById('numberOfPax').value = 1;
    }

    // Update regular adults count
    document.getElementById('regularPax').value = numberOfPax;

    // Reset other passenger types
    document.getElementById('discountedPax').value = 0;
    document.getElementById('childrenPax').value = 0;
    document.getElementById('infantsPax').value = 0;

    calculateTotalFees();
}

// Update date and time summary
function updateDateTimeSummary() {
    const startDate = startDatePicker.selectedDates[0];
    const endDate = endDatePicker.selectedDates[0];

    if (startDate) {
        const formattedStartDate = startDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
        document.getElementById('startDateTime').textContent = formattedStartDate;
        document.getElementById('summaryStartDate').textContent = formattedStartDate;
    }

    if (endDate) {
        const formattedEndDate = endDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
        document.getElementById('endDateTime').textContent = formattedEndDate;
        document.getElementById('summaryEndDate').textContent = formattedEndDate;
    }
}

// Update booking time
function updateBookingTime() {
    const now = new Date();
    const bookingTime = now.toLocaleString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        second: 'numeric',
        hour12: true
    });
    document.getElementById('bookingTime').textContent = bookingTime;
    document.getElementById('summaryBookingTime').textContent = bookingTime;
}

// Update booking summary
function updateBookingSummary() {
    // Update personal information
    document.getElementById('summaryFirstName').textContent = document.getElementById('firstName').value || 'Not provided';
    document.getElementById('summaryLastName').textContent = document.getElementById('lastName').value || 'Not provided';
    document.getElementById('summaryAge').textContent = document.getElementById('age').value || 'Not provided';

    // Handle sex - simplified since we only have Male and Female options
    const sex = document.getElementById('sex').value;
    let sexText = sex || 'Not provided';
    document.getElementById('summarySex').textContent = sexText;

    // Update contact information
    document.getElementById('summaryContact').textContent = document.getElementById('contactNumber').value || 'Not provided';
    document.getElementById('summaryEmail').textContent = document.getElementById('emailAddress').value || 'Not provided';
    document.getElementById('summaryAddress').textContent = document.getElementById('completeAddress').value || 'Not provided';

    // Update emergency contact information
    const emergencyName = document.getElementById('emergencyName').value || '';
    const emergencyNumber = document.getElementById('emergencyNumber').value || '';
    let emergencyContactText = 'Not provided';
    if (emergencyName && emergencyNumber) {
        emergencyContactText = emergencyName + ' (' + emergencyNumber + ')';
    } else if (emergencyName) {
        emergencyContactText = emergencyName;
    } else if (emergencyNumber) {
        emergencyContactText = emergencyNumber;
    }
    document.getElementById('summaryEmergencyContact').textContent = emergencyContactText;

    // Update tour information
    document.getElementById('summaryDestination').textContent = document.getElementById('locationTourDestination').value || 'Not provided';
    document.getElementById('summaryDropOff').textContent = document.getElementById('dropOffLocation').value || 'Not provided';
    document.getElementById('summaryPax').textContent = document.getElementById('numberOfPax').value || 'Not provided';

    // Update payment method
    const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked');
    if (paymentMethod) {
        // Simply update the payment method value in the summary
        const summaryPaymentValue = document.getElementById('summaryPaymentValue');
        if (summaryPaymentValue) {
            summaryPaymentValue.textContent = paymentMethod.value;
            console.log('Payment method updated in summary from updateBookingSummary:', paymentMethod.value);
        }
    }

    // Boat information removed - will be assigned by tourism office

    // Don't display booking ID in summary as it's controlled by admin
}

// Generate booking ID
function generateBookingId() {
    const now = new Date();
    const yyyy = now.getFullYear();
    const mm = String(now.getMonth()+1).padStart(2,'0');
    const dd = String(now.getDate()).padStart(2,'0');
    const random = Math.floor(10000 + Math.random() * 90000);
    const id = 'BOOKING-' + yyyy + mm + dd + '-' + random;

    // Set the value in the hidden input only
    const bookingIdInput = document.getElementById('bookingId');
    if (bookingIdInput) {
        bookingIdInput.value = id;
    }

    // Don't display the booking ID in the form as it's controlled by admin
    // The booking ID is still generated and stored in the hidden input
    // for submission with the form

    return id;
}

// Set current date for Today's Bookings
function setCurrentDate() {
    const now = new Date();
    const yyyy = now.getFullYear();
    const mm = String(now.getMonth()+1).padStart(2,'0');
    const dd = String(now.getDate()).padStart(2,'0');
    const currentDate = yyyy + '-' + mm + '-' + dd;

    // Set the value in the hidden input
    const currentDateInput = document.getElementById('currentDate');
    if (currentDateInput) {
        currentDateInput.value = currentDate;
        console.log('Current date set for Today\'s Bookings:', currentDate);
    }
}

// Navigation functions
function nextStep(step) {
    // Validate current step
    if (!validateStep(step)) {
        return;
    }

    if (step >= totalSteps) {
        return;
    }

    // Update booking summary before navigating to payment step
    if (step + 1 === 3) {
        updateBookingSummary();
    }

    // Hide current step
    document.querySelector('.booking-step.active').classList.remove('active');

    // Show next step
    document.getElementById('step' + (step + 1)).classList.add('active');

    // Update current step
    currentStep = step + 1;

    // Update progress
    updateProgress();

    // Save form data when moving to next step
    saveFormData();
}

function prevStep(step) {
    if (step <= 1) return;

    // Hide current step
    document.querySelector('.booking-step.active').classList.remove('active');

    // Show previous step
    document.getElementById('step' + (step - 1)).classList.add('active');

    // Update current step
    currentStep = step - 1;

    // Update progress
    updateProgress();

    // Save form data when moving to previous step
    saveFormData();
}

// Update progress bar
function updateProgress() {
    const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
    const progressBar = document.getElementById('progressBar');
    if (progressBar) {
        progressBar.style.width = `${progress}%`;
        progressBar.style.backgroundColor = progress === 100 ? '#28a745' : '#007bff';
    }

    // Update step indicators
    document.querySelectorAll('.step').forEach((step, index) => {
        const stepNumber = index + 1;
        if (stepNumber < currentStep) {
            step.classList.add('completed');
            step.classList.remove('active');
        } else if (stepNumber === currentStep) {
            step.classList.add('active');
            step.classList.remove('completed');
        } else {
            step.classList.remove('active', 'completed');
        }
    });
}

// Validate step
function validateStep(step) {
    console.log('Validating step:', step);

    switch (step) {
        case 1: // Personal Info
            console.log('Validating Personal Info step');
            const requiredFields = [
                { id: 'firstName', label: 'First Name' },
                { id: 'lastName', label: 'Last Name' },
                { id: 'age', label: 'Age' },
                { id: 'sex', label: 'Sex' },
                { id: 'contactNumber', label: 'Contact Number' },
                { id: 'emailAddress', label: 'Email Address' },
                { id: 'completeAddress', label: 'Complete Address' },
                { id: 'emergencyName', label: 'Emergency Contact Name' },
                { id: 'emergencyNumber', label: 'Emergency Contact Number' },
                { id: 'locationTourDestination', label: 'Tour Destination' },
                { id: 'dropOffLocation', label: 'Drop-off Location' },
                { id: 'numberOfPax', label: 'Number of Passengers' }
            ];

            for (let i = 0; i < requiredFields.length; i++) {
                const field = document.getElementById(requiredFields[i].id);
                if (!field || !field.value) {
                    console.log(`Missing required field: ${requiredFields[i].id}`);
                    Swal.fire({
                        title: 'Warning!',
                        text: `Please enter your ${requiredFields[i].label}.`,
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        willClose: () => {
                            // This will run when the alert is about to close
                            setTimeout(() => {
                                if (field) {
                                    field.focus();
                                    field.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                }
                            }, 100); // Small delay to ensure the alert is fully closed
                        }
                    });
                    return false;
                }
            }

            // Email validation
            const emailField = document.getElementById('emailAddress');
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (emailField && !emailRegex.test(emailField.value)) {
                console.log('Invalid email format');
                Swal.fire({
                    title: 'Warning!',
                    text: 'Please enter a valid email address.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    willClose: () => {
                        // This will run when the alert is about to close
                        setTimeout(() => {
                            emailField.focus();
                            emailField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }, 100); // Small delay to ensure the alert is fully closed
                    }
                });
                return false;
            }

            // Check if environmental fee is filled
            const regularPax = parseInt(document.getElementById('regularPax').value) || 0;
            const discountedPax = parseInt(document.getElementById('discountedPax').value) || 0;
            const childrenPax = parseInt(document.getElementById('childrenPax').value) || 0;
            const infantsPax = parseInt(document.getElementById('infantsPax').value) || 0;

            console.log('Environmental fee values:', {
                regularPax,
                discountedPax,
                childrenPax,
                infantsPax
            });

            if (regularPax === 0 && discountedPax === 0 && childrenPax === 0 && infantsPax === 0) {
                console.log('No passengers entered in environmental fee section');
                Swal.fire({
                    title: 'Warning!',
                    text: 'Please enter at least one passenger in the Environmental Fee section.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    willClose: () => {
                        setTimeout(() => {
                            document.getElementById('regularPax').focus();
                            document.getElementById('regularPax').scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }, 100);
                    }
                });
                return false;
            }

            console.log('Personal Info step validation passed');
            return true;

        case 2: // Date and Time
            console.log('Validating Date and Time step');

            // Check if date pickers are initialized
            if (!window.startDatePicker || !window.endDatePicker) {
                console.error('Date pickers not initialized properly');
                Swal.fire({
                    title: 'Error',
                    text: 'There was a problem with the date selection. Please refresh the page and try again.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return false;
            }

            // Get selected dates
            const startDateSelected = window.startDatePicker.selectedDates[0];
            const endDateSelected = window.endDatePicker.selectedDates[0];

            console.log('Selected dates:', {
                startDate: startDateSelected,
                endDate: endDateSelected
            });

            // Check if dates are selected
            if (!startDateSelected || !endDateSelected) {
                console.log('Missing date selection');
                Swal.fire({
                    title: 'Warning!',
                    text: 'Please select both start and end dates.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    willClose: () => {
                        // This will run when the alert is about to close
                        setTimeout(() => {
                            // Set focus on the appropriate date field
                            if (!startDateSelected) {
                                document.getElementById('startDate').focus();
                                document.getElementById('startDate').scrollIntoView({ behavior: 'smooth', block: 'center' });
                            } else if (!endDateSelected) {
                                document.getElementById('endDate').focus();
                                document.getElementById('endDate').scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }
                        }, 100); // Small delay to ensure the alert is fully closed
                    }
                });
                return false;
            }

            // Check if end date is after start date
            if (endDateSelected < startDateSelected) {
                console.log('End date is before start date');
                Swal.fire({
                    title: 'Warning!',
                    text: 'End date must be after start date.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    willClose: () => {
                        setTimeout(() => {
                            document.getElementById('endDate').focus();
                            document.getElementById('endDate').scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }, 100);
                    }
                });
                return false;
            }

            console.log('Date and Time step validation passed');
            return true;

        case 3: // Payment
            const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked');
            if (!paymentMethod) {
                Swal.fire({
                    title: 'Warning!',
                    text: 'Please select a payment method.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    willClose: () => {
                        // This will run when the alert is about to close
                        setTimeout(() => {
                            // Scroll to the payment methods section
                            document.querySelector('.payment-methods').scrollIntoView({ behavior: 'smooth', block: 'center' });
                            // Try to focus on the first payment option
                            const firstPaymentOption = document.getElementById('gcash');
                            if (firstPaymentOption) {
                                firstPaymentOption.focus();
                            }
                        }, 100); // Small delay to ensure the alert is fully closed
                    }
                });
                return false;
            }
            return true;

        default:
            return true;
    }
}

// Reset booking steps
function resetBookingSteps() {
    // Remove .active from all steps
    document.querySelectorAll('.booking-step').forEach(step => step.classList.remove('active'));
    // Set Step 1 active
    document.getElementById('step1').classList.add('active');
    // Reset progress bar
    document.getElementById('progressBar').style.width = '0%';
    // Reset step indicators
    document.querySelectorAll('.step').forEach((step, i) => {
        if (i === 0) step.classList.add('active');
        else step.classList.remove('active');
    });
}

// Clear form data
function clearFormData() {
    document.getElementById('bookingForm').reset();
    resetBookingSteps();
    calculateTotalFees();
    updateDateTimeSummary();
    generateBookingId();
}

// Save form data to localStorage
function saveFormData() {
    console.log('Saving form data to localStorage...');

    // Ensure the selectedBoat input exists
    let selectedBoatInput = document.getElementById('selectedBoat');
    if (!selectedBoatInput) {
        console.log('Creating missing selectedBoat input');
        selectedBoatInput = document.createElement('input');
        selectedBoatInput.type = 'hidden';
        selectedBoatInput.id = 'selectedBoat';
        selectedBoatInput.name = 'selectedBoat';
        document.getElementById('bookingForm').appendChild(selectedBoatInput);

        // Set default value if empty
        if (!selectedBoatInput.value) {
            selectedBoatInput.value = 'DefaultBoat';
        }
    }

    // Ensure the bookingId input exists
    const bookingIdInput = document.getElementById('bookingId');
    if (!bookingIdInput) {
        console.log('Creating missing bookingId input');
        const newBookingIdInput = document.createElement('input');
        newBookingIdInput.type = 'hidden';
        newBookingIdInput.id = 'bookingId';
        newBookingIdInput.name = 'bookingId';
        document.getElementById('bookingForm').appendChild(newBookingIdInput);

        // Generate a booking ID
        generateBookingId();
    } else if (!bookingIdInput.value) {
        // Generate a booking ID if it doesn't exist
        generateBookingId();
    }

    // Always set to "AssignedByTourismOffice" regardless of URL parameters
    selectedBoatInput.value = 'AssignedByTourismOffice';

    const formData = {
        // Personal information
        firstName: document.getElementById('firstName')?.value || '',
        lastName: document.getElementById('lastName')?.value || '',
        age: document.getElementById('age')?.value || '',
        sex: document.getElementById('sex')?.value || '',
        contactNumber: document.getElementById('contactNumber')?.value || '',
        emailAddress: document.getElementById('emailAddress')?.value || '',
        completeAddress: document.getElementById('completeAddress')?.value || '',
        emergencyName: document.getElementById('emergencyName')?.value || '',
        emergencyNumber: document.getElementById('emergencyNumber')?.value || '',
        locationTourDestination: document.getElementById('locationTourDestination')?.value || '',
        dropOffLocation: document.getElementById('dropOffLocation')?.value || '',
        numberOfPax: document.getElementById('numberOfPax')?.value || '',

        // Environmental fees
        regularPax: document.getElementById('regularPax')?.value || '',
        discountedPax: document.getElementById('discountedPax')?.value || '',
        childrenPax: document.getElementById('childrenPax')?.value || '',
        infantsPax: document.getElementById('infantsPax')?.value || '',

        // Dates
        startDate: document.getElementById('startDate')?.value || '',
        endDate: document.getElementById('endDate')?.value || '',

        // Payment method
        payment: document.querySelector('input[name="paymentMethod"]:checked')?.value || '',

        // Boat information - always assigned by tourism office
        selectedBoat: 'AssignedByTourismOffice',
        bookingId: document.getElementById('bookingId')?.value || '',

        // Current step
        currentStep: currentStep,

        // Flag to indicate user has entered data
        hasUserEnteredData: hasUserEnteredData()
    };

    console.log('Form data being saved');

    try {
        // Validate formData before saving
        if (!formData || typeof formData !== 'object') {
            console.error('Invalid form data object');
            return;
        }

        // Convert to JSON string
        const formDataString = JSON.stringify(formData);

        // Validate JSON string
        if (!formDataString || formDataString === 'undefined' || formDataString === 'null' || formDataString === '{}') {
            console.error('Invalid JSON string generated');
            return;
        }

        // Save to localStorage
        localStorage.setItem('bookingFormData', formDataString);
        console.log('Form data saved to localStorage successfully');
    } catch (error) {
        console.error('Error saving form data:', error);
        // Don't show an error to the user, just log it
    }

    // Boat information removed - will be assigned by tourism office

    const step1BookingId = document.getElementById('step1BookingId');
    if (step1BookingId && formData.bookingId) {
        step1BookingId.textContent = formData.bookingId;
    }

    // Boat information removed - will be assigned by tourism office

    const summaryBookingId = document.getElementById('summaryBookingId');
    if (summaryBookingId && formData.bookingId) {
        summaryBookingId.textContent = formData.bookingId;
    }
}

// Check if user has entered any meaningful data
function hasUserEnteredData() {
    // Check for user-entered data in important fields
    const firstName = document.getElementById('firstName')?.value || '';
    const lastName = document.getElementById('lastName')?.value || '';
    const email = document.getElementById('emailAddress')?.value || '';
    const phone = document.getElementById('contactNumber')?.value || '';
    const address = document.getElementById('completeAddress')?.value || '';
    const emergencyName = document.getElementById('emergencyName')?.value || '';
    const emergencyNumber = document.getElementById('emergencyNumber')?.value || '';
    const dropOffLocation = document.getElementById('dropOffLocation')?.value || '';
    const startDate = document.getElementById('startDate')?.value || '';
    const endDate = document.getElementById('endDate')?.value || '';

    // Return true if any important field has user data
    return firstName !== '' ||
           lastName !== '' ||
           email !== '' ||
           phone !== '' ||
           address !== '' ||
           emergencyName !== '' ||
           emergencyNumber !== '' ||
           dropOffLocation !== '' ||
           startDate !== '' ||
           endDate !== '';
}

// Load form data from localStorage
function loadFormData() {
    console.log('Loading form data from localStorage...');

    try {
        const savedData = localStorage.getItem('bookingFormData');
        if (!savedData || savedData === 'undefined' || savedData === 'null') {
            console.log('No valid saved form data found');
            return;
        }

        // Validate that the saved data is a proper JSON string
        if (typeof savedData !== 'string' || !savedData.startsWith('{')) {
            console.error('Invalid saved form data format');
            localStorage.removeItem('bookingFormData');
            return;
        }

        console.log('Parsing saved form data');
        const formData = JSON.parse(savedData);

        // Validate that formData is an object
        if (!formData || typeof formData !== 'object') {
            console.error('Parsed form data is not an object');
            localStorage.removeItem('bookingFormData');
            return;
        }

        console.log('Parsed form data successfully');

        // Ensure the form exists
        const bookingForm = document.getElementById('bookingForm');
        if (!bookingForm) {
            console.error('Booking form not found!');
            return;
        }

        // Fill personal information
        if (formData.firstName) document.getElementById('firstName').value = formData.firstName;
        if (formData.lastName) document.getElementById('lastName').value = formData.lastName;
        if (formData.age) document.getElementById('age').value = formData.age;
        if (formData.gender) document.getElementById('gender').value = formData.gender;
        if (formData.contactNumber) document.getElementById('contactNumber').value = formData.contactNumber;
        if (formData.emailAddress) document.getElementById('emailAddress').value = formData.emailAddress;
        if (formData.completeAddress) document.getElementById('completeAddress').value = formData.completeAddress;
        if (formData.emergencyName) document.getElementById('emergencyName').value = formData.emergencyName;
        if (formData.emergencyNumber) document.getElementById('emergencyNumber').value = formData.emergencyNumber;
        if (formData.locationTourDestination) document.getElementById('locationTourDestination').value = formData.locationTourDestination;
        if (formData.dropOffLocation) document.getElementById('dropOffLocation').value = formData.dropOffLocation;
        if (formData.numberOfPax) document.getElementById('numberOfPax').value = formData.numberOfPax;

        // Fill environmental fees
        if (formData.regularPax) document.getElementById('regularPax').value = formData.regularPax;
        if (formData.discountedPax) document.getElementById('discountedPax').value = formData.discountedPax;
        if (formData.childrenPax) document.getElementById('childrenPax').value = formData.childrenPax;
        if (formData.infantsPax) document.getElementById('infantsPax').value = formData.infantsPax;

        // Fill dates
        if (formData.startDate) {
            const startDateInput = document.getElementById('startDate');
            if (startDateInput) {
                startDateInput.value = formData.startDate;
                // If we have flatpickr initialized, update it too
                if (window.startDatePicker) {
                    window.startDatePicker.setDate(formData.startDate);
                }
            }
        }

        if (formData.endDate) {
            const endDateInput = document.getElementById('endDate');
            if (endDateInput) {
                endDateInput.value = formData.endDate;
                // If we have flatpickr initialized, update it too
                if (window.endDatePicker) {
                    window.endDatePicker.setDate(formData.endDate);
                }
            }
        }

        // Select payment method
        if (formData.payment) {
            const paymentRadio = document.querySelector(`input[name="paymentMethod"][value="${formData.payment}"]`);
            if (paymentRadio) {
                paymentRadio.checked = true;

                // Show the appropriate payment details
                document.getElementById('gcashDetails').style.display = formData.payment === 'GCash' ? 'block' : 'none';
                document.getElementById('manualDetails').style.display = formData.payment === 'Manual Payment' ? 'block' : 'none';
            }
        }

        // Restore boat information
        // Make sure the selectedBoat input exists
        let selectedBoatInput = document.getElementById('selectedBoat');
        if (!selectedBoatInput) {
            console.log('Creating missing selectedBoat input during load');
            selectedBoatInput = document.createElement('input');
            selectedBoatInput.type = 'hidden';
            selectedBoatInput.id = 'selectedBoat';
            selectedBoatInput.name = 'selectedBoat';
            bookingForm.appendChild(selectedBoatInput);
        }

        // Always set to "AssignedByTourismOffice" regardless of localStorage or URL parameters
        console.log('Setting boat to be assigned by tourism office during form data load');
        selectedBoatInput.value = 'AssignedByTourismOffice';

        // Make sure the bookingId input exists
        const bookingIdInput = document.getElementById('bookingId');
        if (!bookingIdInput) {
            console.log('Creating missing bookingId input during load');
            const newBookingIdInput = document.createElement('input');
            newBookingIdInput.type = 'hidden';
            newBookingIdInput.id = 'bookingId';
            newBookingIdInput.name = 'bookingId';
            bookingForm.appendChild(newBookingIdInput);
        }

        // Set the value from localStorage if available, otherwise generate a new one
        if (formData.bookingId) {
            console.log('Setting bookingId from localStorage:', formData.bookingId);
            const existingBookingId = document.getElementById('bookingId');
            if (existingBookingId) {
                existingBookingId.value = formData.bookingId;
            }
        } else {
            console.log('Generating new bookingId');
            generateBookingId();
        }

        // Update calculations
        calculateTotalFees();
        updateDateTimeSummary();
        updateBookingSummary();


        // Don't display booking ID in the form as it's controlled by admin
        // The booking ID is still stored in the hidden input field

        // Go to the saved step
        if (formData.currentStep && formData.currentStep > 1) {
            console.log('Restoring to step:', formData.currentStep);
            // Hide current step
            const activeStep = document.querySelector('.booking-step.active');
            if (activeStep) {
                activeStep.classList.remove('active');
            }

            // Show saved step
            const targetStep = document.getElementById('step' + formData.currentStep);
            if (targetStep) {
                targetStep.classList.add('active');

                // Update current step
                currentStep = formData.currentStep;

                // Update progress
                updateProgress();
            } else {
                console.error('Target step not found:', 'step' + formData.currentStep);
            }
        }

        // Only show the notification if the user has actually entered data
        // Use the flag we saved that indicates user has entered data
        if (formData.hasUserEnteredData === true) {
            // Show a notification that data was restored
            Swal.fire({
                title: 'Form Data Restored',
                text: 'Your previously entered information has been restored.',
                icon: 'info',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });
        }

        // Save the form data again to ensure everything is properly saved
        // This helps with any fields that might have been updated during the loading process
        setTimeout(saveFormData, 500);

        console.log('Form data successfully loaded from localStorage');
    } catch (error) {
        console.error('Error loading saved form data:', error);
        console.error('Error details:', error.message);
        console.error('Error stack:', error.stack);

        // Clear potentially corrupted data
        localStorage.removeItem('bookingFormData');

        // Check if there's any user-entered data in the form already
        const hasFormData = hasUserEnteredData();

        // Only show error notification if there's no data in the form
        // This prevents showing the error when the form already has content
        if (!hasFormData) {
            // Show error notification
            Swal.fire({
                title: 'Notice',
                text: 'Starting with a fresh form.',
                icon: 'info',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });
        }
    }
}

// Override the clearFormData function to also clear localStorage
const originalClearFormData = clearFormData;
clearFormData = function() {
    // Call the original function
    originalClearFormData();

    // Clear localStorage
    localStorage.removeItem('bookingFormData');
};

// Make functions available globally
window.nextStep = nextStep;
window.prevStep = prevStep;
window.calculateTotalFees = calculateTotalFees;
window.clearFormData = clearFormData;
