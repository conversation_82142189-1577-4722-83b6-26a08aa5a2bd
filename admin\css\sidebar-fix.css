/* Completely hide all scrollbars */
/* For all elements */
* {
    -ms-overflow-style: none !important;  /* IE and Edge */
    scrollbar-width: none !important;  /* Firefox */
}

/* For WebKit browsers (Chrome, Safari) */
*::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

/* Sidebar fixes - completely remove scrollbar */
.main-sidebar {
    height: 100vh !important;
    min-height: 100% !important;
    position: fixed !important;
    overflow: hidden !important;
    padding-bottom: 0 !important;
}

/* Make nav menu fit without scrollbar */
.nav-sidebar {
    overflow: hidden !important;
}

/* Adjust sidebar height to fit content */
.sidebar-mini .main-sidebar,
.sidebar-mini-md .main-sidebar,
.sidebar-mini-xs .main-sidebar {
    overflow: hidden !important;
}

.sidebar {
    padding-bottom: 0 !important;
    overflow: hidden !important;
}

/* Adjust nav items to fit without scrollbar */
.nav-sidebar .nav-item {
    margin-bottom: 0 !important;
}

/* Adjust padding for better readability */
.nav-sidebar .nav-link {
    padding: 0.7rem 1rem !important;
    font-size: 1.05rem !important;
}

/* Fix content wrapper to adjust for sidebar */
.content-wrapper {
    min-height: calc(100vh - 70px) !important;
    overflow: hidden !important;
}

/* Fix footer position */
.main-footer {
    margin-left: 250px;
}

@media (max-width: 991.98px) {
    .main-footer {
        margin-left: 0;
    }
}

/* Fix for sidebar collapse */
body.sidebar-collapse .main-footer {
    margin-left: 4.6rem;
}

@media (max-width: 991.98px) {
    body.sidebar-collapse .main-footer {
        margin-left: 0;
    }
}
