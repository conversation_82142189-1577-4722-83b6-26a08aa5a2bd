<?php
echo "<h2>Background Image Debug</h2>";

$user_role = 'admin';
$bg_image_path = 'images/' . $user_role . '-background.jpg';

echo "User role: " . $user_role . "<br>";
echo "Background path: " . $bg_image_path . "<br>";
echo "File exists: " . (file_exists($bg_image_path) ? 'YES' : 'NO') . "<br>";
echo "Full path: " . realpath($bg_image_path) . "<br>";
echo "Current directory: " . getcwd() . "<br>";

echo "<h3>Files in images directory:</h3>";
if (is_dir('images')) {
    $files = scandir('images');
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            echo $file . "<br>";
        }
    }
} else {
    echo "Images directory not found<br>";
}

echo "<h3>Test Image Display:</h3>";
if (file_exists($bg_image_path)) {
    echo '<img src="' . $bg_image_path . '" style="max-width: 200px;" alt="Background"><br>';
} else {
    echo "Cannot display image - file not found<br>";
}
?>
