<?php
session_start();
include('includes/config.php');

// Check if admin is logged in
if(!isset($_SESSION['aid']) || strlen($_SESSION['aid'])==0) {
    header('location:index.php');
} else {
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Pending Bookings | Admin Dashboard</title>
  <!-- Bootstrap CSS (CDN) -->
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
  <!-- FontAwesome (optional, CDN) -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <!-- AdminLTE CSS (keep local if available) -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
  <!-- DataTables -->
  <link rel="stylesheet" href="plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <!-- Navbar -->
<?php include_once("includes/navbar.php");?>
  <!-- /.navbar -->

 <?php include_once("includes/sidebar.php");?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>Pending Bookings</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
              <li class="breadcrumb-item active">Pending Bookings</li>
            </ol>
          </div>
        </div>
      </div><!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header bg-warning">
                <h3 class="card-title"><i class="fas fa-clock"></i> Pending Bookings List</h3>
                <div class="card-tools">
                  <button type="button" class="btn btn-tool" onclick="document.querySelector('.card-body').classList.toggle('collapse')">
                    <i class="fas fa-minus"></i>
                  </button>
                  <button type="button" class="btn btn-tool" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt"></i>
                  </button>
                </div>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                <table id="example1" class="table table-bordered table-striped">
                    <thead class="thead-dark">
                  <tr>
                    <th>#</th>
                    <th>Reference No</th>
                    <th>Customer Name</th>
                    <th>Email</th>
                    <th>Contact No</th>
                    <th>Boat</th>
                    <th>Destination</th>
                    <th>Date</th>
                    <th>Time</th>
                    <th>Total Amount</th>
                    <th>Status</th>
                    <th>Action</th>
                  </tr>
                    </thead>
                                <tbody>
                  <?php
                    // Query to fetch pending bookings with boat and destination details
                    $sql = "SELECT b.*, bt.name as boat_name, d.name as destination_name
                            FROM bookings b
                            LEFT JOIN boats bt ON b.boat_id = bt.boat_id
                            LEFT JOIN destinations d ON b.destination_id = d.destination_id
                            WHERE b.booking_status = 'pending'
                            ORDER BY b.created_at DESC";
                    $query = $con->query($sql);
                    if($query && $query->num_rows > 0) {
                        $cnt = 1;
                        while($row = $query->fetch_assoc()) {
                  ?>
                  <tr>
                    <td><?php echo $cnt++; ?></td>
                    <td><?php echo htmlspecialchars($row['booking_code'] ?? 'N/A'); ?></td>
                    <td><?php echo htmlspecialchars(($row['first_name'] ?? '') . ' ' . ($row['last_name'] ?? '')); ?></td>
                    <td><?php echo htmlspecialchars($row['email'] ?? 'N/A'); ?></td>
                    <td><?php echo htmlspecialchars($row['contact_number'] ?? 'N/A'); ?></td>
                    <td><?php echo htmlspecialchars($row['boat_name'] ?? 'N/A'); ?></td>
                    <td><?php echo htmlspecialchars($row['destination_name'] ?? 'N/A'); ?></td>
                    <td><?php echo $row['start_date'] ? date('M d, Y', strtotime($row['start_date'])) : 'N/A'; ?></td>
                    <td><?php echo $row['booking_time'] ? date('h:i A', strtotime($row['booking_time'])) : 'N/A'; ?></td>
                    <td>₱<?php echo number_format($row['total'] ?? 0, 2); ?></td>
                    <td><span class="badge badge-warning">Pending</span></td>
                    <td>
                      <button class="btn btn-info btn-sm" onclick="viewDetails(<?php echo $row['booking_id']; ?>)">
                        <i class="fas fa-eye"></i> View
                      </button>
                      <button class="btn btn-success btn-sm" onclick="acceptBooking(<?php echo $row['booking_id']; ?>)">
                        <i class="fas fa-check"></i> Accept
                      </button>
                      <button class="btn btn-danger btn-sm" onclick="rejectBooking(<?php echo $row['booking_id']; ?>)">
                        <i class="fas fa-times"></i> Reject
                      </button>
                    </td>
                  </tr>
                                    <?php 
                                        }
                                    } else {
                                    ?>
                  <tr>
                    <td colspan="12" class="text-center">No pending bookings found</td>
                  </tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                </div>
              </div>
              <!-- /.card-body -->
            </div>
            <!-- /.card -->
          </div>
          <!-- /.col -->
        </div>
        <!-- /.row -->
      </div>
      <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php include_once('includes/footer.php');?>

  <!-- Control Sidebar -->
  <aside class="control-sidebar control-sidebar-dark">
    <!-- Control sidebar content goes here -->
  </aside>
  <!-- /.control-sidebar -->
</div>

<!-- View Details Modal -->
<div class="modal fade" id="viewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Booking Details</h4>
            </div>
            <div class="modal-body" id="modalContent">
                <div class="text-center">
                    <i class="fa fa-spinner fa-spin fa-2x"></i>
                    <p>Loading...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- jQuery (CDN) -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<!-- Bootstrap JS (CDN) -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE JS (keep local if available) -->
<script src="dist/js/adminlte.min.js"></script>
<!-- DataTables  & Plugins (keep local if available) -->
<script src="plugins/datatables/jquery.dataTables.min.js"></script>
<script src="plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
<script src="plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
<script src="plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
<script src="plugins/jszip/jszip.min.js"></script>
<script src="plugins/pdfmake/pdfmake.min.js"></script>
<script src="plugins/pdfmake/vfs_fonts.js"></script>
<script src="plugins/datatables-buttons/js/buttons.html5.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.print.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.colVis.min.js"></script>
<!-- AdminLTE for demo purposes -->
<script src="dist/js/demo.js"></script>

<!-- Page specific script -->
<script>
$(function () {
    // Initialize DataTable
    $("#example1").DataTable({
      "responsive": true,
      "lengthChange": true,
      "autoWidth": false,
      "pageLength": 10,
      "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"]
    }).buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');
});
</script>

<script>
function viewDetails(bookingId) {
    console.log('View Details clicked for booking ID:', bookingId);
    
    // Show modal
    $('#viewModal').modal('show');
    
    // Load booking details
    $.ajax({
        url: 'get-booking-details.php',
        type: 'POST',
        data: { id: bookingId },
        dataType: 'json',
        success: function(response) {
            console.log('Response:', response);
            
            if(response.success && response.data) {
                var booking = response.data;
                var html = `
                    <div class="row">
                        <div class="col-md-6">
                            <h4>Customer Information</h4>
                            <p><strong>Name:</strong> ${booking.first_name} ${booking.last_name}</p>
                            <p><strong>Email:</strong> ${booking.email}</p>
                            <p><strong>Contact:</strong> ${booking.contact_number}</p>
                        </div>
                        <div class="col-md-6">
                            <h4>Booking Information</h4>
                            <p><strong>Booking Code:</strong> ${booking.booking_code}</p>
                            <p><strong>Total Amount:</strong> ₱${parseFloat(booking.total).toLocaleString()}</p>
                            <p><strong>Status:</strong> ${booking.booking_status}</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <h4>Trip Details</h4>
                            <p><strong>Start Date:</strong> ${booking.start_date || 'N/A'}</p>
                            <p><strong>End Date:</strong> ${booking.end_date || 'N/A'}</p>
                            <p><strong>Boat:</strong> ${booking.boat_name || 'N/A'}</p>
                            <p><strong>Destination:</strong> ${booking.destination_name || 'N/A'}</p>
                        </div>
                    </div>
                `;
                $('#modalContent').html(html);
            } else {
                $('#modalContent').html('<div class="alert alert-danger">Error loading booking details</div>');
            }
        },
        error: function() {
            $('#modalContent').html('<div class="alert alert-danger">Error loading booking details</div>');
        }
    });
}

function acceptBooking(bookingId) {
    if(confirm('Are you sure you want to accept this booking?')) {
        console.log('Accept booking:', bookingId);
        
        $.ajax({
            url: 'confirm-booking.php',
            type: 'POST',
            data: { 
                id: bookingId,
                action: 'confirm'
            },
            dataType: 'json',
            success: function(response) {
                if(response.success) {
                    alert('Booking accepted successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + (response.error || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error accepting booking');
            }
        });
    }
}

function rejectBooking(bookingId) {
    if(confirm('Are you sure you want to reject this booking?')) {
        console.log('Reject booking:', bookingId);
        
        $.ajax({
            url: 'confirm-booking.php',
            type: 'POST',
            data: { 
                id: bookingId,
                action: 'cancel'
            },
            dataType: 'json',
            success: function(response) {
                if(response.success) {
                    alert('Booking rejected successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + (response.error || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error rejecting booking');
            }
        });
    }
}
</script>

</body>
</html>
<?php } ?>
