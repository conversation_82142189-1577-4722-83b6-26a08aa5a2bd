<?php
// Test booking details functionality
require_once 'admin/includes/config.php';

try {
    echo "=== TESTING BOOKING DETAILS ===\n\n";
    
    // Check if there are any bookings
    $count_query = "SELECT COUNT(*) as total FROM bookings";
    $result = $con->query($count_query);
    $count = $result->fetch_assoc()['total'];
    echo "Total bookings in database: $count\n\n";
    
    if ($count == 0) {
        echo "No bookings found! Let's create a test booking...\n";
        
        // Insert a test booking
        $insert_query = "INSERT INTO bookings (
            first_name, last_name, email, contact_number, address, age, sex,
            emergency_name, emergency_number, destination_id, drop_off_location,
            no_of_pax, start_date, end_date, booking_time, boat_id,
            environmental_fee, payment_method, total, booking_status, booking_code
        ) VALUES (
            'Juan', 'Dela Cruz', '<EMAIL>', '09123456789', 'Manila, Philippines', 30, 'Male',
            'Maria <PERSON>', '09987654321', 1, 'Estancia Port',
            2, '2024-08-15', '2024-08-16', '08:00', 1,
            150.00, 'GCash', 1650.00, 'pending', 'BK2024001'
        )";
        
        if ($con->query($insert_query)) {
            echo "Test booking created successfully!\n";
            
            // Get the new booking ID
            $new_id = $con->insert_id;
            echo "New booking ID: $new_id\n\n";
            
            // Also insert passenger data
            $passenger_query = "INSERT INTO booking_passengers (
                booking_id, first_name, last_name, age, sex, city, province,
                contact_number, passenger_type, is_main_contact
            ) VALUES 
            ($new_id, 'Juan', 'Dela Cruz', 30, 'Male', 'Manila', 'Metro Manila', '09123456789', 'regular', 1),
            ($new_id, 'Maria', 'Dela Cruz', 28, 'Female', 'Manila', 'Metro Manila', '', 'regular', 0)";
            
            if ($con->query($passenger_query)) {
                echo "Test passenger data created successfully!\n\n";
            }
        } else {
            echo "Error creating test booking: " . $con->error . "\n";
        }
    }
    
    // Get a sample booking ID to test
    $sample_query = "SELECT booking_id, first_name, last_name, booking_status FROM bookings LIMIT 1";
    $result = $con->query($sample_query);
    
    if ($result->num_rows > 0) {
        $booking = $result->fetch_assoc();
        $test_id = $booking['booking_id'];
        
        echo "Testing booking details for ID: $test_id\n";
        echo "Booking: {$booking['first_name']} {$booking['last_name']} - Status: {$booking['booking_status']}\n\n";
        
        // Test the simplified query
        $test_query = "SELECT
            b.*,
            IFNULL(bt.name, 'Tourism Office Boat') as boat_name,
            IFNULL(bt.price_per_day, 0) as price_per_day,
            IFNULL(d.name, 'Gigantes Island') as destination_name
        FROM bookings b
        LEFT JOIN boats bt ON b.boat_id = bt.boat_id
        LEFT JOIN destinations d ON b.destination_id = d.destination_id
        WHERE b.booking_id = ?";
        
        $stmt = $con->prepare($test_query);
        $stmt->bind_param("i", $test_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            echo "✅ Query works! Booking details found.\n";
            $details = $result->fetch_assoc();
            echo "Name: {$details['first_name']} {$details['last_name']}\n";
            echo "Email: {$details['email']}\n";
            echo "Destination: {$details['destination_name']}\n";
            echo "Boat: {$details['boat_name']}\n";
        } else {
            echo "❌ Query failed - no results returned.\n";
        }
        
        // Test passenger data
        echo "\nTesting passenger manifest...\n";
        $passenger_query = "SELECT COUNT(*) as count FROM booking_passengers WHERE booking_id = ?";
        $stmt = $con->prepare($passenger_query);
        $stmt->bind_param("i", $test_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $passenger_count = $result->fetch_assoc()['count'];
        
        echo "Passengers found: $passenger_count\n";
        
    } else {
        echo "No bookings available to test.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

$con->close();
?>
