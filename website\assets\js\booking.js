// Global variables
let currentStep = 1;
const totalSteps = 3;

// Save current step to localStorage
function saveCurrentStep() {
    try {
        const savedData = localStorage.getItem('bookingFormData');
        let formData = {};

        if (savedData) {
            formData = JSON.parse(savedData);
        }

        formData.currentStep = currentStep;
        formData.lastUpdated = new Date().toISOString();

        localStorage.setItem('bookingFormData', JSON.stringify(formData));
        console.log('Saved current step to localStorage:', currentStep);
    } catch (error) {
        console.error('Error saving current step:', error);
    }
}

// Real-time date validation function
function validateDatesRealTime() {
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');

    if (!startDateInput || !endDateInput) {
        console.log('Date inputs not found');
        return false;
    }

    // Get selected dates
    let startDateSelected, endDateSelected;

    if (window.startDatePicker && window.endDatePicker) {
        // Using Flatpickr
        startDateSelected = window.startDatePicker.selectedDates[0];
        endDateSelected = window.endDatePicker.selectedDates[0];
    } else {
        // Using native date inputs
        startDateSelected = startDateInput.value ? new Date(startDateInput.value) : null;
        endDateSelected = endDateInput.value ? new Date(endDateInput.value) : null;
    }

    console.log('Real-time validation - Selected dates:', {
        startDate: startDateSelected,
        endDate: endDateSelected
    });

    // Validate start date
    const startDateValid = startDateSelected !== null;
    updateFieldValidation('startDate', startDateValid);

    // Validate end date
    let endDateValid = false;
    if (endDateSelected !== null) {
        if (startDateSelected) {
            // Check if end date is not before start date
            endDateValid = endDateSelected >= startDateSelected;
        } else {
            // End date selected but no start date
            endDateValid = false;
        }
    }
    updateFieldValidation('endDate', endDateValid);

    // Check if Step 2 should be marked as completed
    const step2Complete = startDateValid && endDateValid;
    console.log('Step 2 completion status:', step2Complete);

    // Update step indicators if we're currently on step 2 or beyond
    if (currentStep >= 2) {
        updateProgress();
    }

    return step2Complete;
}

// Update field validation visual feedback
function updateFieldValidation(fieldId, isValid) {
    const field = document.getElementById(fieldId);
    if (!field) return;

    // Remove existing validation classes
    field.classList.remove('valid', 'invalid');

    // Add appropriate validation class
    if (isValid) {
        field.classList.add('valid');
        console.log(`${fieldId} marked as valid`);
    } else {
        field.classList.add('invalid');
        console.log(`${fieldId} marked as invalid`);
    }
}

// Restore saved step from localStorage
function restoreSavedStep() {
    try {
        const savedData = localStorage.getItem('bookingFormData');
        if (!savedData) {
            console.log('No saved step data found, staying at step 1');
            return;
        }

        const formData = JSON.parse(savedData);
        if (!formData.currentStep || formData.currentStep < 1 || formData.currentStep > totalSteps) {
            console.log('Invalid saved step, staying at step 1');
            return;
        }

        if (formData.currentStep === 1) {
            console.log('Already at step 1, no restoration needed');
            return;
        }

        console.log('Restoring to saved step:', formData.currentStep);

        // Hide current step
        const activeStep = document.querySelector('.booking-step.active');
        if (activeStep) {
            activeStep.classList.remove('active');
        }

        // Show saved step
        const targetStep = document.getElementById('step' + formData.currentStep);
        if (targetStep) {
            targetStep.classList.add('active');
            currentStep = formData.currentStep;
            console.log('Successfully restored to step:', currentStep);
        } else {
            console.error('Target step element not found:', 'step' + formData.currentStep);
            currentStep = 1;
        }
    } catch (error) {
        console.error('Error restoring saved step:', error);
        currentStep = 1;
    }
}

// Check if user is coming from main website (fresh visit) - Global scope
const urlParams = new URLSearchParams(window.location.search);
const bookingCompleted = localStorage.getItem('bookingCompleted') === 'true';

// Detect fresh visit from main website (for clearing data)
// ONLY clear data if:
// 1. Coming from main website with fresh=true parameter, OR
// 2. Booking was just completed
const isExplicitlyFresh = (urlParams.has('fresh') && urlParams.get('fresh') === 'true') ||
                         bookingCompleted;

// Always load form data if it exists (for refresh scenarios)
const shouldLoadFormData = true;

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing booking.js');

    if (isExplicitlyFresh) {
        console.log('User coming from main website - clearing previous form data');
        localStorage.removeItem('bookingFormData');
        localStorage.removeItem('bookingCompleted'); // Clear the completion flag
        currentStep = 1;

        // Force a complete form reset for fresh visitors
        setTimeout(function() {
            console.log('Performing complete form reset for fresh visitor');
            const form = document.getElementById('bookingForm');
            if (form) {
                form.reset();
            }

            // Reset all step indicators
            document.querySelectorAll('.booking-step').forEach(step => step.classList.remove('active'));
            const step1 = document.getElementById('step1');
            if (step1) step1.classList.add('active');

            // Reset progress bar
            const progressBar = document.getElementById('progressBar');
            if (progressBar) progressBar.style.width = '0%';

            // Reset step indicators
            document.querySelectorAll('.step').forEach((step, i) => {
                if (i === 0) step.classList.add('active');
                else step.classList.remove('active');
            });

            // Recalculate fees and regenerate booking ID
            calculateTotalFees();
            generateBookingId();

            // Show a subtle notification that this is a fresh form
            setTimeout(function() {
                Swal.fire({
                    title: 'Fresh Booking Form',
                    text: 'Ready for a new booking!',
                    icon: 'info',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true
                });
            }, 500);
        }, 100);
    } else {
        // Check for saved step in localStorage and restore it
        const savedData = localStorage.getItem('bookingFormData');
        if (savedData) {
            try {
                const formData = JSON.parse(savedData);
                if (formData && formData.currentStep && formData.currentStep >= 1 && formData.currentStep <= totalSteps) {
                    currentStep = formData.currentStep;
                    console.log('Restored currentStep from localStorage:', currentStep);
                } else {
                    currentStep = 1;
                    console.log('Invalid or missing currentStep in localStorage, defaulting to 1');
                }
            } catch (error) {
                console.error('Error parsing saved form data:', error);
                currentStep = 1;
            }
        } else {
            currentStep = 1;
            console.log('No saved data found, starting at step 1');
        }
    }

    // Initialize date pickers
    initializeDatePickers();

    // Set up event listeners
    setupEventListeners();

    // Initialize calculations
    calculateTotalFees();
    updateDateTimeSummary();
    updateBookingTime();

    // Generate booking ID
    generateBookingId();

    // Initialize tooltips
    initializeTooltips();

    // Initialize hidden fields
    initializeHiddenFields();

    // Set current date for Today's Bookings
    setCurrentDate();

    // Always load saved form data if available (even on refresh)
    loadFormData();

    // Save form data on any input change
    document.querySelectorAll('input, select, textarea').forEach(element => {
        element.addEventListener('change', saveFormData);
        element.addEventListener('input', saveFormData);
    });

    // Save form data before page unload and warn user about unsaved changes
    window.addEventListener('beforeunload', function(e) {
        console.log('Saving form data before page unload');
        saveFormData();

        // Check if user has entered any data
        const savedData = localStorage.getItem('bookingFormData');
        if (savedData) {
            try {
                const formData = JSON.parse(savedData);
                if (formData.hasUserEnteredData === true) {
                    // Show warning message
                    const message = 'You have unsaved booking information. Are you sure you want to leave?';
                    e.preventDefault();
                    e.returnValue = message;
                    return message;
                }
            } catch (error) {
                console.error('Error checking form data:', error);
            }
        }
    });

    // Force a save of the form data after a short delay
    // This ensures any URL parameters are properly saved to localStorage
    setTimeout(function() {
        console.log('Forcing initial save of form data');
        saveFormData();
    }, 1000);

    // Setup additional functionality
    setupFormSubmission();
    setupDateAndBookingId();

    // Initialize environmental fees and button states
    const totalPassengers = parseInt(document.getElementById('totalPassengers')?.value) || 1;
    syncEnvironmentalFeesWithPassengers(totalPassengers);
    updateFeeButtonStates();

    // Initialize step indicators and restore saved step
    restoreSavedStep();
    updateProgress();

    setupMobileMenu();
    setupPaymentMethods();
});

// Global date picker variables
window.startDatePicker = null;
window.endDatePicker = null;

// Initialize date pickers
function initializeDatePickers() {
    console.log('Initializing date pickers');

    try {
        // Start date picker
        window.startDatePicker = flatpickr("#startDate", {
            minDate: "today",
            dateFormat: "Y-m-d",
            altInput: true,
            altFormat: "F j, Y (l)",
            onChange: function(_, dateStr) {
                // Use underscore for unused parameter
                if (window.endDatePicker) {
                    window.endDatePicker.set("minDate", dateStr);
                }
                updateDateTimeSummary();
                // Real-time validation
                setTimeout(validateDatesRealTime, 100);
            }
        });

        // End date picker
        window.endDatePicker = flatpickr("#endDate", {
            minDate: "today",
            dateFormat: "Y-m-d",
            altInput: true,
            altFormat: "F j, Y (l)",
            onChange: function(_) {
                // Use underscore for unused parameter
                updateDateTimeSummary();
                // Real-time validation
                setTimeout(validateDatesRealTime, 100);
            }
        });

        console.log('Date pickers initialized successfully:', {
            startDatePicker: window.startDatePicker ? 'OK' : 'Failed',
            endDatePicker: window.endDatePicker ? 'OK' : 'Failed'
        });
    } catch (error) {
        console.error('Error initializing date pickers:', error);

        // Create a fallback for date pickers if flatpickr fails
        if (!window.startDatePicker) {
            console.log('Creating fallback for start date picker');
            const startDateInput = document.getElementById('startDate');
            if (startDateInput) {
                startDateInput.type = 'date';
                startDateInput.min = new Date().toISOString().split('T')[0];
                startDateInput.addEventListener('change', function() {
                    const endDateInput = document.getElementById('endDate');
                    if (endDateInput) {
                        endDateInput.min = this.value;
                    }
                    updateDateTimeSummary();
                    // Real-time validation
                    setTimeout(validateDatesRealTime, 100);
                });
            }
        }

        if (!window.endDatePicker) {
            console.log('Creating fallback for end date picker');
            const endDateInput = document.getElementById('endDate');
            if (endDateInput) {
                endDateInput.type = 'date';
                endDateInput.min = new Date().toISOString().split('T')[0];
                endDateInput.addEventListener('change', function() {
                    updateDateTimeSummary();
                    // Real-time validation
                    setTimeout(validateDatesRealTime, 100);
                });
            }
        }
    }
}

// Set up event listeners
function setupEventListeners() {
    // Form submission
    const bookingForm = document.getElementById('bookingForm');
    if (bookingForm) {
        bookingForm.addEventListener('submit', onBookingFormSubmit);

        // Add direct click event listener to the submit button
        const submitButton = document.querySelector('.btn-submit');
        if (submitButton) {
            console.log('Adding click event listener to submit button');
            submitButton.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Submit button clicked');
                onBookingFormSubmit(e);
            });
        }
    }

    // Gender selection - no longer needed since we removed the "Other" option
    // const genderSelect = document.getElementById('gender');
    // if (genderSelect) {
    //     genderSelect.addEventListener('change', toggleGenderSpecify);
    // }

    // Auto-resize textarea to remove scrollbar
    const addressTextarea = document.getElementById('completeAddress');
    if (addressTextarea) {
        addressTextarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
            // Save form data when textarea changes
            saveFormData();
        });
        // Initial resize
        addressTextarea.style.height = 'auto';
        addressTextarea.style.height = (addressTextarea.scrollHeight) + 'px';
    }

    // Payment options
    document.querySelectorAll('.payment-option').forEach(option => {
        option.addEventListener('click', function() {
            const radio = option.querySelector('input[type="radio"]');
            radio.checked = true;
            document.querySelectorAll('.payment-option').forEach(o => o.classList.remove('selected'));
            option.classList.add('selected');

            // Show/hide payment details
            const paymentType = radio.id;
            document.getElementById('gcashDetails').style.display = paymentType === 'gcash' ? 'block' : 'none';
            document.getElementById('manualDetails').style.display = paymentType === 'manual' ? 'block' : 'none';

            // Update payment method in summary
            const summaryPaymentValue = document.getElementById('summaryPaymentValue');
            if (summaryPaymentValue) {
                summaryPaymentValue.textContent = radio.value;
                console.log('Payment method updated in summary:', radio.value);
            }

            // Save form data when payment option changes
            saveFormData();

            // Update step indicators since payment method is now selected
            updateProgress();
        });
    });

    // Environmental fee inputs
    ['regularPax', 'discountedPax', 'childrenPax', 'infantsPax'].forEach(function(id) {
        const el = document.getElementById(id);
        if (el) {
            el.addEventListener('input', function() {
                calculateTotalFees();
                // Save form data when environmental fees change
                saveFormData();
            });
        }
    });

    // Number of pax
    const numberOfPaxEl = document.getElementById('numberOfPax');
    if (numberOfPaxEl) {
        numberOfPaxEl.addEventListener('input', function() {
            syncNumberOfPax();
            // Save form data when number of pax changes
            saveFormData();
        });
    }

    // Update booking summary when fields change
    const fieldsToUpdate = [
        'firstName', 'lastName', 'age', 'sex', 'contactNumber', 'emailAddress',
        'completeAddress', 'emergencyName', 'emergencyNumber', 'locationTourDestination',
        'dropOffLocation', 'numberOfPax'
    ];

    fieldsToUpdate.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('input', function() {
                updateBookingSummary();
                // Save form data when fields change
                saveFormData();
            });
            field.addEventListener('change', function() {
                updateBookingSummary();
                // Save form data when fields change
                saveFormData();
            });
        }
    });

    // Save form data when dates change
    document.getElementById('startDate').addEventListener('change', saveFormData);
    document.getElementById('endDate').addEventListener('change', saveFormData);

    // Duplicate beforeunload event removed - already exists above
}

// Initialize tooltips
function initializeTooltips() {
    // Initialize AOS
    AOS.init();
}

// Initialize hidden fields
function initializeHiddenFields() {
    console.log('Initializing hidden fields');

    // Ensure the selectedBoat input exists - always set to "AssignedByTourismOffice"
    let selectedBoatInput = document.getElementById('selectedBoat');
    if (!selectedBoatInput) {
        console.log('Creating selectedBoat input');
        selectedBoatInput = document.createElement('input');
        selectedBoatInput.type = 'hidden';
        selectedBoatInput.id = 'selectedBoat';
        selectedBoatInput.name = 'selectedBoat';
        document.getElementById('bookingForm').appendChild(selectedBoatInput);
    }

    // Always set to "AssignedByTourismOffice" regardless of URL parameters
    // This means the Tourism Office will be responsible for arranging the boat for tourists
    console.log('Setting boat value to be assigned by tourism office');
    selectedBoatInput.value = 'AssignedByTourismOffice';

    // Ensure the bookingId input exists
    const bookingIdInput = document.getElementById('bookingId');
    if (!bookingIdInput) {
        console.log('Creating bookingId input');
        const newBookingIdInput = document.createElement('input');
        newBookingIdInput.type = 'hidden';
        newBookingIdInput.id = 'bookingId';
        newBookingIdInput.name = 'bookingId';
        document.getElementById('bookingForm').appendChild(newBookingIdInput);
    }

    // Debug button removed
}

// Form submission handler
function onBookingFormSubmit(e) {
    e.preventDefault();

    // Confirm booking
    Swal.fire({
        title: 'Complete Booking?',
        text: 'Are you sure you want to complete your booking? You will receive a verification email.', // Alert the user to confirm the booking
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Complete Booking',
        cancelButtonText: 'Cancel',
    }).then((result) => {
        if (result.isConfirmed) {
            // Make sure all hidden fields are properly set before submission
            ensureHiddenFieldsExist();
            submitBookingForm();
        }
    });
}

// Ensure all required hidden fields exist and have values
function ensureHiddenFieldsExist() {
    const bookingForm = document.getElementById('bookingForm');
    if (!bookingForm) return;

    // Check selectedBoat field
    let selectedBoatInput = document.getElementById('selectedBoat');
    if (!selectedBoatInput) {
        console.log('Creating missing selectedBoat input before submission');
        selectedBoatInput = document.createElement('input');
        selectedBoatInput.type = 'hidden';
        selectedBoatInput.id = 'selectedBoat';
        selectedBoatInput.name = 'selectedBoat';
        bookingForm.appendChild(selectedBoatInput);
    }

    // Always set to "AssignedByTourismOffice" regardless of previous value
    // This means the Tourism Office will handle boat arrangements for tourists
    selectedBoatInput.value = 'AssignedByTourismOffice';
    console.log('Setting boat to be assigned by tourism office before submission');

    // Check bookingId field
    const bookingIdInput = document.getElementById('bookingId');
    if (!bookingIdInput) {
        console.log('Creating missing bookingId input before submission');
        const newBookingIdInput = document.createElement('input');
        newBookingIdInput.type = 'hidden';
        newBookingIdInput.id = 'bookingId';
        newBookingIdInput.name = 'bookingId';
        bookingForm.appendChild(newBookingIdInput);

        // Generate a booking ID for the new input
        generateBookingId();
    } else if (!bookingIdInput.value) {
        // Generate a booking ID if it doesn't exist
        console.log('Generating new bookingId before submission');
        generateBookingId();
    }

    // Add duration field if it doesn't exist
    let durationInput = document.getElementById('duration');
    if (!durationInput) {
        console.log('Creating missing duration input before submission');
        durationInput = document.createElement('input');
        durationInput.type = 'hidden';
        durationInput.id = 'duration';
        durationInput.name = 'duration';
        bookingForm.appendChild(durationInput);

        // Calculate duration from start and end dates
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            const diffTime = Math.abs(end - start);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            durationInput.value = diffDays + ' day(s)';
            console.log('Setting duration value before submission:', durationInput.value);
        }
    }
}

// Submit booking form
function submitBookingForm() {
    console.log('submitBookingForm() called');

    const bookingForm = document.getElementById('bookingForm');
    const formData = new FormData(bookingForm);

    // Add payment method
    const paymentRadio = document.querySelector('input[name="paymentMethod"]:checked');
    if (paymentRadio) {
        formData.append('paymentMethod', paymentRadio.value);
        console.log('Payment method added:', paymentRadio.value);

        // If GCash is selected, add proof of payment file
        if (paymentRadio.value === 'GCash') {
            const proofUpload = document.getElementById('gcashProofUpload');
            if (proofUpload && proofUpload.files[0]) {
                formData.append('gcashProofUpload', proofUpload.files[0]);
                console.log('GCash proof of payment added:', proofUpload.files[0].name);
            }
        }

        // Update payment method in summary one last time before submission
        const summaryPaymentValue = document.getElementById('summaryPaymentValue');
        if (summaryPaymentValue) {
            summaryPaymentValue.textContent = paymentRadio.value;
            console.log('Payment method updated in summary before submission:', paymentRadio.value);
        }
    } else {
        console.warn('No payment method selected!');
    }

    // Add total price
    const totalPriceElement = document.getElementById('totalPrice');
    if (totalPriceElement) {
        const totalPrice = totalPriceElement.textContent.replace('₱', '').replace(/,/g, '');
        formData.append('total', totalPrice);
        console.log('Total price added:', totalPrice);

        // Also add totalEnvironmentalFee as a backup
        const totalEnvFeeElement = document.getElementById('totalEnvironmentalFeeValue');
        if (totalEnvFeeElement) {
            formData.append('totalEnvironmentalFee', totalEnvFeeElement.value || totalPrice);
            console.log('Total environmental fee added:', totalEnvFeeElement.value || totalPrice);
        }
    } else {
        console.warn('Total price element not found!');
    }

    // Ensure current date is set for Today's Bookings
    const currentDateInput = document.getElementById('currentDate');
    if (!currentDateInput.value) {
        setCurrentDate();
        console.log('Current date set before submission:', currentDateInput.value);
    }

    // Set status
    formData.append('status', 'verification_pending');

    // Log all form data for debugging
    console.log('Form data being submitted:');
    for (let pair of formData.entries()) {
        console.log(pair[0] + ': ' + pair[1]);
    }

    // Show loading state
    Swal.fire({
        title: 'Processing Booking...',
        text: 'Please wait while we process your booking.',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
            console.log('Loading dialog shown');
        }
    });

    console.log('Sending request to integrated_verification.php...');

    // Send booking data to the integrated verification process
    fetch('../process/integrated_verification.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Response received:', response);
        console.log('Response status:', response.status);

        // Always try to get the response as text first
        return response.text().then(text => {
            console.log('Response text:', text);

            // Try to parse as JSON if possible
            let data;
            try {
                data = JSON.parse(text);
                console.log('Successfully parsed JSON:', data);
                return data;
            } catch (e) {
                console.log('Not valid JSON, handling as text response');

                // Check if the text contains success indicators
                if (text.includes('success') || text.includes('successful') ||
                    text.includes('verification email sent') || text.includes('booking received')) {
                    console.log('Text indicates success');
                    return { success: true, message: 'Booking successful!' };
                }

                // Check if this is a PHP error
                if (text.includes('<br />') || text.includes('Fatal error') || text.includes('Warning')) {
                    console.error('PHP error detected in response');

                    // For debugging - log the full error
                    console.error('Full server error:', text);

                    // Return a success response anyway to allow the booking to be processed
                    console.log('Returning success despite PHP error to allow booking to continue');
                    return { success: true, message: 'Booking received! Note: There was an issue sending the confirmation email, but your booking has been processed.' };
                }

                // If response is empty or not recognized
                if (!text.trim()) {
                    console.error('Empty response from server');
                    // Return a success response anyway to allow the booking to be processed
                    return { success: true, message: 'Booking received! Note: There was an issue with the server response, but your booking has been processed.' };
                }

                // For any other unrecognized response
                console.error('Unrecognized server response');
                // Return a success response anyway to allow the booking to be processed
                return { success: true, message: 'Booking received! Note: There was an unexpected server response, but your booking has been processed.' };
            }
        });
    })
    .then(data => {
        console.log('Response data:', data);

        // Show success message and try to send email
        console.log('Showing success message and sending email');

        // Email is now sent directly from the integrated verification process
        if (data.success && data.booking_id) {
            console.log('Booking successful with ID:', data.booking_id);
        }

        // Display booking confirmation with code
        const bookingCode = data.booking_code || 'N/A';
        console.log('Booking code:', bookingCode);

        Swal.fire({
            title: 'Booking Confirmed!',
            html: `
                <div style="text-align: center; padding: 20px;">
                    <div style="background-color: #d4edda; border: 2px solid #28a745; border-radius: 10px; padding: 20px; margin: 15px 0;">
                        <h3 style="color: #155724; margin-bottom: 10px;">Your Confirmation Code:</h3>
                        <div style="font-size: 24px; font-weight: bold; color: #28a745; letter-spacing: 2px; font-family: monospace;">
                            ${bookingCode}
                        </div>
                    </div>
                    <p style="margin-top: 15px; color: #333;">
                        Your booking has been received and a verification email has been sent to your email address.
                        Please save this confirmation code and check your inbox (and spam folder) for details.
                    </p>
                    <p style="color: #666; font-size: 14px; margin-top: 10px;">
                        <strong>Important:</strong> Present this confirmation code when you arrive at the tourism office.
                    </p>
                </div>
            `,
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#28a745',
            width: '500px'
        }).then(() => {
            console.log('Success dialog closed, clearing form...');
            clearFormData();
            resetBookingSteps();

            // Set a flag that booking was completed
            localStorage.setItem('bookingCompleted', 'true');

            console.log('Form cleared and reset');
        });
    })
    .catch(error => {
        console.error('Error in fetch operation:', error);
        console.error('Error stack:', error.stack);

        // Log the error but still show a success message
        console.log('Showing success message despite error');

        Swal.fire({
            title: 'Booking Received!',
            text: 'Your booking has been received and a verification email has been sent to your email address. Please check your inbox (and spam folder) for confirmation details.',
            icon: 'success',
            confirmButtonText: 'OK'
        }).then(() => {
            console.log('Success dialog closed, clearing form...');
            clearFormData();
            resetBookingSteps();

            // Set a flag that booking was completed
            localStorage.setItem('bookingCompleted', 'true');

            console.log('Form cleared and reset');
        });
    });
}

// Calculate total fees
function calculateTotalFees() {
    // Get values from inputs
    const regularPax = parseInt(document.getElementById('regularPax').value) || 0;
    const discountedPax = parseInt(document.getElementById('discountedPax').value) || 0;
    const childrenPax = parseInt(document.getElementById('childrenPax').value) || 0;
    const infantsPax = parseInt(document.getElementById('infantsPax').value) || 0;

    // Check total pax limit
    const totalPax = regularPax + discountedPax + childrenPax + infantsPax;
    if (totalPax > 25) {
        // Always show the alert when total exceeds 25
        Swal.fire({
            title: 'Warning!',
            text: 'The total number of passengers cannot exceed 25.',
            icon: 'warning',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 4000,
            timerProgressBar: true
        });

        // Reset to valid values immediately
        setTimeout(() => {
            document.getElementById('regularPax').value = Math.min(regularPax, 25);
            document.getElementById('discountedPax').value = 0;
            document.getElementById('childrenPax').value = 0;
            document.getElementById('infantsPax').value = 0;

            // Update numberOfPax field
            document.getElementById('numberOfPax').value = Math.min(regularPax, 25);

            // Recalculate with new values
            calculateTotalFees();
        }, 100);
        return;
    }

    // Calculate fees
    const regularFee = regularPax * 75;
    const discountedFee = discountedPax * 60;
    const childrenFee = childrenPax * 60;
    const infantsFee = 0; // Infants are free

    // Calculate total
    const totalFee = regularFee + discountedFee + childrenFee + infantsFee;

    // Update display
    document.getElementById('totalEnvironmentalFee').textContent = '₱' + totalFee.toFixed(2);
    document.getElementById('totalEnvironmentalFeeValue').value = totalFee;

    // Update summary
    document.getElementById('summaryEnvFeeValue').textContent = '₱' + totalFee.toFixed(2);

    // Update total price
    const formattedTotal = '₱' + totalFee.toFixed(2);
    document.getElementById('totalPrice').textContent = formattedTotal;
    console.log('Setting totalPrice to:', formattedTotal);

    // Update hidden total input field
    document.getElementById('total').value = totalFee.toFixed(2);
    console.log('Updated hidden total input field:', totalFee.toFixed(2));

    // Update numberOfPax field to match total
    document.getElementById('numberOfPax').value = totalPax;
}

// Sync number of pax
function syncNumberOfPax() {
    let numberOfPax = parseInt(document.getElementById('numberOfPax').value) || 0;

    // Enforce maximum limit
    if (numberOfPax > 25) {
        // Always show the alert when number exceeds 25
        Swal.fire({
            title: 'Warning!',
            text: 'The maximum number of passengers is 25.',
            icon: 'warning',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 4000,
            timerProgressBar: true
        });

        // Set to maximum allowed value immediately
        setTimeout(() => {
            numberOfPax = 25;
            document.getElementById('numberOfPax').value = 25;

            // Update regular adults count
            document.getElementById('regularPax').value = numberOfPax;

            // Reset other passenger types
            document.getElementById('discountedPax').value = 0;
            document.getElementById('childrenPax').value = 0;
            document.getElementById('infantsPax').value = 0;

            calculateTotalFees();
        }, 100);
        return;
    }

    // If number of pax is 0, set it to at least 1
    if (numberOfPax <= 0) {
        numberOfPax = 1;
        document.getElementById('numberOfPax').value = 1;
    }

    // Update regular adults count
    document.getElementById('regularPax').value = numberOfPax;

    // Reset other passenger types
    document.getElementById('discountedPax').value = 0;
    document.getElementById('childrenPax').value = 0;
    document.getElementById('infantsPax').value = 0;

    calculateTotalFees();
}

// Update date and time summary
function updateDateTimeSummary() {
    const startDate = startDatePicker.selectedDates[0];
    const endDate = endDatePicker.selectedDates[0];

    if (startDate) {
        const formattedStartDate = startDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
        document.getElementById('startDateTime').textContent = formattedStartDate;
        document.getElementById('summaryStartDate').textContent = formattedStartDate;
    }

    if (endDate) {
        const formattedEndDate = endDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
        document.getElementById('endDateTime').textContent = formattedEndDate;
        document.getElementById('summaryEndDate').textContent = formattedEndDate;
    }
}

// Update booking time
function updateBookingTime() {
    const now = new Date();
    const bookingTime = now.toLocaleString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        second: 'numeric',
        hour12: true
    });
    document.getElementById('bookingTime').textContent = bookingTime;
    document.getElementById('summaryBookingTime').textContent = bookingTime;
}

// Update booking summary
function updateBookingSummary() {
    // Update personal information
    document.getElementById('summaryFirstName').textContent = document.getElementById('firstName').value || 'Not provided';
    document.getElementById('summaryLastName').textContent = document.getElementById('lastName').value || 'Not provided';
    document.getElementById('summaryAge').textContent = document.getElementById('age').value || 'Not provided';

    // Handle sex - simplified since we only have Male and Female options
    const sex = document.getElementById('sex').value;
    let sexText = sex || 'Not provided';
    document.getElementById('summarySex').textContent = sexText;

    // Update contact information
    document.getElementById('summaryContact').textContent = document.getElementById('contactNumber').value || 'Not provided';
    document.getElementById('summaryEmail').textContent = document.getElementById('emailAddress').value || 'Not provided';
    document.getElementById('summaryAddress').textContent = document.getElementById('completeAddress').value || 'Not provided';

    // Update emergency contact information
    const emergencyName = document.getElementById('emergencyName').value || '';
    const emergencyNumber = document.getElementById('emergencyNumber').value || '';

    // Display emergency contact name only
    document.getElementById('summaryEmergencyContact').textContent = emergencyName || 'Not provided';

    // Display emergency number (same as emergency contact number)
    document.getElementById('summaryEmergencyNumber').textContent = emergencyNumber || 'Not provided';

    // Update tour information
    document.getElementById('summaryDestination').textContent = document.getElementById('locationTourDestination').value || 'Not provided';
    document.getElementById('summaryDropOff').textContent = document.getElementById('dropOffLocation').value || 'Not provided';
    document.getElementById('summaryPax').textContent = document.getElementById('numberOfPax').value || 'Not provided';

    // Update payment method
    const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked');
    if (paymentMethod) {
        // Simply update the payment method value in the summary
        const summaryPaymentValue = document.getElementById('summaryPaymentValue');
        if (summaryPaymentValue) {
            summaryPaymentValue.textContent = paymentMethod.value;
            console.log('Payment method updated in summary from updateBookingSummary:', paymentMethod.value);
        }
    }

    // Boat information removed - will be assigned by tourism office

    // Don't display booking ID in summary as it's controlled by admin
}

// Generate booking ID
function generateBookingId() {
    const now = new Date();
    const yyyy = now.getFullYear();
    const mm = String(now.getMonth()+1).padStart(2,'0');
    const dd = String(now.getDate()).padStart(2,'0');
    const random = Math.floor(10000 + Math.random() * 90000);
    const id = 'BOOKING-' + yyyy + mm + dd + '-' + random;

    // Set the value in the hidden input only
    const bookingIdInput = document.getElementById('bookingId');
    if (bookingIdInput) {
        bookingIdInput.value = id;
    }

    // Don't display the booking ID in the form as it's controlled by admin
    // The booking ID is still generated and stored in the hidden input
    // for submission with the form

    return id;
}

// Set current date for Today's Bookings
function setCurrentDate() {
    const now = new Date();
    const yyyy = now.getFullYear();
    const mm = String(now.getMonth()+1).padStart(2,'0');
    const dd = String(now.getDate()).padStart(2,'0');
    const currentDate = yyyy + '-' + mm + '-' + dd;

    // Set the value in the hidden input
    const currentDateInput = document.getElementById('currentDate');
    if (currentDateInput) {
        currentDateInput.value = currentDate;
        console.log('Current date set for Today\'s Bookings:', currentDate);
    }
}

// Navigation functions
function nextStep(step) {
    // Validate current step
    if (!validateStep(step)) {
        return;
    }

    if (step >= totalSteps) {
        return;
    }

    // Update booking summary before navigating to payment step
    if (step + 1 === 3) {
        updateBookingSummary();
    }

    // Hide current step
    document.querySelector('.booking-step.active').classList.remove('active');

    // Show next step
    document.getElementById('step' + (step + 1)).classList.add('active');

    // Update current step
    currentStep = step + 1;

    // Save current step to localStorage immediately
    saveCurrentStep();

    // Update progress
    updateProgress();

    // Save form data when moving to next step
    saveFormData();
}

function prevStep(step) {
    if (step <= 1) return;

    // Hide current step
    document.querySelector('.booking-step.active').classList.remove('active');

    // Show previous step
    document.getElementById('step' + (step - 1)).classList.add('active');

    // Update current step
    currentStep = step - 1;

    // Save current step to localStorage immediately
    saveCurrentStep();

    // Update progress
    updateProgress();

    // Save form data when moving to previous step
    saveFormData();
}

// Update progress bar
function updateProgress() {
    const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
    const progressBar = document.getElementById('progressBar');
    if (progressBar) {
        progressBar.style.width = `${progress}%`;
        progressBar.style.backgroundColor = progress === 100 ? '#28a745' : '#007bff';
    }

    // Update step indicators
    console.log(`Updating progress: currentStep = ${currentStep}, totalSteps = ${totalSteps}`);

    document.querySelectorAll('.step').forEach((step, index) => {
        const stepNumber = index + 1;
        const stepCircle = step.querySelector('.step-circle');

        if (stepNumber < currentStep) {
            // Completed step - but check special conditions
            let isCompleted = true;

            // Special check for Step 2: only complete if both dates are valid
            if (stepNumber === 2) {
                isCompleted = validateDatesRealTime();
            }

            // Special check for Step 3: only complete if payment method is selected
            if (stepNumber === 3) {
                const paymentSelected = document.querySelector('input[name="paymentMethod"]:checked');
                isCompleted = paymentSelected !== null;
            }

            if (isCompleted) {
                step.classList.add('completed');
                step.classList.remove('active');
                if (stepCircle) {
                    stepCircle.textContent = '✓';
                }
                console.log(`Step ${stepNumber}: Set to COMPLETED`);
            } else {
                // Not truly completed yet
                step.classList.remove('active', 'completed');
                if (stepCircle) {
                    stepCircle.textContent = stepNumber;
                }
                console.log(`Step ${stepNumber}: Not completed (missing requirements)`);
            }
        } else if (stepNumber === currentStep) {
            // Current active step - but check if it should be completed
            let shouldBeCompleted = false;

            // Check if current step should be marked as completed
            if (stepNumber === 2) {
                shouldBeCompleted = validateDatesRealTime();
            } else if (stepNumber === 3) {
                const paymentSelected = document.querySelector('input[name="paymentMethod"]:checked');
                shouldBeCompleted = paymentSelected !== null;
            }

            if (shouldBeCompleted) {
                step.classList.add('completed');
                step.classList.remove('active');
                if (stepCircle) {
                    stepCircle.textContent = '✓';
                }
                console.log(`Step ${stepNumber}: Set to COMPLETED (requirements met)`);
            } else {
                step.classList.add('active');
                step.classList.remove('completed');
                if (stepCircle) {
                    stepCircle.textContent = stepNumber;
                }
                console.log(`Step ${stepNumber}: Set to ACTIVE`);
            }
        } else {
            // Future step
            step.classList.remove('active', 'completed');
            if (stepCircle) {
                stepCircle.textContent = stepNumber;
            }
            console.log(`Step ${stepNumber}: Set to INACTIVE`);
        }
    });
}

// Validate step
function validateStep(step) {
    console.log('Validating step:', step);

    switch (step) {
        case 1: // Personal Info
            console.log('Validating Personal Info step');
            const requiredFields = [
                { id: 'firstName', label: 'First Name' },
                { id: 'lastName', label: 'Last Name' },
                { id: 'age', label: 'Age' },
                { id: 'sex', label: 'Sex' },
                { id: 'contactNumber', label: 'Contact Number' },
                { id: 'emailAddress', label: 'Email Address' },
                { id: 'completeAddress', label: 'Complete Address' },
                { id: 'emergencyName', label: 'Emergency Contact Name' },
                { id: 'emergencyNumber', label: 'Emergency Contact Number' },
                { id: 'locationTourDestination', label: 'Tour Destination' },
                { id: 'dropOffLocation', label: 'Drop-off Location' },
                { id: 'numberOfPax', label: 'Number of Passengers' }
            ];

            for (let i = 0; i < requiredFields.length; i++) {
                const field = document.getElementById(requiredFields[i].id);
                if (!field || !field.value) {
                    console.log(`Missing required field: ${requiredFields[i].id}`);
                    Swal.fire({
                        title: 'Warning!',
                        text: `Please enter your ${requiredFields[i].label}.`,
                        icon: 'warning',
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 4000,
                        timerProgressBar: true
                    });

                    // Focus on the field after a short delay
                    setTimeout(() => {
                        if (field) {
                            field.focus();
                            field.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    }, 100);
                    return false;
                }
            }

            // Email validation
            const emailField = document.getElementById('emailAddress');
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (emailField && !emailRegex.test(emailField.value)) {
                console.log('Invalid email format');
                Swal.fire({
                    title: 'Warning!',
                    text: 'Please enter a valid email address.',
                    icon: 'warning',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true
                });

                // Focus on the field after a short delay
                setTimeout(() => {
                    emailField.focus();
                    emailField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 100);
                return false;
            }

            // Check if environmental fee is filled
            const regularPax = parseInt(document.getElementById('regularPax').value) || 0;
            const discountedPax = parseInt(document.getElementById('discountedPax').value) || 0;
            const childrenPax = parseInt(document.getElementById('childrenPax').value) || 0;
            const infantsPax = parseInt(document.getElementById('infantsPax').value) || 0;

            console.log('Environmental fee values:', {
                regularPax,
                discountedPax,
                childrenPax,
                infantsPax
            });

            if (regularPax === 0 && discountedPax === 0 && childrenPax === 0 && infantsPax === 0) {
                console.log('No passengers entered in environmental fee section');
                Swal.fire({
                    title: 'Warning!',
                    text: 'Please enter at least one passenger in the Environmental Fee section.',
                    icon: 'warning',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true
                });

                // Focus on the field after a short delay
                setTimeout(() => {
                    document.getElementById('regularPax').focus();
                    document.getElementById('regularPax').scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 100);
                return false;
            }

            // Validate passenger table fields
            const numberOfPassengers = parseInt(document.getElementById('numberOfPax').value) || 0;
            console.log('Validating passenger table for', numberOfPassengers, 'passengers');

            // Check if passenger table exists and has been generated
            const passengerTableContainer = document.getElementById('passengerInputTable');
            if (numberOfPassengers > 0 && (!passengerTableContainer || !passengerTableContainer.innerHTML.trim())) {
                console.log('Passenger table not generated properly');
                Swal.fire({
                    title: 'Warning!',
                    text: 'Please update the number of passengers to generate the passenger details table.',
                    icon: 'warning',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true
                });

                setTimeout(() => {
                    document.getElementById('numberOfPax').focus();
                    document.getElementById('numberOfPax').scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 100);
                return false;
            }

            for (let i = 1; i <= numberOfPassengers; i++) {
                const passengerFields = [
                    { id: `passenger${i}FirstName`, label: `Passenger ${i} First Name` },
                    { id: `passenger${i}LastName`, label: `Passenger ${i} Last Name` },
                    { id: `passenger${i}Age`, label: `Passenger ${i} Age` },
                    { id: `passenger${i}Gender`, label: `Passenger ${i} Gender` },
                    { id: `passenger${i}CompleteAddress`, label: `Passenger ${i} Complete Address` }
                ];

                for (let j = 0; j < passengerFields.length; j++) {
                    const field = document.getElementById(passengerFields[j].id);
                    if (!field || !field.value.trim()) {
                        console.log(`Missing passenger field: ${passengerFields[j].id}`);
                        Swal.fire({
                            title: 'Warning!',
                            text: `Please enter ${passengerFields[j].label}.`,
                            icon: 'warning',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 4000,
                            timerProgressBar: true
                        });

                        // Focus on the field and scroll to passenger table
                        setTimeout(() => {
                            if (field) {
                                field.focus();
                                field.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }
                        }, 100);
                        return false;
                    }
                }

                // Validate age is a number and reasonable
                const ageField = document.getElementById(`passenger${i}Age`);
                if (ageField && ageField.value) {
                    const age = parseInt(ageField.value);
                    if (isNaN(age) || age < 1 || age > 120) {
                        console.log(`Invalid age for passenger ${i}:`, ageField.value);
                        Swal.fire({
                            title: 'Warning!',
                            text: `Please enter a valid age (1-120) for Passenger ${i}.`,
                            icon: 'warning',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 4000,
                            timerProgressBar: true
                        });

                        setTimeout(() => {
                            ageField.focus();
                            ageField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }, 100);
                        return false;
                    }
                }

                // Validate contact number format if provided (optional field)
                const contactField = document.getElementById(`passenger${i}ContactNumber`);
                if (contactField && contactField.value.trim()) {
                    const contactRegex = /^09[0-9]{9}$/;
                    if (!contactRegex.test(contactField.value.trim())) {
                        console.log(`Invalid contact format for passenger ${i}:`, contactField.value);
                        Swal.fire({
                            title: 'Warning!',
                            text: `Please enter a valid contact number (09XXXXXXXXX) for Passenger ${i} or leave it empty.`,
                            icon: 'warning',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 5000,
                            timerProgressBar: true
                        });

                        setTimeout(() => {
                            contactField.focus();
                            contactField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }, 100);
                        return false;
                    }
                }
            }

            console.log('Personal Info step validation passed (including passenger table)');
            return true;

        case 2: // Date and Time
            console.log('Validating Date and Time step');

            // Check if date pickers are initialized
            if (!window.startDatePicker || !window.endDatePicker) {
                console.error('Date pickers not initialized properly');
                Swal.fire({
                    title: 'Error',
                    text: 'There was a problem with the date selection. Please refresh the page and try again.',
                    icon: 'error',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 5000,
                    timerProgressBar: true
                });
                return false;
            }

            // Get selected dates
            const startDateSelected = window.startDatePicker.selectedDates[0];
            const endDateSelected = window.endDatePicker.selectedDates[0];

            console.log('Selected dates:', {
                startDate: startDateSelected,
                endDate: endDateSelected
            });

            // Check if dates are selected
            if (!startDateSelected || !endDateSelected) {
                console.log('Missing date selection');
                Swal.fire({
                    title: 'Warning!',
                    text: 'Please select both start and end dates.',
                    icon: 'warning',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true
                });

                // Focus on the appropriate date field after a short delay
                setTimeout(() => {
                    if (!startDateSelected) {
                        document.getElementById('startDate').focus();
                        document.getElementById('startDate').scrollIntoView({ behavior: 'smooth', block: 'center' });
                    } else if (!endDateSelected) {
                        document.getElementById('endDate').focus();
                        document.getElementById('endDate').scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }, 100);
                return false;
            }

            // Check if end date is after start date
            if (endDateSelected < startDateSelected) {
                console.log('End date is before start date');
                Swal.fire({
                    title: 'Warning!',
                    text: 'End date must be after start date.',
                    icon: 'warning',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true
                });

                // Focus on the end date field after a short delay
                setTimeout(() => {
                    document.getElementById('endDate').focus();
                    document.getElementById('endDate').scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 100);
                return false;
            }

            console.log('Date and Time step validation passed');
            return true;

        case 3: // Payment
            const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked');
            if (!paymentMethod) {
                Swal.fire({
                    title: 'Warning!',
                    text: 'Please select a payment method.',
                    icon: 'warning',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true
                });

                // Focus on payment methods after a short delay
                setTimeout(() => {
                    // Scroll to the payment methods section
                    document.querySelector('.payment-methods').scrollIntoView({ behavior: 'smooth', block: 'center' });
                    // Try to focus on the first payment option
                    const firstPaymentOption = document.getElementById('gcash');
                    if (firstPaymentOption) {
                        firstPaymentOption.focus();
                    }
                }, 100);
                return false;
            }

            // If GCash is selected, validate proof of payment upload
            if (paymentMethod.value === 'GCash') {
                const proofUpload = document.getElementById('gcashProofUpload');
                if (!proofUpload || !proofUpload.files[0]) {
                    Swal.fire({
                        title: 'Proof of Payment Required!',
                        text: 'Please upload your GCash payment receipt or screenshot.',
                        icon: 'warning',
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 5000,
                        timerProgressBar: true
                    });

                    // Focus on upload section after a short delay
                    setTimeout(() => {
                        // Scroll to the upload section
                        document.querySelector('.proof-of-payment-section').scrollIntoView({ behavior: 'smooth', block: 'center' });
                        // Focus on the upload input
                        if (proofUpload) {
                            proofUpload.focus();
                        }
                    }, 100);
                    return false;
                }
            }

            return true;

        default:
            return true;
    }
}

// Reset booking steps
function resetBookingSteps() {
    // Remove .active from all steps
    document.querySelectorAll('.booking-step').forEach(step => step.classList.remove('active'));
    // Set Step 1 active
    document.getElementById('step1').classList.add('active');
    // Reset progress bar
    document.getElementById('progressBar').style.width = '0%';
    // Reset step indicators
    document.querySelectorAll('.step').forEach((step, i) => {
        if (i === 0) step.classList.add('active');
        else step.classList.remove('active');
    });
}

// Clear form data
function clearFormData() {
    document.getElementById('bookingForm').reset();
    resetBookingSteps();
    calculateTotalFees();
    updateDateTimeSummary();
    generateBookingId();
}

// Reset form with confirmation dialog
function resetFormWithConfirmation() {
    Swal.fire({
        title: 'Reset Form?',
        text: 'Are you sure you want to reset the form? All entered data will be lost.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, Reset',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6'
    }).then((result) => {
        if (result.isConfirmed) {
            clearFormData();
            // Clear localStorage as well
            localStorage.removeItem('bookingFormData');

            Swal.fire({
                title: 'Form Reset!',
                text: 'The form has been reset successfully.',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
        }
    });
}

// Get passenger data from dynamically generated fields
function getPassengerData() {
    const passengerData = {};
    const totalPassengers = parseInt(document.getElementById('totalPassengers')?.value) || 1;

    // Get data for companions only (passenger 2 onwards)
    for (let i = 2; i <= totalPassengers; i++) {
        const firstName = document.getElementById(`passenger${i}FirstName`)?.value || '';
        const lastName = document.getElementById(`passenger${i}LastName`)?.value || '';
        const age = document.getElementById(`passenger${i}Age`)?.value || '';
        const sex = document.getElementById(`passenger${i}Sex`)?.value || '';
        const completeAddress = document.getElementById(`passenger${i}CompleteAddress`)?.value || '';
        const contactNumber = document.getElementById(`passenger${i}ContactNumber`)?.value || '';

        if (firstName || lastName || age || sex || completeAddress || contactNumber) {
            passengerData[`passenger${i}`] = {
                firstName,
                lastName,
                age,
                sex,
                completeAddress,
                contactNumber
            };
        }
    }

    return passengerData;
}

// Save form data to localStorage
function saveFormData() {
    console.log('Saving form data to localStorage...');

    // Ensure the selectedBoat input exists
    let selectedBoatInput = document.getElementById('selectedBoat');
    if (!selectedBoatInput) {
        console.log('Creating missing selectedBoat input');
        selectedBoatInput = document.createElement('input');
        selectedBoatInput.type = 'hidden';
        selectedBoatInput.id = 'selectedBoat';
        selectedBoatInput.name = 'selectedBoat';
        document.getElementById('bookingForm').appendChild(selectedBoatInput);

        // Set default value if empty
        if (!selectedBoatInput.value) {
            selectedBoatInput.value = 'DefaultBoat';
        }
    }

    // Ensure the bookingId input exists
    const bookingIdInput = document.getElementById('bookingId');
    if (!bookingIdInput) {
        console.log('Creating missing bookingId input');
        const newBookingIdInput = document.createElement('input');
        newBookingIdInput.type = 'hidden';
        newBookingIdInput.id = 'bookingId';
        newBookingIdInput.name = 'bookingId';
        document.getElementById('bookingForm').appendChild(newBookingIdInput);

        // Generate a booking ID
        generateBookingId();
    } else if (!bookingIdInput.value) {
        // Generate a booking ID if it doesn't exist
        generateBookingId();
    }

    // Always set to "AssignedByTourismOffice" regardless of URL parameters
    selectedBoatInput.value = 'AssignedByTourismOffice';

    const formData = {
        // Personal information
        firstName: document.getElementById('firstName')?.value || '',
        lastName: document.getElementById('lastName')?.value || '',
        age: document.getElementById('age')?.value || '',
        sex: document.getElementById('sex')?.value || '',
        contactNumber: document.getElementById('contactNumber')?.value || '',
        emailAddress: document.getElementById('emailAddress')?.value || '',
        completeAddress: document.getElementById('completeAddress')?.value || '',
        emergencyName: document.getElementById('emergencyName')?.value || '',
        emergencyNumber: document.getElementById('emergencyNumber')?.value || '',
        locationTourDestination: document.getElementById('locationTourDestination')?.value || '',
        dropOffLocation: document.getElementById('dropOffLocation')?.value || '',
        numberOfPax: document.getElementById('numberOfPax')?.value || '',

        // Environmental fees
        regularPax: document.getElementById('regularPax')?.value || '',
        discountedPax: document.getElementById('discountedPax')?.value || '',
        childrenPax: document.getElementById('childrenPax')?.value || '',
        infantsPax: document.getElementById('infantsPax')?.value || '',

        // Dates
        startDate: document.getElementById('startDate')?.value || '',
        endDate: document.getElementById('endDate')?.value || '',

        // Payment method
        payment: document.querySelector('input[name="paymentMethod"]:checked')?.value || '',

        // Boat information - always assigned by tourism office
        selectedBoat: 'AssignedByTourismOffice',
        bookingId: document.getElementById('bookingId')?.value || '',

        // Current step
        currentStep: currentStep,

        // Flag to indicate user has entered data
        hasUserEnteredData: (() => {
            const hasData = hasUserEnteredData();
            console.log('Saving form data - hasUserEnteredData:', hasData);
            return hasData;
        })(),

        // Passenger data
        totalPassengers: document.getElementById('totalPassengers')?.value || '1',
        passengerData: getPassengerData()
    };

    console.log('Form data being saved');

    try {
        // Validate formData before saving
        if (!formData || typeof formData !== 'object') {
            console.error('Invalid form data object');
            return;
        }

        // Convert to JSON string
        const formDataString = JSON.stringify(formData);

        // Validate JSON string
        if (!formDataString || formDataString === 'undefined' || formDataString === 'null' || formDataString === '{}') {
            console.error('Invalid JSON string generated');
            return;
        }

        // Save to localStorage
        localStorage.setItem('bookingFormData', formDataString);
        console.log('Form data saved to localStorage successfully');
    } catch (error) {
        console.error('Error saving form data:', error);
        // Don't show an error to the user, just log it
    }

    // Boat information removed - will be assigned by tourism office

    const step1BookingId = document.getElementById('step1BookingId');
    if (step1BookingId && formData.bookingId) {
        step1BookingId.textContent = formData.bookingId;
    }

    // Boat information removed - will be assigned by tourism office

    const summaryBookingId = document.getElementById('summaryBookingId');
    if (summaryBookingId && formData.bookingId) {
        summaryBookingId.textContent = formData.bookingId;
    }
}

// Check if user has entered any meaningful data
function hasUserEnteredData() {
    // Check for user-entered data in important fields
    const firstName = document.getElementById('firstName')?.value || '';
    const lastName = document.getElementById('lastName')?.value || '';
    const email = document.getElementById('emailAddress')?.value || '';
    const phone = document.getElementById('contactNumber')?.value || '';
    const address = document.getElementById('completeAddress')?.value || '';
    const emergencyName = document.getElementById('emergencyName')?.value || '';
    const emergencyNumber = document.getElementById('emergencyNumber')?.value || '';
    const dropOffLocation = document.getElementById('dropOffLocation')?.value || '';
    const startDate = document.getElementById('startDate')?.value || '';
    const endDate = document.getElementById('endDate')?.value || '';

    // Return true if any important field has user data
    return firstName !== '' ||
           lastName !== '' ||
           email !== '' ||
           phone !== '' ||
           address !== '' ||
           emergencyName !== '' ||
           emergencyNumber !== '' ||
           dropOffLocation !== '' ||
           startDate !== '' ||
           endDate !== '';
}

// Load form data from localStorage
function loadFormData() {
    console.log('Loading form data from localStorage...');

    try {
        const savedData = localStorage.getItem('bookingFormData');
        if (!savedData || savedData === 'undefined' || savedData === 'null') {
            console.log('No valid saved form data found');
            return;
        }

        // Validate that the saved data is a proper JSON string
        if (typeof savedData !== 'string' || !savedData.startsWith('{')) {
            console.error('Invalid saved form data format');
            localStorage.removeItem('bookingFormData');
            return;
        }

        console.log('Parsing saved form data');
        const formData = JSON.parse(savedData);

        // Validate that formData is an object
        if (!formData || typeof formData !== 'object') {
            console.error('Parsed form data is not an object');
            localStorage.removeItem('bookingFormData');
            return;
        }

        console.log('Parsed form data successfully');

        // Ensure the form exists
        const bookingForm = document.getElementById('bookingForm');
        if (!bookingForm) {
            console.error('Booking form not found!');
            return;
        }

        // Fill personal information
        if (formData.firstName) document.getElementById('firstName').value = formData.firstName;
        if (formData.lastName) document.getElementById('lastName').value = formData.lastName;
        if (formData.age) document.getElementById('age').value = formData.age;
        if (formData.gender) document.getElementById('gender').value = formData.gender;
        if (formData.contactNumber) document.getElementById('contactNumber').value = formData.contactNumber;
        if (formData.emailAddress) document.getElementById('emailAddress').value = formData.emailAddress;
        if (formData.completeAddress) document.getElementById('completeAddress').value = formData.completeAddress;
        if (formData.emergencyName) document.getElementById('emergencyName').value = formData.emergencyName;
        if (formData.emergencyNumber) document.getElementById('emergencyNumber').value = formData.emergencyNumber;
        if (formData.locationTourDestination) document.getElementById('locationTourDestination').value = formData.locationTourDestination;
        if (formData.dropOffLocation) document.getElementById('dropOffLocation').value = formData.dropOffLocation;
        if (formData.numberOfPax) document.getElementById('numberOfPax').value = formData.numberOfPax;

        // Fill environmental fees
        if (formData.regularPax) document.getElementById('regularPax').value = formData.regularPax;
        if (formData.discountedPax) document.getElementById('discountedPax').value = formData.discountedPax;
        if (formData.childrenPax) document.getElementById('childrenPax').value = formData.childrenPax;
        if (formData.infantsPax) document.getElementById('infantsPax').value = formData.infantsPax;

        // Fill dates
        if (formData.startDate) {
            const startDateInput = document.getElementById('startDate');
            if (startDateInput) {
                startDateInput.value = formData.startDate;
                // If we have flatpickr initialized, update it too
                if (window.startDatePicker) {
                    window.startDatePicker.setDate(formData.startDate);
                }
            }
        }

        if (formData.endDate) {
            const endDateInput = document.getElementById('endDate');
            if (endDateInput) {
                endDateInput.value = formData.endDate;
                // If we have flatpickr initialized, update it too
                if (window.endDatePicker) {
                    window.endDatePicker.setDate(formData.endDate);
                }
            }
        }

        // Select payment method
        if (formData.payment) {
            const paymentRadio = document.querySelector(`input[name="paymentMethod"][value="${formData.payment}"]`);
            if (paymentRadio) {
                paymentRadio.checked = true;

                // Show the appropriate payment details
                document.getElementById('gcashDetails').style.display = formData.payment === 'GCash' ? 'block' : 'none';
                document.getElementById('manualDetails').style.display = formData.payment === 'Manual Payment' ? 'block' : 'none';

                // Update payment method in summary
                const summaryPaymentValue = document.getElementById('summaryPaymentValue');
                if (summaryPaymentValue) {
                    summaryPaymentValue.textContent = formData.payment;
                }

                console.log('Restored payment method:', formData.payment);
            }
        }

        // Restore passenger data
        if (formData.totalPassengers) {
            const totalPassengersInput = document.getElementById('totalPassengers');
            const numberOfPaxInput = document.getElementById('numberOfPax');

            console.log('Restoring passenger count:', formData.totalPassengers);

            if (totalPassengersInput) {
                totalPassengersInput.value = formData.totalPassengers;
                console.log('Set totalPassengers to:', totalPassengersInput.value);

                // Also update numberOfPax to match
                if (numberOfPaxInput) {
                    numberOfPaxInput.value = formData.totalPassengers;
                    console.log('Set numberOfPax to:', numberOfPaxInput.value);
                }

                // Force update passenger forms to sync everything
                updatePassengerForms();

                // Regenerate passenger table with saved count
                generatePassengerInputTable(parseInt(formData.totalPassengers));

                // Restore individual passenger data
                if (formData.passengerData) {
                    setTimeout(() => {
                        console.log('Restoring passenger data:', formData.passengerData);
                        Object.keys(formData.passengerData).forEach(passengerKey => {
                            const passengerInfo = formData.passengerData[passengerKey];
                            const passengerNumber = passengerKey.replace('passenger', '');

                            console.log(`Restoring passenger ${passengerNumber}:`, passengerInfo);

                            // Restore each field
                            const firstNameField = document.getElementById(`passenger${passengerNumber}FirstName`);
                            const lastNameField = document.getElementById(`passenger${passengerNumber}LastName`);
                            const ageField = document.getElementById(`passenger${passengerNumber}Age`);
                            const sexField = document.getElementById(`passenger${passengerNumber}Sex`);
                            const addressField = document.getElementById(`passenger${passengerNumber}CompleteAddress`);
                            const contactField = document.getElementById(`passenger${passengerNumber}ContactNumber`);

                            if (firstNameField) {
                                firstNameField.value = passengerInfo.firstName || '';
                                console.log(`Restored firstName for passenger ${passengerNumber}:`, firstNameField.value);
                            }
                            if (lastNameField) {
                                lastNameField.value = passengerInfo.lastName || '';
                                console.log(`Restored lastName for passenger ${passengerNumber}:`, lastNameField.value);
                            }
                            if (ageField) {
                                ageField.value = passengerInfo.age || '';
                                console.log(`Restored age for passenger ${passengerNumber}:`, ageField.value);
                            }
                            if (sexField) {
                                sexField.value = passengerInfo.sex || '';
                                console.log(`Restored sex for passenger ${passengerNumber}:`, sexField.value);
                            }
                            if (addressField) {
                                addressField.value = passengerInfo.completeAddress || '';
                                console.log(`Restored address for passenger ${passengerNumber}:`, addressField.value);
                            }
                            if (contactField) {
                                contactField.value = passengerInfo.contactNumber || '';
                                console.log(`Restored contact for passenger ${passengerNumber}:`, contactField.value);
                            }
                        });
                    }, 500); // Increased delay to ensure table is fully generated
                }
            }
        }

        // Restore boat information
        // Make sure the selectedBoat input exists
        let selectedBoatInput = document.getElementById('selectedBoat');
        if (!selectedBoatInput) {
            console.log('Creating missing selectedBoat input during load');
            selectedBoatInput = document.createElement('input');
            selectedBoatInput.type = 'hidden';
            selectedBoatInput.id = 'selectedBoat';
            selectedBoatInput.name = 'selectedBoat';
            bookingForm.appendChild(selectedBoatInput);
        }

        // Always set to "AssignedByTourismOffice" regardless of localStorage or URL parameters
        console.log('Setting boat to be assigned by tourism office during form data load');
        selectedBoatInput.value = 'AssignedByTourismOffice';

        // Make sure the bookingId input exists
        const bookingIdInput = document.getElementById('bookingId');
        if (!bookingIdInput) {
            console.log('Creating missing bookingId input during load');
            const newBookingIdInput = document.createElement('input');
            newBookingIdInput.type = 'hidden';
            newBookingIdInput.id = 'bookingId';
            newBookingIdInput.name = 'bookingId';
            bookingForm.appendChild(newBookingIdInput);
        }

        // Set the value from localStorage if available, otherwise generate a new one
        if (formData.bookingId) {
            console.log('Setting bookingId from localStorage:', formData.bookingId);
            const existingBookingId = document.getElementById('bookingId');
            if (existingBookingId) {
                existingBookingId.value = formData.bookingId;
            }
        } else {
            console.log('Generating new bookingId');
            generateBookingId();
        }

        // Update calculations
        calculateTotalFees();
        updateDateTimeSummary();
        updateBookingSummary();


        // Don't display booking ID in the form as it's controlled by admin
        // The booking ID is still stored in the hidden input field

        // Step restoration is now handled by restoreSavedStep() function called during initialization

        // Only show the notification if the user has actually entered data
        // Use the flag we saved that indicates user has entered data
        console.log('Checking if should show restore notification:', formData.hasUserEnteredData);
        if (formData.hasUserEnteredData === true) {
            console.log('Showing form data restored notification');
            // Show a prominent notification that data was restored
            Swal.fire({
                title: '📋 Form Data Restored!',
                text: 'Your previously entered booking information has been automatically restored. You can continue where you left off.',
                icon: 'success',
                confirmButtonText: 'Continue Booking',
                confirmButtonColor: '#28a745',
                allowOutsideClick: false,
                allowEscapeKey: false
            });
        } else {
            console.log('Not showing restore notification - no user data detected');
        }

        // Save the form data again to ensure everything is properly saved
        // This helps with any fields that might have been updated during the loading process
        setTimeout(saveFormData, 500);

        console.log('Form data successfully loaded from localStorage');
    } catch (error) {
        console.error('Error loading saved form data:', error);
        console.error('Error details:', error.message);
        console.error('Error stack:', error.stack);

        // Clear potentially corrupted data
        localStorage.removeItem('bookingFormData');

        // Check if there's any user-entered data in the form already
        const hasFormData = hasUserEnteredData();

        // Only show error notification if there's no data in the form
        // This prevents showing the error when the form already has content
        if (!hasFormData) {
            // Show error notification
            Swal.fire({
                title: 'Notice',
                text: 'Starting with a fresh form.',
                icon: 'info',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });
        }
    }
}

// Override the clearFormData function to also clear localStorage
const originalClearFormData = clearFormData;
clearFormData = function() {
    // Call the original function
    originalClearFormData();

    // Clear localStorage
    localStorage.removeItem('bookingFormData');
};

// Enhanced Validation Functions
function validateField(input) {
    const fieldId = input.id;
    const value = input.value.trim();
    const validIcon = document.getElementById(fieldId + '-valid');
    const invalidIcon = document.getElementById(fieldId + '-invalid');

    if (!validIcon || !invalidIcon) return;

    let isValid = false;

    // Get parent input-field container
    const inputField = input.closest('.input-field');

    // Handle empty fields
    if (value === '') {
        if (input.hasAttribute('required')) {
            // Required field is empty - show invalid
            isValid = false;
        } else {
            // Optional field is empty - hide both icons
            if (inputField) {
                inputField.classList.remove('is-valid', 'is-invalid', 'has-validation');
            }
            return;
        }
    } else {
        // Field has value - validate based on field type and pattern
        switch(fieldId) {
            case 'firstName':
            case 'lastName':
            case 'emergencyName':
                isValid = /^[A-Za-z\s]+$/.test(value) && value.length >= 2;
                break;
            case 'age':
                const age = parseInt(value);
                isValid = age >= 1 && age <= 120;
                break;
            case 'contactNumber':
            case 'emergencyNumber':
                isValid = /^09[0-9]{9}$/.test(value);
                break;
            case 'emailAddress':
                // Email is now required
                if (value === '') {
                    isValid = false;
                } else {
                    isValid = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value);
                }
                break;
            case 'sex':
                isValid = value !== '';
                break;
            case 'completeAddress':
                // Updated validation - flexible format (with or without comma)
                isValid = value.length >= 15;
                break;
            default:
                isValid = value.length > 0;
        }
    }

    // Show appropriate icon and update field styling
    if (inputField) {
        inputField.classList.add('has-validation');

        if (isValid) {
            inputField.classList.add('is-valid');
            inputField.classList.remove('is-invalid');
        } else {
            inputField.classList.add('is-invalid');
            inputField.classList.remove('is-valid');
        }
    }
}

function validateAge(input) {
    let value = input.value;

    // Remove any non-numeric characters (including 'e', '+', '-', '.')
    value = value.replace(/[^0-9]/g, '');

    // Convert to number and check if it exceeds 120
    const age = parseInt(value);
    if (age > 120) {
        // Reset to 120 and show alert
        value = '120';
        input.value = value;

        Swal.fire({
            title: 'Oops!',
            text: 'Please enter valid number 1 to 120',
            icon: 'warning',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
        });
    } else {
        // Update the input value normally
        input.value = value;
    }

    // Validate age range
    const finalAge = parseInt(input.value);
    if (input.value === '' || finalAge < 1 || finalAge > 120) {
        input.setCustomValidity('Age must be between 1 and 120');
    } else {
        input.setCustomValidity('');
    }

    // Trigger field validation for icon display
    validateField(input);
}

function validateEmailWithAlert(input) {
    const email = input.value.trim();
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

    if (email === '') {
        // Email is required
        input.setCustomValidity('Email address is required');
    } else if (!emailRegex.test(email)) {
        input.setCustomValidity('Please enter a valid email address');

        // Show toast alert for invalid email (especially missing @)
        if (email !== '' && !email.includes('@')) {
            Swal.fire({
                title: 'Oops!',
                text: 'Please enter valid email with @ symbol',
                icon: 'warning',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
        } else if (email !== '' && !emailRegex.test(email)) {
            Swal.fire({
                title: 'Oops!',
                text: 'Please enter valid email address',
                icon: 'warning',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
        }
    } else {
        input.setCustomValidity('');
    }

    // Trigger field validation for icon display
    validateField(input);
}

function validateAddressWithAlert(input) {
    const address = input.value.trim();

    if (address === '') {
        input.setCustomValidity('Complete address is required');
    } else if (address.length < 15) {
        input.setCustomValidity('Please enter a complete address with street, barangay, city, province');
    } else {
        input.setCustomValidity('');
    }

    // Trigger field validation for icon display
    validateField(input);
}

// Passenger Count Management Functions
function increasePassengerCount() {
    const input = document.getElementById('totalPassengers');
    const numberOfPaxInput = document.getElementById('numberOfPax');
    const currentValue = parseInt(input.value);

    if (currentValue < 25) {
        const newValue = currentValue + 1;
        input.value = newValue;
        numberOfPaxInput.value = newValue;
        updatePassengerForms();

        // Sync environmental fees with new passenger count
        syncEnvironmentalFeesWithPassengers(newValue);
        updateFeeButtonStates();

        console.log(`Increased passengers: ${currentValue} → ${newValue}`);
    }
}

function decreasePassengerCount() {
    const input = document.getElementById('totalPassengers');
    const numberOfPaxInput = document.getElementById('numberOfPax');
    const currentValue = parseInt(input.value);

    if (currentValue > 1) {
        const newValue = currentValue - 1;
        input.value = newValue;
        numberOfPaxInput.value = newValue;
        updatePassengerForms();

        // Update environmental fees button states for new passenger count
        updateFeeButtonStates();

        console.log(`Decreased passengers: ${currentValue} → ${newValue}`);
    }
}

function updatePassengerForms() {
    const totalPassengers = parseInt(document.getElementById('totalPassengers').value) || 1;

    // Generate table input (Dean's actual request)
    generatePassengerInputTable(totalPassengers);
}

// Fee Count Management Functions
function increaseFeeCount(feeType) {
    const input = document.getElementById(feeType);
    const currentValue = parseInt(input.value);
    const totalPassengers = parseInt(document.getElementById('totalPassengers').value);

    // Calculate current total of all fee categories
    const regularPax = parseInt(document.getElementById('regularPax').value) || 0;
    const discountedPax = parseInt(document.getElementById('discountedPax').value) || 0;
    const childrenPax = parseInt(document.getElementById('childrenPax').value) || 0;
    const infantsPax = parseInt(document.getElementById('infantsPax').value) || 0;
    const currentTotal = regularPax + discountedPax + childrenPax + infantsPax;

    // Only allow increase if under the passenger limit
    if (currentTotal < totalPassengers) {
        input.value = currentValue + 1;
        calculateTotalFees();
        updateFeeButtonStates();

        console.log(`Increased ${feeType}: ${currentValue} → ${currentValue + 1} (Total: ${currentTotal + 1}/${totalPassengers})`);
    } else {
        console.log(`Cannot increase ${feeType}: At passenger limit (${currentTotal}/${totalPassengers})`);
    }
}

function decreaseFeeCount(feeType) {
    const input = document.getElementById(feeType);
    const currentValue = parseInt(input.value);

    // Simple decrease - allow going to zero for any category
    if (currentValue > 0) {
        input.value = currentValue - 1;
        calculateTotalFees();
        updateFeeButtonStates();

        console.log(`Decreased ${feeType}: ${currentValue} → ${currentValue - 1}`);
    }
}

// Update button states based on current selections
function updateFeeButtonStates() {
    const totalPassengers = parseInt(document.getElementById('totalPassengers').value) || 1;
    const regularPax = parseInt(document.getElementById('regularPax').value) || 0;
    const discountedPax = parseInt(document.getElementById('discountedPax').value) || 0;
    const childrenPax = parseInt(document.getElementById('childrenPax').value) || 0;
    const infantsPax = parseInt(document.getElementById('infantsPax').value) || 0;
    const currentTotal = regularPax + discountedPax + childrenPax + infantsPax;

    console.log(`Updating button states: Total passengers: ${totalPassengers}, Current fee total: ${currentTotal}`);

    // Update each fee type individually
    const feeTypes = [
        { id: 'regularPax', value: regularPax },
        { id: 'discountedPax', value: discountedPax },
        { id: 'childrenPax', value: childrenPax },
        { id: 'infantsPax', value: infantsPax }
    ];

    feeTypes.forEach(feeType => {
        // Find plus and minus buttons for this fee type
        const plusBtn = document.querySelector(`button[onclick="increaseFeeCount('${feeType.id}')"]`);
        const minusBtn = document.querySelector(`button[onclick="decreaseFeeCount('${feeType.id}')"]`);

        // Update plus button state
        if (plusBtn) {
            if (currentTotal < totalPassengers) {
                // Under limit - always allow
                plusBtn.disabled = false;
                console.log(`Enabled plus button for ${feeType.id} - under limit (${currentTotal}/${totalPassengers})`);
            } else {
                // At limit - disable to prevent exceeding
                plusBtn.disabled = true;
                console.log(`Disabled plus button for ${feeType.id} - at limit (${currentTotal}/${totalPassengers})`);
            }
        }

        // Update minus button state
        if (minusBtn) {
            if (feeType.value <= 0) {
                minusBtn.disabled = true;
                console.log(`Disabled minus button for ${feeType.id} - value is 0`);
            } else {
                // Allow minus for all categories - user can choose which category to use
                minusBtn.disabled = false;
                console.log(`Enabled minus button for ${feeType.id}`);
            }
        }
    });
}

// Sync environmental fees with passenger count (from project notes)
function syncEnvironmentalFeesWithPassengers(totalPassengers) {
    console.log(`Syncing environmental fees with total passengers: ${totalPassengers}`);

    // Get current environmental fees
    const regularPax = parseInt(document.getElementById('regularPax')?.value) || 0;
    const discountedPax = parseInt(document.getElementById('discountedPax')?.value) || 0;
    const childrenPax = parseInt(document.getElementById('childrenPax')?.value) || 0;
    const infantsPax = parseInt(document.getElementById('infantsPax')?.value) || 0;

    const currentFeesTotal = regularPax + discountedPax + childrenPax + infantsPax;

    // Only auto-adjust if environmental fees are currently 0 (first time setup)
    // DO NOT auto-adjust if user has already set a breakdown - preserve their choices
    if (currentFeesTotal === 0) {
        // Set all passengers as regular adults by default only when starting fresh
        document.getElementById('regularPax').value = totalPassengers;
        document.getElementById('discountedPax').value = 0;
        document.getElementById('childrenPax').value = 0;
        document.getElementById('infantsPax').value = 0;

        // Recalculate fees
        calculateTotalFees();
        updateFeeButtonStates();

        console.log(`Auto-adjusted environmental fees to match ${totalPassengers} passengers (first time setup)`);
    } else {
        // User has already set environmental fees - preserve their breakdown
        console.log(`Preserving existing environmental fees breakdown: ${regularPax} adults, ${discountedPax} PWD, ${childrenPax} children, ${infantsPax} infants`);
    }
}

// Additional Validation Functions for Passenger Table
function validateInputWithAlert(input, regex, type) {
    input.value = input.value.replace(regex, '');
}

function validatePassengerContactInput(input) {
    // Allow only numbers and limit to 12 characters
    input.value = input.value.replace(/[^0-9]/g, '');
    if (input.value.length > 12) {
        input.value = input.value.substring(0, 12);
    }
}

function validateContactNumber(input) {
    const value = input.value.trim();
    if (value && !value.match(/^09[0-9]{9}$/)) {
        input.setCustomValidity('Please enter a valid 11-digit Philippine mobile number starting with 09');
    } else {
        input.setCustomValidity('');
    }
}

function validateContactNumberWithAlert(input) {
    let value = input.value;

    // Remove any non-numeric characters
    value = value.replace(/[^0-9]/g, '');

    // Check if reaches or exceeds 11 digits
    if (value.length >= 11) {
        // Limit to 11 digits and show alert when reaching limit
        if (value.length > 11) {
            value = value.substring(0, 11);
        }
        input.value = value;

        // Show alert when reaching 11 digits
        if (value.length === 11) {
            Swal.fire({
                title: 'Oops!',
                text: 'Maximum 11 digits reached for mobile number',
                icon: 'warning',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
        }
    } else if (value.length >= 2 && !value.startsWith('09')) {
        // Check if doesn't start with 09 (Philippine mobile format)
        Swal.fire({
            title: 'Oops!',
            text: 'Please enter valid Philippine number starting with 09',
            icon: 'warning',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
        });
    } else {
        // Update the input value normally
        input.value = value;
    }

    // Trigger field validation for icon display
    validateField(input);
}

// Generate passenger input table for COMPANIONS ONLY (Dean's ACTUAL request)
function generatePassengerInputTable(totalPassengers) {
    const container = document.getElementById('passengerInputContainer');
    if (!container) return;

    // Validate passenger limit (including main booker)
    if (totalPassengers > 25) {
        Swal.fire({
            title: 'Too Many Passengers!',
            text: 'Oops! Please enter 25 passengers only',
            icon: 'warning',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
        });
        document.getElementById('totalPassengers').value = 25;
        return;
    }

    // Clear existing content
    container.innerHTML = '';

    // Calculate companions (total passengers - 1 main booker)
    const companions = totalPassengers - 1;

    if (companions <= 0) {
        // Only main booker, no additional passengers
        container.innerHTML = `
            <div style="background: #f0f8ff; border: 1px solid #b3d9ff; border-radius: 8px; padding: 20px; margin-top: 20px; text-align: center;">
                <h5 style="color: #00a8b5; margin-bottom: 10px;">
                    <i class="fas fa-user" style="margin-right: 8px;"></i>Solo Traveler
                </h5>
                <p style="margin: 0; color: #666;">
                    Only the main booker (you) will be traveling. No other passengers to declare.
                </p>
            </div>
        `;
        return;
    }

    // Create table input HTML for COMPANIONS ONLY (Dean's "NAKA TABLE LANG SYA")
    let tableHTML = `
        <div style="background: white; border: 1px solid #ddd; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-top: 20px;">
            <h5 style="color: #00a8b5; margin-bottom: 15px; text-align: center;">
                <i class="fas fa-users" style="margin-right: 8px;"></i>Additional Passengers Table (${companions} additional passengers)
            </h5>
            <div style="margin-bottom: 15px; padding: 10px; background: #e8f4f8; border-left: 4px solid #17a2b8; border-radius: 4px;">
                <strong>Note:</strong> The main booker's information is already filled in the form above.
                This table is for your <strong>${companions} additional passenger(s)</strong> only.
            </div>
            <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
                    <thead>
                        <tr style="background-color: #00a8b5; color: white;">
                            <th style="border: 1px solid #ddd; padding: 10px; text-align: center; width: 50px;">No.</th>
                            <th style="border: 1px solid #ddd; padding: 10px; width: 150px;">First Name *</th>
                            <th style="border: 1px solid #ddd; padding: 10px; width: 150px;">Last Name *</th>
                            <th style="border: 1px solid #ddd; padding: 10px; width: 80px;">Age *</th>
                            <th style="border: 1px solid #ddd; padding: 10px; width: 100px;">Gender *</th>
                            <th style="border: 1px solid #ddd; padding: 10px; width: 200px;">Complete Address *</th>
                            <th style="border: 1px solid #ddd; padding: 10px; width: 130px;">Contact (Optional)</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    // Generate table rows for COMPANIONS only (starting from passenger2)
    for (let i = 1; i <= companions; i++) {
        const passengerNumber = i + 1; // passenger2, passenger3, etc.
        const rowStyle = i % 2 === 0 ? 'background-color: #f9f9f9;' : '';

        tableHTML += `
            <tr style="${rowStyle}">
                <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-weight: bold;">
                    ${i}<br><small style="color: #666;">(Additional)</small>
                </td>
                <td style="border: 1px solid #ddd; padding: 5px;">
                    <input type="text" id="passenger${passengerNumber}FirstName" name="passenger${passengerNumber}FirstName"
                           placeholder="First name" required
                           style="width: 100%; border: 1px solid #ccc; padding: 5px; border-radius: 3px;"
                           pattern="[A-Za-z\\s]+" oninput="validateInputWithAlert(this, /[^A-Za-z\\s]/g, 'name')">
                </td>
                <td style="border: 1px solid #ddd; padding: 5px;">
                    <input type="text" id="passenger${passengerNumber}LastName" name="passenger${passengerNumber}LastName"
                           placeholder="Last name" required
                           style="width: 100%; border: 1px solid #ccc; padding: 5px; border-radius: 3px;"
                           pattern="[A-Za-z\\s]+" oninput="validateInputWithAlert(this, /[^A-Za-z\\s]/g, 'name')">
                </td>
                <td style="border: 1px solid #ddd; padding: 5px;">
                    <input type="number" id="passenger${passengerNumber}Age" name="passenger${passengerNumber}Age"
                           placeholder="Age" required min="1" max="120"
                           style="width: 100%; border: 1px solid #ccc; padding: 5px; border-radius: 3px;"
                           onblur="validateAge(this)">
                </td>
                <td style="border: 1px solid #ddd; padding: 5px;">
                    <select id="passenger${passengerNumber}Sex" name="passenger${passengerNumber}Sex" required
                            style="width: 100%; border: 1px solid #ccc; padding: 5px; border-radius: 3px;">
                        <option value="">Select</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                    </select>
                </td>
                <td style="border: 1px solid #ddd; padding: 5px;">
                    <input type="text" id="passenger${passengerNumber}CompleteAddress" name="passenger${passengerNumber}CompleteAddress"
                           placeholder="Enter complete address" required
                           style="width: 100%; border: 1px solid #ccc; padding: 5px; border-radius: 3px;">
                </td>
                <td style="border: 1px solid #ddd; padding: 5px;">
                    <input type="tel" id="passenger${passengerNumber}ContactNumber" name="passenger${passengerNumber}ContactNumber"
                           placeholder="09XXXXXXXXX (optional)"
                           style="width: 100%; border: 1px solid #ccc; padding: 5px; border-radius: 3px;"
                           maxlength="12" pattern="09[0-9]{9}"
                           oninput="validatePassengerContactInput(this)"
                           onblur="validateContactNumber(this)">
                </td>
            </tr>
        `;
    }

    tableHTML += `
                    </tbody>
                </table>
            </div>
            <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 5px; text-align: center;">
                <strong style="color: #28a745;">✅ Passengers Added: ${companions} additional passenger(s) + 1 main booker = ${totalPassengers} total passengers</strong>
                <br><small style="color: #666;">Fill all required fields (*) for additional passengers before proceeding</small>
            </div>
        </div>
    `;

    container.innerHTML = tableHTML;

    // Add event listeners to save data when passenger fields change
    for (let i = 2; i <= totalPassengers; i++) {
        const fields = ['FirstName', 'LastName', 'Age', 'Sex', 'CompleteAddress', 'ContactNumber'];
        fields.forEach(field => {
            const element = document.getElementById(`passenger${i}${field}`);
            if (element) {
                element.addEventListener('input', saveFormData);
                element.addEventListener('change', saveFormData);
            }
        });
    }
}

// Make functions available globally
window.nextStep = nextStep;
window.prevStep = prevStep;
window.calculateTotalFees = calculateTotalFees;
window.clearFormData = clearFormData;
window.resetFormWithConfirmation = resetFormWithConfirmation;
window.validateField = validateField;
window.validateAge = validateAge;
window.validateEmailWithAlert = validateEmailWithAlert;
window.validateAddressWithAlert = validateAddressWithAlert;
window.increasePassengerCount = increasePassengerCount;
window.decreasePassengerCount = decreasePassengerCount;
window.updatePassengerForms = updatePassengerForms;
window.increaseFeeCount = increaseFeeCount;
window.decreaseFeeCount = decreaseFeeCount;
window.updateFeeButtonStates = updateFeeButtonStates;
window.syncEnvironmentalFeesWithPassengers = syncEnvironmentalFeesWithPassengers;
window.saveCurrentStep = saveCurrentStep;
window.restoreSavedStep = restoreSavedStep;
window.validateDatesRealTime = validateDatesRealTime;
window.updateFieldValidation = updateFieldValidation;
window.validateInputWithAlert = validateInputWithAlert;
window.validatePassengerContactInput = validatePassengerContactInput;
window.validateContactNumber = validateContactNumber;
window.validateContactNumberWithAlert = validateContactNumberWithAlert;
window.generatePassengerInputTable = generatePassengerInputTable;
window.getPassengerData = getPassengerData;
window.validateAndProceed = validateAndProceed;
window.goToPaymentStep = goToPaymentStep;
window.setupFormSubmission = setupFormSubmission;
window.setupDateAndBookingId = setupDateAndBookingId;
window.setupMobileMenu = setupMobileMenu;
window.setupPaymentMethods = setupPaymentMethods;
window.handleProofUpload = handleProofUpload;
window.deleteProofUpload = deleteProofUpload;

// Additional JavaScript functions moved from HTML

// Handle proof of payment upload for GCash
function handleProofUpload(input) {
    const file = input.files[0];
    const uploadPreview = document.getElementById('uploadPreview');
    const uploadedFileName = document.getElementById('uploadedFileName');

    if (file) {
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedTypes.includes(file.type)) {
            Swal.fire({
                title: 'Invalid File Type',
                text: 'Please upload a JPEG, JPG, or PNG file.',
                icon: 'error',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
                timerProgressBar: true
            });
            input.value = ''; // Clear the input
            uploadPreview.style.display = 'none';
            return;
        }

        // Validate file size (5MB max)
        const maxSize = 5 * 1024 * 1024; // 5MB in bytes
        if (file.size > maxSize) {
            Swal.fire({
                title: 'File Too Large',
                text: 'Please upload a file smaller than 5MB.',
                icon: 'error',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 4000,
                timerProgressBar: true
            });
            input.value = ''; // Clear the input
            uploadPreview.style.display = 'none';
            return;
        }

        // Show upload preview
        uploadPreview.style.display = 'block';
        uploadedFileName.textContent = file.name + ' (' + (file.size / 1024 / 1024).toFixed(2) + ' MB)';

        // Save form data when file is uploaded
        saveFormData();

        console.log('Proof of payment uploaded:', file.name);
    } else {
        uploadPreview.style.display = 'none';
    }
}

// Delete proof of payment upload
function deleteProofUpload() {
    const input = document.getElementById('gcashProofUpload');
    const uploadPreview = document.getElementById('uploadPreview');

    Swal.fire({
        title: 'Delete Uploaded File?',
        text: 'Are you sure you want to delete the uploaded proof of payment?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes, Delete',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6'
    }).then((result) => {
        if (result.isConfirmed) {
            // Clear the file input
            input.value = '';

            // Hide the upload preview
            uploadPreview.style.display = 'none';

            // Save form data after deletion
            saveFormData();

            Swal.fire({
                title: 'File Deleted!',
                text: 'The uploaded file has been removed.',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });

            console.log('Proof of payment deleted');
        }
    });
}

// Form submission handler
function setupFormSubmission() {
    const bookingForm = document.getElementById('bookingForm');
    if (bookingForm) {
        bookingForm.addEventListener('submit', function(e) {
            // Prevent the default form submission
            e.preventDefault();

            // Make sure all required fields are filled
            var requiredFields = document.querySelectorAll('[required]');
            var allFilled = true;

            requiredFields.forEach(function(field) {
                if (!field.value) {
                    allFilled = false;
                    field.classList.add('error');
                } else {
                    field.classList.remove('error');
                }
            });

            if (!allFilled) {
                alert('Please fill in all required fields');
                return;
            }

            // Set the total amount
            var envFeeText = document.getElementById('summaryEnvFeeValue').textContent.replace('₱', '').replace(',', '') || '0.00';
            var envFee = parseFloat(envFeeText) || 0;
            document.getElementById('total').value = envFee;
            console.log('Setting total amount to:', envFee);

            // Force the current date for today's bookings
            var today = new Date();
            var dateString = today.getFullYear() + '-' +
                            String(today.getMonth() + 1).padStart(2, '0') + '-' +
                            String(today.getDate()).padStart(2, '0');
            document.getElementById('currentDate').value = dateString;

            // Set the start date to today to ensure it appears in Today's Bookings
            document.getElementById('startDate').value = dateString;

            // Submit the form
            console.log('Submitting booking form to send_verification_email.php...');
            this.submit();
        });
    }
}

// Date and booking ID setup
function setupDateAndBookingId() {
    var today = new Date();
    var dateString = today.getFullYear() + '-' +
                    String(today.getMonth() + 1).padStart(2, '0') + '-' +
                    String(today.getDate()).padStart(2, '0');

    const currentDateField = document.getElementById('currentDate');
    if (currentDateField) {
        currentDateField.value = dateString;
        console.log('Set current date to: ' + dateString);
    }

    // Generate booking ID with current date to ensure it appears in Today's Bookings
    var random = Math.floor(10000 + Math.random() * 90000);
    var bookingId = 'BOOKING-' + today.getFullYear() +
                   String(today.getMonth() + 1).padStart(2, '0') +
                   String(today.getDate()).padStart(2, '0') + '-' + random;

    const bookingIdField = document.getElementById('bookingId');
    if (bookingIdField) {
        bookingIdField.value = bookingId;
        console.log('Generated booking ID: ' + bookingId);
    }
}

// Validation and proceed function
function validateAndProceed(step) {
    // Simply call the nextStep function directly
    // The validateStep function in booking.js will handle the validation
    nextStep(step);
}

// Go to payment step function
function goToPaymentStep() {
    // Basic date validation
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    if (!startDate || !endDate) {
        alert('Please select both start and end dates.');
        return;
    }

    // Use the standard nextStep function to ensure proper step indicator updates
    nextStep(2);
}

// Mobile menu functionality
function setupMobileMenu() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (navbarToggler && navbarCollapse) {
        navbarToggler.addEventListener('click', function() {
            // Toggle the 'show' class on the navbar collapse
            navbarCollapse.classList.toggle('show');

            // Toggle the 'expanded' class on the toggler for styling
            this.classList.toggle('expanded');

            // Update aria-expanded attribute
            const expanded = navbarCollapse.classList.contains('show');
            this.setAttribute('aria-expanded', expanded);
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!navbarToggler.contains(event.target) &&
                !navbarCollapse.contains(event.target) &&
                navbarCollapse.classList.contains('show')) {
                navbarCollapse.classList.remove('show');
                navbarToggler.classList.remove('expanded');
                navbarToggler.setAttribute('aria-expanded', 'false');
            }
        });

        // Close menu when clicking on a nav link
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (navbarCollapse.classList.contains('show')) {
                    navbarCollapse.classList.remove('show');
                    navbarToggler.classList.remove('expanded');
                    navbarToggler.setAttribute('aria-expanded', 'false');
                }
            });
        });
    }
}

// Payment method selection
function setupPaymentMethods() {
    const gcashRadio = document.getElementById('gcash');
    const manualRadio = document.getElementById('manual');
    const gcashDetails = document.getElementById('gcashDetails');
    const manualDetails = document.getElementById('manualDetails');

    if (gcashRadio && manualRadio) {
        // Check if any payment method is already selected (from localStorage restoration)
        const currentlySelected = document.querySelector('input[name="paymentMethod"]:checked');

        if (!currentlySelected) {
            // Only set GCash as default if no payment method is already selected
            gcashRadio.checked = true;
            gcashDetails.style.display = 'block';
            manualDetails.style.display = 'none';

            // Update payment method in summary with default selection
            const summaryPaymentValue = document.getElementById('summaryPaymentValue');
            if (summaryPaymentValue) {
                summaryPaymentValue.textContent = gcashRadio.value;
            }

            console.log('Set GCash as default payment method (no previous selection)');
        } else {
            // Payment method already selected (restored from localStorage)
            // Show the appropriate details based on current selection
            if (currentlySelected.value === 'GCash') {
                gcashDetails.style.display = 'block';
                manualDetails.style.display = 'none';
                console.log('Maintained GCash selection from localStorage');
            } else if (currentlySelected.value === 'Manual Payment') {
                gcashDetails.style.display = 'none';
                manualDetails.style.display = 'block';
                console.log('Maintained Manual Payment selection from localStorage');
            }

            // Update summary with current selection
            const summaryPaymentValue = document.getElementById('summaryPaymentValue');
            if (summaryPaymentValue) {
                summaryPaymentValue.textContent = currentlySelected.value;
            }
        }

        gcashRadio.addEventListener('change', function() {
            if (this.checked) {
                gcashDetails.style.display = 'block';
                manualDetails.style.display = 'none';
                // Update payment method in summary
                if (summaryPaymentValue) {
                    summaryPaymentValue.textContent = this.value;
                }
            }
        });

        manualRadio.addEventListener('change', function() {
            if (this.checked) {
                manualDetails.style.display = 'block';
                gcashDetails.style.display = 'none';
                // Update payment method in summary
                if (summaryPaymentValue) {
                    summaryPaymentValue.textContent = this.value;
                }
            }
        });
    }
}
