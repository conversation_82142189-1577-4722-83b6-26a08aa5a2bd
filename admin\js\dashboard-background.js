/**
 * Dashboard Background Manager
 * Handles the background display for admin and subadmin dashboards
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get user role from the data attribute
    const userRole = document.body.getAttribute('data-user-role');

    // Create background element
    let backgroundElement = document.createElement('div');
    backgroundElement.id = 'dashboardBackground';
    backgroundElement.className = 'dashboard-background';

    // Set background (subadmin functionality has been removed)
    backgroundElement.classList.add('admin-background');

    // Add to body
    document.body.appendChild(backgroundElement);

    // Check if the background image exists
    function checkImageExists(imageUrl, callback) {
        const img = new Image();
        img.onload = function() { callback(true); };
        img.onerror = function() { callback(false); };
        img.src = imageUrl;
    }

    // Get the computed background image URL
    const bgStyle = window.getComputedStyle(backgroundElement);
    const bgImage = bgStyle.backgroundImage;

    // Extract the URL from the background-image property
    const urlMatch = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
    if (urlMatch && urlMatch[1]) {
        const imageUrl = urlMatch[1];

        // Check if the image exists
        checkImageExists(imageUrl, function(exists) {
            if (!exists) {
                // If image doesn't exist, add fallback class
                backgroundElement.classList.add('fallback');
                console.log('Background image not found, using fallback style');
            }
        });
    } else {
        // No background image set, use fallback
        backgroundElement.classList.add('fallback');
        console.log('No background image set, using fallback style');
    }
});
