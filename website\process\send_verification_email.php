<?php
// Super simple booking script for Carles Tourism Booking System

// Include database connection
require_once '../config/db_connect.php';

// Function to log debug information
function debug_log($message) {
    $log_file = 'logs/debug_' . date('Y-m-d') . '.log';
    $log_dir = dirname($log_file);

    // Create logs directory if it doesn't exist
    if (!file_exists($log_dir)) {
        mkdir($log_dir, 0777, true);
    }

    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[$timestamp] $message" . PHP_EOL;

    file_put_contents($log_file, $log_message, FILE_APPEND);
}

// Log start of script
debug_log('Script started');

// Get booking data from POST
$booking = $_POST;

// Debug log the booking data
debug_log('POST data received');

// Generate booking code
$booking_code = 'BOOKING-' . date('Ymd') . '-' . rand(10000, 99999);

// Get form data
$first_name = isset($booking['firstName']) ? $booking['firstName'] : '';
$last_name = isset($booking['lastName']) ? $booking['lastName'] : '';
$email = isset($booking['emailAddress']) ? $booking['emailAddress'] : '';
$contact = isset($booking['contactNumber']) ? $booking['contactNumber'] : '';
$destination = isset($booking['locationTourDestination']) ? $booking['locationTourDestination'] : '';
$pax = isset($booking['numberOfPax']) ? intval($booking['numberOfPax']) : 0;
$start_date = isset($booking['startDate']) ? $booking['startDate'] : date('Y-m-d');
$end_date = isset($booking['endDate']) ? $booking['endDate'] : date('Y-m-d');
$total = isset($booking['total']) ? floatval($booking['total']) : 0;

// Direct query - simplified for troubleshooting
$query = "INSERT INTO bookings (booking_code, first_name, last_name, email, contact_number,
          tour_destination, no_of_pax, start_date, end_date, booking_status, created_at, is_today_booking, total)
          VALUES ('$booking_code', '$first_name', '$last_name', '$email', '$contact',
          '$destination', $pax, '$start_date', '$end_date', 'pending', NOW(), 1, $total)";

debug_log('Executing query: ' . $query);

// Execute query
if ($con->query($query)) {
    $new_booking_id = $con->insert_id;
    debug_log('Booking inserted successfully with ID: ' . $new_booking_id);

    // Add notification for admin
    $notif_sql = "INSERT INTO notifications (user_id, message, type, reference_id, is_read, created_at)
                  VALUES (1, 'New booking #$booking_code from $first_name $last_name', 'new_booking', $new_booking_id, 0, NOW())";

    $con->query($notif_sql);

    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Booking saved successfully.',
        'booking_code' => $booking_code,
        'booking_id' => $new_booking_id
    ]);
} else {
    debug_log('Database error: ' . $con->error);
    echo json_encode([
        'success' => false,
        'message' => 'Error saving booking: ' . $con->error
    ]);
}
?>
