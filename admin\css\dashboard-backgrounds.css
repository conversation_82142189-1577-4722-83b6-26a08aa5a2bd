/**
 * Dashboard Backgrounds CSS
 * Styles for admin dashboard backgrounds
 * (subadmin functionality has been removed)
 */

/* Common background styles */
.dashboard-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.15; /* Subtle background */
    pointer-events: none; /* Don't interfere with clicks */
}

/* Admin background */
.admin-background {
    background-image: url('../images/admin-background.jpg');
}

/* Subadmin background - removed */

/* Fallback background if image is missing */
.dashboard-background.fallback {
    background-image: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}
