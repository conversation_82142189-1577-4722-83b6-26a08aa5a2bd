{"version": 3, "sources": ["webpack://bootstrap-colorpicker/webpack/universalModuleDefinition", "webpack://bootstrap-colorpicker/webpack/bootstrap", "webpack://bootstrap-colorpicker/external {\"root\":\"jQuery\",\"commonjs2\":\"jquery\",\"commonjs\":\"jquery\",\"amd\":\"jquery\"}", "webpack://bootstrap-colorpicker/./src/js/Extension.js", "webpack://bootstrap-colorpicker/./src/js/ColorItem.js", "webpack://bootstrap-colorpicker/./src/js/options.js", "webpack://bootstrap-colorpicker/./src/js/extensions/Palette.js", "webpack://bootstrap-colorpicker/./node_modules/color-name/index.js", "webpack://bootstrap-colorpicker/./node_modules/color-convert/conversions.js", "webpack://bootstrap-colorpicker/./src/js/plugin.js", "webpack://bootstrap-colorpicker/./src/js/Colorpicker.js", "webpack://bootstrap-colorpicker/./src/js/extensions/index.js", "webpack://bootstrap-colorpicker/./src/js/extensions/Debugger.js", "webpack://bootstrap-colorpicker/./src/js/extensions/Preview.js", "webpack://bootstrap-colorpicker/./src/js/extensions/Swatches.js", "webpack://bootstrap-colorpicker/./src/js/SliderHandler.js", "webpack://bootstrap-colorpicker/./src/js/PopupHandler.js", "webpack://bootstrap-colorpicker/./src/js/InputHandler.js", "webpack://bootstrap-colorpicker/./node_modules/color/index.js", "webpack://bootstrap-colorpicker/./node_modules/color-string/index.js", "webpack://bootstrap-colorpicker/./node_modules/simple-swizzle/index.js", "webpack://bootstrap-colorpicker/./node_modules/is-arrayish/index.js", "webpack://bootstrap-colorpicker/./node_modules/color-convert/index.js", "webpack://bootstrap-colorpicker/./node_modules/color-convert/route.js", "webpack://bootstrap-colorpicker/./src/js/ColorHandler.js", "webpack://bootstrap-colorpicker/./src/js/PickerHandler.js", "webpack://bootstrap-colorpicker/./src/js/AddonHandler.js"], "names": ["webpackUniversalModuleDefinition", "root", "factory", "exports", "module", "require", "define", "amd", "window", "__WEBPACK_EXTERNAL_MODULE__0__", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "getDefault", "getModuleExports", "object", "property", "prototype", "hasOwnProperty", "p", "s", "_j<PERSON>y", "Extension", "colorpicker", "options", "arguments", "length", "undefined", "_classCallCheck", "this", "element", "Error", "on", "$", "proxy", "onCreate", "onDestroy", "onUpdate", "onChange", "onInvalid", "onShow", "onHide", "onEnable", "onDisable", "color", "realColor", "event", "off", "_color", "HSVAColor", "h", "v", "a", "isNaN", "ColorItem", "fn", "_len", "args", "Array", "_key", "result", "apply", "QixColor", "format", "_original", "replace", "sanitizeFormat", "valid", "parse", "_color2", "default", "_format", "isHex", "model", "hue", "saturation", "alpha", "has<PERSON><PERSON><PERSON>", "toObject", "string", "round", "str", "<PERSON><PERSON><PERSON><PERSON>", "isDark", "isLight", "formula", "hues", "isArray", "colorFormulas", "colors", "mainColor", "for<PERSON>ach", "levels", "saturationv", "push", "Math", "sanitizeString", "e", "String", "match", "toLowerCase", "complementary", "triad", "tetrad", "splitcomplement", "sassVars", "bar_size_short", "base_margin", "columns", "sliderSize", "customClass", "fallbackColor", "horizontal", "inline", "container", "popover", "animation", "placement", "fallbackPlacement", "debug", "input", "addon", "autoInputFallback", "useHashPrefix", "useAlpha", "template", "extensions", "showText", "sliders", "selector", "maxLeft", "maxTop", "callLeft", "callTop", "childSelector", "slidersHorz", "_Extension2", "defaults", "namesAsValues", "Palette", "_this", "_possibleConstructorReturn", "__proto__", "getPrototypeOf", "extend", "_typeof", "keys", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "toUpperCase", "getValue", "getName", "defaultValue", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "cssKeywords", "reverseKeywords", "convert", "rgb", "channels", "labels", "hsl", "hsv", "hwb", "cmyk", "xyz", "lab", "lch", "hex", "keyword", "ansi16", "ansi256", "hcg", "apple", "g", "b", "min", "max", "delta", "rdif", "gdif", "bdif", "diff", "diffc", "w", "y", "k", "comparativeDistance", "x", "pow", "reversed", "currentClosestDistance", "Infinity", "currentClosestKeyword", "distance", "z", "t1", "t2", "t3", "val", "smin", "lmin", "sv", "hi", "floor", "f", "q", "vmin", "sl", "wh", "bl", "ratio", "y2", "x2", "z2", "hr", "atan2", "PI", "sqrt", "cos", "sin", "ansi", "mult", "rem", "integer", "toString", "substring", "colorString", "split", "map", "char", "join", "parseInt", "chroma", "grayscale", "pure", "mg", "_Colorpicker", "plugin", "Colorpicker", "option", "fnArgs", "slice", "isSingleElement", "returnValue", "$elements", "each", "$this", "_jquery2", "inst", "data", "isFunction", "constructor", "_Extension", "_options", "_extensions", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_Popup<PERSON>andler", "_InputHandler", "_ColorHandler", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ColorItem", "colorPickerIdCounter", "self", "colorHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "picker", "id", "lastEvent", "alias", "addClass", "attr", "disabled", "inputHandler", "InputHandler", "ColorHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addon<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "init", "trigger", "initExtensions", "attach", "update", "isDisabled", "disable", "ext", "registerExtension", "ExtensionClass", "config", "unbind", "removeClass", "removeData", "show", "hide", "toggle", "ch", "hasColor", "equals", "createColor", "assureColor", "enable", "eventName", "type", "coreExtensions", "_Debugger", "_Preview", "_Swatches", "_Palette", "Debugger", "Preview", "Swatches", "debugger", "preview", "swatches", "palette", "eventCounter", "hasInput", "onChangeInput", "_console", "logMessage", "console", "concat", "logArgs", "log", "_get", "elementInner", "find", "append", "css", "html", "toRgbString", "_Palette2", "barTemplate", "swatchTemplate", "isEnabled", "load", "_this2", "swatchContainer", "isAliased", "empty", "$swatch", "$sw", "setValue", "currentSlider", "mousePointer", "left", "top", "onMove", "defaultOnMove", "slider", "cp", "getFallbackColor", "getClone", "guideStyle", "focus", "sliderClasses", "slider<PERSON><PERSON>", "pressed", "mousemove.colorpicker", "moved", "touchmove.colorpicker", "mouseup.colorpicker", "released", "touchend.colorpicker", "pageX", "pageY", "originalEvent", "touches", "target", "zone", "closest", "is", "parent", "guide", "offset", "style", "preventDefault", "popoverTarget", "popoverTip", "clicking", "hidding", "showing", "hasAddon", "createPopover", "mousedown.colorpicker touchstart.colorpicker", "focus.colorpicker", "focusout.colorpicker", "reposition", "document", "onClickingInside", "isOrIsInside", "currentTarget", "isClickingInside", "_defaults", "content", "tip", "fireShow", "fireHide", "isVisible", "stopPropagation", "isPopover", "isHidden", "hasClass", "_initValue", "keyup.colorpicker", "onkeyup", "change.colorpicker", "onchange", "item", "getFormattedColor", "prop", "inputVal", "getColorString", "resolveColorDelegate", "isInvalidColor", "_slice", "skippedModels", "hashedModelKeys", "sort", "limiters", "Color", "obj", "valpha", "newArr", "zeroArray", "splice", "hashedKeys", "JSON", "stringify", "limit", "freeze", "toJSON", "places", "to", "percentString", "percent", "array", "unitArray", "unitObject", "roundToPlace", "getset", "maxfn", "saturationl", "lightness", "wblack", "rgbNumber", "luminosity", "lum", "chan", "contrast", "color2", "lum1", "lum2", "level", "contrastRatio", "yiq", "negate", "lighten", "darken", "saturate", "desaturate", "whiten", "blacken", "fade", "opaquer", "rotate", "degrees", "mix", "mixinColor", "weight", "color1", "w1", "w2", "newAlpha", "assertArray", "raw", "roundTo", "num", "Number", "toFixed", "channel", "modifier", "arr", "colorNames", "swizzle", "reverseNames", "cs", "prefix", "abbr", "rgba", "per", "hexAlpha", "i2", "parseFloat", "clamp", "hexDouble", "hsla", "hwba", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "results", "len", "arg", "wrap", "Function", "conversions", "route", "models", "wrapRaw", "wrappedFn", "conversion", "wrapRounded", "fromModel", "routes", "routeModels", "toModel", "buildGraph", "graph", "deriveBFS", "queue", "current", "pop", "adjacents", "adjacent", "node", "unshift", "link", "from", "wrapConversion", "path", "cur", "fallbackOnInvalid", "isAlphaEnabled", "fallback", "warn", "extResolvedColor", "resolveColor", "hasTransparency", "_supportsAlphaBar", "pickerParent", "appendTo", "remove", "vertical", "saturationGuide", "hueGuide", "alphaGuide", "hsva", "toHsvaRatio", "getCloneHueOnly", "toHexString", "hexColor", "alphaBg", "colorStr", "styles", "background", "icn", "eq"], "mappings": "CAAA,SAAAA,iCAAAC,KAAAC,SACA,UAAAC,UAAA,iBAAAC,SAAA,SACAA,OAAAD,QAAAD,QAAAG,QAAA,gBACA,UAAAC,SAAA,YAAAA,OAAAC,IACAD,OAAA,mCAAAJ,cACA,UAAAC,UAAA,SACAA,QAAA,yBAAAD,QAAAG,QAAA,gBAEAJ,KAAA,yBAAAC,QAAAD,KAAA,YARA,CASCO,OAAA,SAAAC,gCACD,yBCTA,IAAAC,oBAGA,SAAAC,oBAAAC,UAGA,GAAAF,iBAAAE,UAAA,CACA,OAAAF,iBAAAE,UAAAT,QAGA,IAAAC,OAAAM,iBAAAE,WACAC,EAAAD,SACAE,EAAA,MACAX,YAIAY,QAAAH,UAAAI,KAAAZ,OAAAD,QAAAC,cAAAD,QAAAQ,qBAGAP,OAAAU,EAAA,KAGA,OAAAV,OAAAD,QAKAQ,oBAAAM,EAAAF,QAGAJ,oBAAAO,EAAAR,iBAGAC,oBAAAQ,EAAA,SAAAhB,QAAAiB,KAAAC,QACA,IAAAV,oBAAAW,EAAAnB,QAAAiB,MAAA,CACAG,OAAAC,eAAArB,QAAAiB,MAA0CK,WAAA,KAAAC,IAAAL,WAK1CV,oBAAAgB,EAAA,SAAAxB,SACA,UAAAyB,SAAA,aAAAA,OAAAC,YAAA,CACAN,OAAAC,eAAArB,QAAAyB,OAAAC,aAAwDC,MAAA,WAExDP,OAAAC,eAAArB,QAAA,cAAiD2B,MAAA,QAQjDnB,oBAAAoB,EAAA,SAAAD,MAAAE,MACA,GAAAA,KAAA,EAAAF,MAAAnB,oBAAAmB,OACA,GAAAE,KAAA,SAAAF,MACA,GAAAE,KAAA,UAAAF,QAAA,UAAAA,aAAAG,WAAA,OAAAH,MACA,IAAAI,GAAAX,OAAAY,OAAA,MACAxB,oBAAAgB,EAAAO,IACAX,OAAAC,eAAAU,GAAA,WAAyCT,WAAA,KAAAK,QACzC,GAAAE,KAAA,UAAAF,OAAA,iBAAAM,OAAAN,MAAAnB,oBAAAQ,EAAAe,GAAAE,IAAA,SAAAA,KAAgH,OAAAN,MAAAM,MAAqBC,KAAA,KAAAD,MACrI,OAAAF,IAIAvB,oBAAA2B,EAAA,SAAAlC,QACA,IAAAiB,OAAAjB,eAAA6B,WACA,SAAAM,aAA2B,OAAAnC,OAAA,YAC3B,SAAAoC,mBAAiC,OAAApC,QACjCO,oBAAAQ,EAAAE,OAAA,IAAAA,QACA,OAAAA,QAIAV,oBAAAW,EAAA,SAAAmB,OAAAC,UAAsD,OAAAnB,OAAAoB,UAAAC,eAAA5B,KAAAyB,OAAAC,WAGtD/B,oBAAAkC,EAAA,GAIA,OAAAlC,wCAAAmC,EAAA,8BClFA1C,OAAAD,QAAAM,8oBCEA,IAAAsC,QAAApC,oBAAA,sRAKMqC,qBAKJ,SAAAA,UAAYC,aAA2B,IAAdC,QAAcC,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,MAAAG,gBAAAC,KAAAP,WAKrCO,KAAKN,YAAcA,YAMnBM,KAAKL,QAAUA,QAEf,KAAMK,KAAKN,YAAYO,SAAWD,KAAKN,YAAYO,QAAQJ,QAAS,CAClE,MAAM,IAAIK,MAAM,oDAGlBF,KAAKN,YAAYO,QAAQE,GAAG,oCAAqCC,iBAAEC,MAAML,KAAKM,SAAUN,OACxFA,KAAKN,YAAYO,QAAQE,GAAG,qCAAsCC,iBAAEC,MAAML,KAAKO,UAAWP,OAC1FA,KAAKN,YAAYO,QAAQE,GAAG,oCAAqCC,iBAAEC,MAAML,KAAKQ,SAAUR,OACxFA,KAAKN,YAAYO,QAAQE,GAAG,oCAAqCC,iBAAEC,MAAML,KAAKS,SAAUT,OACxFA,KAAKN,YAAYO,QAAQE,GAAG,qCAAsCC,iBAAEC,MAAML,KAAKU,UAAWV,OAC1FA,KAAKN,YAAYO,QAAQE,GAAG,kCAAmCC,iBAAEC,MAAML,KAAKW,OAAQX,OACpFA,KAAKN,YAAYO,QAAQE,GAAG,kCAAmCC,iBAAEC,MAAML,KAAKY,OAAQZ,OACpFA,KAAKN,YAAYO,QAAQE,GAAG,oCAAqCC,iBAAEC,MAAML,KAAKa,SAAUb,OACxFA,KAAKN,YAAYO,QAAQE,GAAG,qCAAsCC,iBAAEC,MAAML,KAAKc,UAAWd,+EAY/Ee,OAAyB,IAAlBC,UAAkBpB,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,GAAN,KAC9B,OAAO,gDASAqB,oDAUCA,OACRjB,KAAKN,YAAYO,QAAQiB,IAAI,8DAStBD,kDAUAA,oDAUCA,8CAUHA,8CAUAA,oDAUGA,kDAUDA,gDAKIxB,ssBC7If,IAAA0B,OAAA/D,oBAAA,qRAMMgE,qBAOJ,SAAAA,UAAYC,EAAG9B,EAAG+B,EAAGC,GAAGxB,gBAAAC,KAAAoB,WACtBpB,KAAKqB,EAAIG,MAAMH,GAAK,EAAIA,EACxBrB,KAAKT,EAAIiC,MAAMjC,GAAK,EAAIA,EACxBS,KAAKsB,EAAIE,MAAMF,GAAK,EAAIA,EACxBtB,KAAKuB,EAAIC,MAAMH,GAAK,EAAIE,oEAIxB,OAAUvB,KAAKqB,EAAf,KAAqBrB,KAAKT,EAA1B,MAAiCS,KAAKsB,EAAtC,MAA6CtB,KAAKuB,8BAOhDE,2EA2BAC,IAAa,QAAAC,KAAA/B,UAAAC,OAAN+B,KAAMC,MAAAF,KAAA,EAAAA,KAAA,KAAAG,KAAA,EAAAA,KAAAH,KAAAG,OAAA,CAANF,KAAME,KAAA,GAAAlC,UAAAkC,MACf,GAAIlC,UAAUC,SAAW,EAAG,CAC1B,OAAOG,KAAKmB,OAGd,IAAIY,OAAS/B,KAAKmB,OAAOO,IAAIM,MAAMhC,KAAKmB,OAAQS,MAEhD,KAAMG,kBAAkBE,iBAAW,CAEjC,OAAOF,OAGT,OAAO,IAAIN,UAAUM,OAAQ/B,KAAKkC,6CAUlC,OAAOlC,KAAKmC,kDAvCZ,OAAOf,cA8CT,SAAAK,YAAyC,IAA7BV,MAA6BnB,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,GAArB,KAAqB,IAAfsC,OAAetC,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,GAAN,KAAMG,gBAAAC,KAAAyB,WACvCzB,KAAKoC,QAAQrB,MAAOmB,sEAYdnB,OAAsB,IAAfmB,OAAetC,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,GAAN,KACtBsC,OAAST,UAAUY,eAAeH,QAMlClC,KAAKmC,WACHpB,MACAmB,OACAI,MAAO,MAMTtC,KAAKmB,OAASM,UAAUc,MAAMxB,OAE9B,GAAIf,KAAKmB,SAAW,KAAM,CACxBnB,KAAKmB,QAAS,EAAAqB,QAAAC,WACdzC,KAAKmC,UAAUG,MAAQ,MACvB,OAOFtC,KAAK0C,QAAUR,OAASA,OACrBT,UAAUkB,MAAM5B,OAAS,MAAQf,KAAKmB,OAAOyB,gDAiIhD,OAAO5C,KAAKmC,UAAUG,QAAU,qDAiEtBjB,GACVrB,KAAK6C,KAAQ,EAAIxB,GAAK,kEAkBL9B,GACjBS,KAAK8C,WAAcvD,EAAI,wDAkBX+B,GACZtB,KAAKzB,OAAU,EAAI+C,GAAK,wDAmBZC,GACZvB,KAAK+C,MAAQ,EAAIxB,wDAkBjB,OAAOvB,KAAK8C,aAAe,wDAS3B,OAAO9C,KAAK+C,QAAU,4DAStB,OAAO/C,KAAKgD,YAAehD,KAAK+C,MAAQ,8CASxC,OAAQvB,MAAMxB,KAAK+C,mDASnB,OAAO,IAAI3B,UAAUpB,KAAK6C,IAAK7C,KAAK8C,WAAY9C,KAAKzB,MAAOyB,KAAK+C,+CASjE,OAAO/C,KAAKiD,6DAWZ,OAAO,IAAI7B,UACTpB,KAAK6C,IAAM,IACX7C,KAAK8C,WAAa,IAClB9C,KAAKzB,MAAQ,IACbyB,KAAK+C,mDAWP,OAAO/C,KAAKkD,iDAUQ,IAAfhB,OAAetC,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,GAAN,KACdsC,OAAST,UAAUY,eAAeH,OAASA,OAASlC,KAAKkC,QAEzD,IAAKA,OAAQ,CACX,OAAOlC,KAAKmB,OAAOgC,QAAQD,SAG7B,GAAIlD,KAAKmB,OAAOe,UAAYpC,UAAW,CACrC,MAAM,IAAII,MAAJ,8BAAwCgC,OAAxC,KAGR,IAAIkB,IAAMpD,KAAKmB,OAAOe,UAEtB,OAAOkB,IAAID,MAAQC,IAAID,QAAQD,SAAWE,0CAYrCrC,OACLA,MAASA,iBAAiBU,UAAaV,MAAQ,IAAIU,UAAUV,OAE7D,IAAKA,MAAMsC,YAAcrD,KAAKqD,UAAW,CACvC,OAAO,MAGT,OACErD,KAAK6C,MAAQ9B,MAAM8B,KACnB7C,KAAK8C,aAAe/B,MAAM+B,YAC1B9C,KAAKzB,QAAUwC,MAAMxC,OACrByB,KAAK+C,QAAUhC,MAAMgC,kDAUvB,OAAO,IAAItB,UAAUzB,KAAKmB,OAAQnB,KAAKkC,kEAUvC,OAAO,IAAIT,WAAWzB,KAAK6C,IAAK,IAAK,IAAK,GAAI7C,KAAKkC,gEASnD,OAAO,IAAIT,UAAUzB,KAAKmB,OAAO4B,MAAM,GAAI/C,KAAKkC,0DAShD,OAAOlC,KAAKkD,OAAO,yDASnB,OAAOlD,KAAKkD,OAAO,yDASnB,OAAOlD,KAAKkD,OAAO,+CAUnB,OAAOlD,KAAKmB,OAAOmC,mDAUnB,OAAOtD,KAAKmB,OAAOoC,oDAYZC,SACP,IAAIC,QAEJ,GAAI5B,MAAM6B,QAAQF,SAAU,CAC1BC,KAAOD,aACF,IAAK/B,UAAUkC,cAActE,eAAemE,SAAU,CAC3D,MAAM,IAAItD,MAAJ,yCAAmDsD,QAAnD,UACD,CACLC,KAAOhC,UAAUkC,cAAcH,SAGjC,IAAII,UAAaC,UAAY7D,KAAKmB,OAAQe,OAASlC,KAAKkC,OAExDuB,KAAKK,QAAQ,SAAUjB,KACrB,IAAIkB,QACFlB,KAAQgB,UAAUhB,MAAQA,KAAO,IAAOgB,UAAUhB,MAClDgB,UAAUG,cACVH,UAAUtF,QACVsF,UAAUd,SAGZa,OAAOK,KAAK,IAAIxC,UAAUsC,OAAQ7B,WAGpC,OAAO0B,uCA1WP,OAAO5D,KAAKmB,OAAO0B,wBA8CbtE,OACNyB,KAAKmB,OAASnB,KAAKmB,OAAO0B,IAAItE,8CAtC9B,OAAOyB,KAAKmB,OAAO6C,gCAwDNzF,OACbyB,KAAKmB,OAASnB,KAAKmB,OAAO6C,YAAYzF,yCAhDtC,OAAOyB,KAAKmB,OAAO5C,0BAkEXA,OACRyB,KAAKmB,OAASnB,KAAKmB,OAAO5C,MAAMA,yCA1DhC,IAAIgD,EAAIvB,KAAKmB,OAAO4B,QAEpB,OAAOvB,MAAMD,GAAK,EAAIA,oBA0EdhD,OAERyB,KAAKmB,OAASnB,KAAKmB,OAAO4B,MAAMmB,KAAKf,MAAM5E,MAAQ,KAAO,wCAnE1D,OAAOyB,KAAK0C,QAAU1C,KAAK0C,QAAU1C,KAAKmB,OAAOyB,wBAqFxCrE,OACTyB,KAAK0C,QAAUjB,UAAUY,eAAe9D,6CA1P7BwC,OACX,GAAIA,iBAAiBkB,gBAAU,CAC7B,OAAOlB,MAGT,GAAIA,iBAAiBU,UAAW,CAC9B,OAAOV,MAAMI,OAGf,IAAIe,OAAS,KAEb,GAAInB,iBAAiBK,UAAW,CAC9BL,OAASA,MAAMM,EAAGN,MAAMxB,EAAGwB,MAAMO,EAAGE,MAAMT,MAAMQ,GAAK,EAAIR,MAAMQ,OAC1D,CACLR,MAAQU,UAAU0C,eAAepD,OAGnC,GAAIA,QAAU,KAAM,CAClB,OAAO,KAGT,GAAIc,MAAM6B,QAAQ3C,OAAQ,CACxBmB,OAAS,MAGX,IACE,OAAO,EAAAM,QAAAC,SAAS1B,MAAOmB,QACvB,MAAOkC,GACP,OAAO,4DAaWhB,KACpB,YAAaA,MAAQ,UAAYA,eAAeiB,QAAS,CACvD,OAAOjB,IAGT,GAAIA,IAAIkB,MAAM,mBAAoB,CAChC,UAAWlB,IAGb,GAAIA,IAAImB,gBAAkB,cAAe,CACvC,MAAO,YAGT,OAAOnB,wCAaIA,KACX,YAAaA,MAAQ,UAAYA,eAAeiB,QAAS,CACvD,OAAO,MAGT,QAASjB,IAAIkB,MAAM,2EAcCpC,QACpB,OAAQA,QACN,IAAK,MACL,IAAK,OACL,IAAK,OACL,IAAK,OACL,IAAK,OACH,MAAO,MACT,IAAK,MACL,IAAK,OACL,IAAK,UACL,IAAK,OACH,MAAO,MACT,IAAK,MACL,IAAK,OACL,IAAK,MACL,IAAK,OACL,IAAK,MACL,IAAK,OACH,MAAO,MACT,QACE,MAAO,4BAuYfT,UAAUkC,eACRa,eAAgB,KAChBC,OAAQ,EAAG,IAAK,KAChBC,QAAS,EAAG,GAAI,IAAK,KACrBC,iBAAkB,EAAG,GAAI,sBAGZlD,kBAGbL,4BACAK,wICpoBF,IAAImD,UACFC,eAAkB,GAClBC,YAAe,EACfC,QAAW,GAGb,IAAIC,WAAcJ,SAASC,eAAiBD,SAASG,QAAYH,SAASE,aAAeF,SAASG,QAAU,oBAY1GE,YAAa,KAOblE,MAAO,MAQPmE,cAAe,MAWfhD,OAAQ,OASRiD,WAAY,MAUZC,OAAQ,MAYRC,UAAW,MAQXC,SACEC,UAAW,KACXC,UAAW,SACXC,kBAAmB,QAOrBC,MAAO,MAOPC,MAAO,QAQPC,MAAO,2BASPC,kBAAmB,KASnBC,cAAe,KAafC,SAAU,KAeVC,qWA+BAC,aAEIpI,KAAM,UACN8B,SACEuG,SAAU,QAQhBC,SACErD,YACEsD,SAAU,0BACVC,QAASrB,WACTsB,OAAQtB,WACRuB,SAAU,qBACVC,QAAS,iBAEX3D,KACEuD,SAAU,mBACVC,QAAS,EACTC,OAAQtB,WACRuB,SAAU,MACVC,QAAS,eAEXzD,OACEqD,SAAU,qBACVK,cAAe,2BACfJ,QAAS,EACTC,OAAQtB,WACRuB,SAAU,MACVC,QAAS,kBAObE,aACE5D,YACEsD,SAAU,0BACVC,QAASrB,WACTsB,OAAQtB,WACRuB,SAAU,qBACVC,QAAS,iBAEX3D,KACEuD,SAAU,mBACVC,QAASrB,WACTsB,OAAQ,EACRC,SAAU,cACVC,QAAS,OAEXzD,OACEqD,SAAU,qBACVK,cAAe,2BACfJ,QAASrB,WACTsB,OAAQ,EACRC,SAAU,gBACVC,QAAS,83BC1Pf,IAAAG,YAAAvJ,oBAAA,uDACA,IAAAoC,QAAApC,oBAAA,26BAEA,IAAIwJ,UAuBFhD,OAAQ,KAQRiD,cAAe,UAOXC,kHAMF,OAAO9G,KAAKL,QAAQiE,WAGtB,SAAAkD,QAAYpH,aAA2B,IAAdC,QAAcC,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,MAAAG,gBAAAC,KAAA8G,SAAA,IAAAC,MAAAC,2BAAAhH,MAAA8G,QAAAG,WAAAjJ,OAAAkJ,eAAAJ,UAAArJ,KAAAuC,KAC/BN,YAAaU,iBAAE+G,OAAO,QAAUP,SAAUjH,WAEhD,IAAMkC,MAAM6B,QAAQqD,MAAKpH,QAAQiE,SAAawD,QAAOL,MAAKpH,QAAQiE,UAAW,SAAW,CACtFmD,MAAKpH,QAAQiE,OAAS,KAJa,OAAAmD,wEAYrC,IAAK/G,KAAKL,QAAQiE,OAAQ,CACxB,OAAO,EAGT,GAAI/B,MAAM6B,QAAQ1D,KAAKL,QAAQiE,QAAS,CACtC,OAAO5D,KAAKL,QAAQiE,OAAO/D,OAG7B,GAAIuH,QAAOpH,KAAKL,QAAQiE,UAAW,SAAU,CAC3C,OAAO5F,OAAOqJ,KAAKrH,KAAKL,QAAQiE,QAAQ/D,OAG1C,OAAO,oDAGIkB,OAAyB,IAAlBC,UAAkBpB,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,GAAN,KAC9B,GAAII,KAAKsH,aAAe,EAAG,CACzB,OAAO,MAIT,GAAIzF,MAAM6B,QAAQ1D,KAAKL,QAAQiE,QAAS,CACtC,GAAI5D,KAAKL,QAAQiE,OAAO2D,QAAQxG,QAAU,EAAG,CAC3C,OAAOA,MAET,GAAIf,KAAKL,QAAQiE,OAAO2D,QAAQxG,MAAMyG,gBAAkB,EAAG,CACzD,OAAOzG,MAAMyG,cAEf,GAAIxH,KAAKL,QAAQiE,OAAO2D,QAAQxG,MAAMwD,gBAAkB,EAAG,CACzD,OAAOxD,MAAMwD,cAEf,OAAO,MAGT,GAAI6C,QAAOpH,KAAKL,QAAQiE,UAAW,SAAU,CAC3C,OAAO,MAIT,IAAK5D,KAAKL,QAAQkH,eAAiB7F,UAAW,CAC5C,OAAOhB,KAAKyH,SAAS1G,MAAO,OAE9B,OAAOf,KAAK0H,QAAQ3G,MAAOf,KAAK0H,QAAQ,IAAM3G,gDAUxCxC,OAA6B,IAAtBoJ,aAAsB/H,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,GAAP,MAC5B,YAAarB,QAAU,YAAcyB,KAAKL,QAAQiE,OAAQ,CACxD,OAAO+D,aAET,IAAK,IAAI9J,QAAQmC,KAAKL,QAAQiE,OAAQ,CACpC,IAAK5D,KAAKL,QAAQiE,OAAOvE,eAAexB,MAAO,CAC7C,SAEF,GAAImC,KAAKL,QAAQiE,OAAO/F,MAAM0G,gBAAkBhG,MAAMgG,cAAe,CACnE,OAAO1G,MAGX,OAAO8J,uDAUA9J,MAA4B,IAAtB8J,aAAsB/H,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,GAAP,MAC5B,YAAa/B,OAAS,YAAcmC,KAAKL,QAAQiE,OAAQ,CACvD,OAAO+D,aAET,GAAI3H,KAAKL,QAAQiE,OAAOvE,eAAexB,MAAO,CAC5C,OAAOmC,KAAKL,QAAQiE,OAAO/F,MAE7B,OAAO8J,iCAtGWlI,qCA0GPqH,kGCnJfjK,OAAAD,SACAgL,WAAA,aACAC,cAAA,aACAC,MAAA,WACAC,YAAA,aACAC,OAAA,aACAC,OAAA,aACAC,QAAA,aACAC,OAAA,OACAC,gBAAA,aACAC,MAAA,SACAC,YAAA,YACAC,OAAA,WACAC,WAAA,aACAC,WAAA,YACAC,YAAA,WACAC,WAAA,YACAC,OAAA,YACAC,gBAAA,aACAC,UAAA,aACAC,SAAA,WACAC,MAAA,WACAC,UAAA,SACAC,UAAA,WACAC,eAAA,YACAC,UAAA,aACAC,WAAA,SACAC,UAAA,aACAC,WAAA,aACAC,aAAA,WACAC,gBAAA,WACAC,YAAA,WACAC,YAAA,YACAC,SAAA,SACAC,YAAA,aACAC,cAAA,aACAC,eAAA,WACAC,eAAA,UACAC,eAAA,UACAC,eAAA,WACAC,YAAA,WACAC,UAAA,YACAC,aAAA,WACAC,SAAA,aACAC,SAAA,aACAC,YAAA,YACAC,WAAA,WACAC,aAAA,aACAC,aAAA,WACAC,SAAA,WACAC,WAAA,aACAC,YAAA,aACAC,MAAA,WACAC,WAAA,YACAC,MAAA,aACAC,OAAA,SACAC,aAAA,YACAC,MAAA,aACAC,UAAA,aACAC,SAAA,aACAC,WAAA,WACAC,QAAA,UACAC,OAAA,aACAC,OAAA,aACAC,UAAA,aACAC,eAAA,aACAC,WAAA,WACAC,cAAA,aACAC,WAAA,aACAC,YAAA,aACAC,WAAA,aACAC,sBAAA,aACAC,WAAA,aACAC,YAAA,aACAC,WAAA,aACAC,WAAA,aACAC,aAAA,aACAC,eAAA,YACAC,cAAA,aACAC,gBAAA,aACAC,gBAAA,aACAC,gBAAA,aACAC,aAAA,aACAC,MAAA,SACAC,WAAA,WACAC,OAAA,aACAC,SAAA,WACAC,QAAA,SACAC,kBAAA,aACAC,YAAA,SACAC,cAAA,YACAC,cAAA,aACAC,gBAAA,YACAC,iBAAA,aACAC,mBAAA,WACAC,iBAAA,YACAC,iBAAA,YACAC,cAAA,WACAC,WAAA,aACAC,WAAA,aACAC,UAAA,aACAC,aAAA,aACAC,MAAA,SACAC,SAAA,aACAC,OAAA,WACAC,WAAA,YACAC,QAAA,WACAC,WAAA,UACAC,QAAA,aACAC,eAAA,aACAC,WAAA,aACAC,eAAA,aACAC,eAAA,aACAC,YAAA,aACAC,WAAA,aACAC,MAAA,YACAC,MAAA,aACAC,MAAA,aACAC,YAAA,aACAC,QAAA,WACAC,eAAA,YACAC,KAAA,SACAC,WAAA,aACAC,WAAA,YACAC,aAAA,WACAC,QAAA,aACAC,YAAA,YACAC,UAAA,WACAC,UAAA,aACAC,QAAA,WACAC,QAAA,aACAC,SAAA,aACAC,WAAA,YACAC,WAAA,aACAC,WAAA,aACAC,MAAA,aACAC,aAAA,WACAC,WAAA,YACAC,KAAA,aACAC,MAAA,WACAC,SAAA,aACAC,QAAA,WACAC,WAAA,YACAC,QAAA,aACAC,OAAA,aACAC,OAAA,aACAC,YAAA,aACAC,QAAA,WACAC,aAAA,2DCrJA,IAAAC,YAAkB5T,oBAAQ,GAM1B,IAAA6T,mBACA,QAAApS,OAAAmS,YAAA,CACA,GAAAA,YAAA3R,eAAAR,KAAA,CACAoS,gBAAAD,YAAAnS,WAIA,IAAAqS,QAAArU,OAAAD,SACAuU,KAAOC,SAAA,EAAAC,OAAA,OACPC,KAAOF,SAAA,EAAAC,OAAA,OACPE,KAAOH,SAAA,EAAAC,OAAA,OACPG,KAAOJ,SAAA,EAAAC,OAAA,OACPI,MAAQL,SAAA,EAAAC,OAAA,QACRK,KAAON,SAAA,EAAAC,OAAA,OACPM,KAAOP,SAAA,EAAAC,OAAA,OACPO,KAAOR,SAAA,EAAAC,OAAA,OACPQ,KAAOT,SAAA,EAAAC,QAAA,QACPS,SAAWV,SAAA,EAAAC,QAAA,YACXU,QAAUX,SAAA,EAAAC,QAAA,WACVW,SAAWZ,SAAA,EAAAC,QAAA,YACXY,KAAOb,SAAA,EAAAC,QAAA,cACPa,OAASd,SAAA,EAAAC,QAAA,oBACTpG,MAAQmG,SAAA,EAAAC,QAAA,UAIR,QAAAzO,SAAAsO,QAAA,CACA,GAAAA,QAAA7R,eAAAuD,OAAA,CACA,kBAAAsO,QAAAtO,QAAA,CACA,UAAA1C,MAAA,8BAAA0C,OAGA,gBAAAsO,QAAAtO,QAAA,CACA,UAAA1C,MAAA,oCAAA0C,OAGA,GAAAsO,QAAAtO,OAAAyO,OAAAxR,SAAAqR,QAAAtO,OAAAwO,SAAA,CACA,UAAAlR,MAAA,sCAAA0C,OAGA,IAAAwO,SAAAF,QAAAtO,OAAAwO,SACA,IAAAC,OAAAH,QAAAtO,OAAAyO,cACAH,QAAAtO,OAAAwO,gBACAF,QAAAtO,OAAAyO,OACArT,OAAAC,eAAAiT,QAAAtO,OAAA,YAAqDrE,MAAA6S,WACrDpT,OAAAC,eAAAiT,QAAAtO,OAAA,UAAmDrE,MAAA8S,UAInDH,QAAAC,IAAAG,IAAA,SAAAH,KACA,IAAA/S,EAAA+S,IAAA,OACA,IAAAgB,EAAAhB,IAAA,OACA,IAAAiB,EAAAjB,IAAA,OACA,IAAAkB,IAAAnO,KAAAmO,IAAAjU,EAAA+T,EAAAC,GACA,IAAAE,IAAApO,KAAAoO,IAAAlU,EAAA+T,EAAAC,GACA,IAAAG,MAAAD,IAAAD,IACA,IAAAhR,EACA,IAAA9B,EACA,IAAAhC,EAEA,GAAA+U,MAAAD,IAAA,CACAhR,EAAA,OACE,GAAAjD,IAAAkU,IAAA,CACFjR,GAAA8Q,EAAAC,GAAAG,WACE,GAAAJ,IAAAG,IAAA,CACFjR,EAAA,GAAA+Q,EAAAhU,GAAAmU,WACE,GAAAH,IAAAE,IAAA,CACFjR,EAAA,GAAAjD,EAAA+T,GAAAI,MAGAlR,EAAA6C,KAAAmO,IAAAhR,EAAA,QAEA,GAAAA,EAAA,GACAA,GAAA,IAGA9D,GAAA8U,IAAAC,KAAA,EAEA,GAAAA,MAAAD,IAAA,CACA9S,EAAA,OACE,GAAAhC,GAAA,IACFgC,EAAAgT,OAAAD,IAAAD,SACE,CACF9S,EAAAgT,OAAA,EAAAD,IAAAD,KAGA,OAAAhR,EAAA9B,EAAA,IAAAhC,EAAA,MAGA2T,QAAAC,IAAAI,IAAA,SAAAJ,KACA,IAAAqB,KACA,IAAAC,KACA,IAAAC,KACA,IAAArR,EACA,IAAA9B,EAEA,IAAAnB,EAAA+S,IAAA,OACA,IAAAgB,EAAAhB,IAAA,OACA,IAAAiB,EAAAjB,IAAA,OACA,IAAA7P,EAAA4C,KAAAoO,IAAAlU,EAAA+T,EAAAC,GACA,IAAAO,KAAArR,EAAA4C,KAAAmO,IAAAjU,EAAA+T,EAAAC,GACA,IAAAQ,MAAA,SAAAjV,GACA,OAAA2D,EAAA3D,GAAA,EAAAgV,KAAA,KAGA,GAAAA,OAAA,GACAtR,EAAA9B,EAAA,MACE,CACFA,EAAAoT,KAAArR,EACAkR,KAAAI,MAAAxU,GACAqU,KAAAG,MAAAT,GACAO,KAAAE,MAAAR,GAEA,GAAAhU,IAAAkD,EAAA,CACAD,EAAAqR,KAAAD,UACG,GAAAN,IAAA7Q,EAAA,CACHD,EAAA,IAAAmR,KAAAE,UACG,GAAAN,IAAA9Q,EAAA,CACHD,EAAA,IAAAoR,KAAAD,KAEA,GAAAnR,EAAA,GACAA,GAAA,OACG,GAAAA,EAAA,GACHA,GAAA,GAIA,OACAA,EAAA,IACA9B,EAAA,IACA+B,EAAA,MAIA4P,QAAAC,IAAAK,IAAA,SAAAL,KACA,IAAA/S,EAAA+S,IAAA,GACA,IAAAgB,EAAAhB,IAAA,GACA,IAAAiB,EAAAjB,IAAA,GACA,IAAA9P,EAAA6P,QAAAC,IAAAG,IAAAH,KAAA,GACA,IAAA0B,EAAA,MAAA3O,KAAAmO,IAAAjU,EAAA8F,KAAAmO,IAAAF,EAAAC,IAEAA,EAAA,QAAAlO,KAAAoO,IAAAlU,EAAA8F,KAAAoO,IAAAH,EAAAC,IAEA,OAAA/Q,EAAAwR,EAAA,IAAAT,EAAA,MAGAlB,QAAAC,IAAAM,KAAA,SAAAN,KACA,IAAA/S,EAAA+S,IAAA,OACA,IAAAgB,EAAAhB,IAAA,OACA,IAAAiB,EAAAjB,IAAA,OACA,IAAAxT,EACA,IAAAD,EACA,IAAAoV,EACA,IAAAC,EAEAA,EAAA7O,KAAAmO,IAAA,EAAAjU,EAAA,EAAA+T,EAAA,EAAAC,GACAzU,GAAA,EAAAS,EAAA2U,IAAA,EAAAA,IAAA,EACArV,GAAA,EAAAyU,EAAAY,IAAA,EAAAA,IAAA,EACAD,GAAA,EAAAV,EAAAW,IAAA,EAAAA,IAAA,EAEA,OAAApV,EAAA,IAAAD,EAAA,IAAAoV,EAAA,IAAAC,EAAA,MAMA,SAAAC,oBAAAC,EAAAH,GACA,OACA5O,KAAAgP,IAAAD,EAAA,GAAAH,EAAA,MACA5O,KAAAgP,IAAAD,EAAA,GAAAH,EAAA,MACA5O,KAAAgP,IAAAD,EAAA,GAAAH,EAAA,MAIA5B,QAAAC,IAAAW,QAAA,SAAAX,KACA,IAAAgC,SAAAlC,gBAAAE,KACA,GAAAgC,SAAA,CACA,OAAAA,SAGA,IAAAC,uBAAAC,SACA,IAAAC,sBAEA,QAAAxB,WAAAd,YAAA,CACA,GAAAA,YAAA3R,eAAAyS,SAAA,CACA,IAAAvT,MAAAyS,YAAAc,SAGA,IAAAyB,SAAAP,oBAAA7B,IAAA5S,OAGA,GAAAgV,SAAAH,uBAAA,CACAA,uBAAAG,SACAD,sBAAAxB,UAKA,OAAAwB,uBAGApC,QAAAY,QAAAX,IAAA,SAAAW,SACA,OAAAd,YAAAc,UAGAZ,QAAAC,IAAAO,IAAA,SAAAP,KACA,IAAA/S,EAAA+S,IAAA,OACA,IAAAgB,EAAAhB,IAAA,OACA,IAAAiB,EAAAjB,IAAA,OAGA/S,IAAA,OAAA8F,KAAAgP,KAAA9U,EAAA,iBAAAA,EAAA,MACA+T,IAAA,OAAAjO,KAAAgP,KAAAf,EAAA,iBAAAA,EAAA,MACAC,IAAA,OAAAlO,KAAAgP,KAAAd,EAAA,iBAAAA,EAAA,MAEA,IAAAa,EAAA7U,EAAA,MAAA+T,EAAA,MAAAC,EAAA,MACA,IAAAU,EAAA1U,EAAA,MAAA+T,EAAA,MAAAC,EAAA,MACA,IAAAoB,EAAApV,EAAA,MAAA+T,EAAA,MAAAC,EAAA,MAEA,OAAAa,EAAA,IAAAH,EAAA,IAAAU,EAAA,MAGAtC,QAAAC,IAAAQ,IAAA,SAAAR,KACA,IAAAO,IAAAR,QAAAC,IAAAO,IAAAP,KACA,IAAA8B,EAAAvB,IAAA,GACA,IAAAoB,EAAApB,IAAA,GACA,IAAA8B,EAAA9B,IAAA,GACA,IAAAnU,EACA,IAAAgE,EACA,IAAA6Q,EAEAa,GAAA,OACAH,GAAA,IACAU,GAAA,QAEAP,IAAA,QAAA/O,KAAAgP,IAAAD,EAAA,WAAAA,EAAA,OACAH,IAAA,QAAA5O,KAAAgP,IAAAJ,EAAA,WAAAA,EAAA,OACAU,IAAA,QAAAtP,KAAAgP,IAAAM,EAAA,WAAAA,EAAA,OAEAjW,EAAA,IAAAuV,EAAA,GACAvR,EAAA,KAAA0R,EAAAH,GACAV,EAAA,KAAAU,EAAAU,GAEA,OAAAjW,EAAAgE,EAAA6Q,IAGAlB,QAAAI,IAAAH,IAAA,SAAAG,KACA,IAAAjQ,EAAAiQ,IAAA,OACA,IAAA/R,EAAA+R,IAAA,OACA,IAAA/T,EAAA+T,IAAA,OACA,IAAAmC,GACA,IAAAC,GACA,IAAAC,GACA,IAAAxC,IACA,IAAAyC,IAEA,GAAArU,IAAA,GACAqU,IAAArW,EAAA,IACA,OAAAqW,aAGA,GAAArW,EAAA,IACAmW,GAAAnW,GAAA,EAAAgC,OACE,CACFmU,GAAAnW,EAAAgC,EAAAhC,EAAAgC,EAGAkU,GAAA,EAAAlW,EAAAmW,GAEAvC,KAAA,OACA,QAAA7T,EAAA,EAAgBA,EAAA,EAAOA,IAAA,CACvBqW,GAAAtS,EAAA,MAAA/D,EAAA,GACA,GAAAqW,GAAA,GACAA,KAEA,GAAAA,GAAA,GACAA,KAGA,KAAAA,GAAA,GACAC,IAAAH,IAAAC,GAAAD,IAAA,EAAAE,QACG,KAAAA,GAAA,GACHC,IAAAF,QACG,KAAAC,GAAA,GACHC,IAAAH,IAAAC,GAAAD,KAAA,IAAAE,IAAA,MACG,CACHC,IAAAH,GAGAtC,IAAA7T,GAAAsW,IAAA,IAGA,OAAAzC,KAGAD,QAAAI,IAAAC,IAAA,SAAAD,KACA,IAAAjQ,EAAAiQ,IAAA,GACA,IAAA/R,EAAA+R,IAAA,OACA,IAAA/T,EAAA+T,IAAA,OACA,IAAAuC,KAAAtU,EACA,IAAAuU,KAAA5P,KAAAoO,IAAA/U,EAAA,KACA,IAAAwW,GACA,IAAAzS,EAEA/D,GAAA,EACAgC,GAAAhC,GAAA,EAAAA,EAAA,EAAAA,EACAsW,MAAAC,MAAA,EAAAA,KAAA,EAAAA,KACAxS,GAAA/D,EAAAgC,GAAA,EACAwU,GAAAxW,IAAA,IAAAsW,MAAAC,KAAAD,MAAA,EAAAtU,GAAAhC,EAAAgC,GAEA,OAAA8B,EAAA0S,GAAA,IAAAzS,EAAA,MAGA4P,QAAAK,IAAAJ,IAAA,SAAAI,KACA,IAAAlQ,EAAAkQ,IAAA,MACA,IAAAhS,EAAAgS,IAAA,OACA,IAAAjQ,EAAAiQ,IAAA,OACA,IAAAyC,GAAA9P,KAAA+P,MAAA5S,GAAA,EAEA,IAAA6S,EAAA7S,EAAA6C,KAAA+P,MAAA5S,GACA,IAAA/B,EAAA,IAAAgC,GAAA,EAAA/B,GACA,IAAA4U,EAAA,IAAA7S,GAAA,EAAA/B,EAAA2U,GACA,IAAA1V,EAAA,IAAA8C,GAAA,EAAA/B,GAAA,EAAA2U,IACA5S,GAAA,IAEA,OAAA0S,IACA,OACA,OAAA1S,EAAA9C,EAAAc,GACA,OACA,OAAA6U,EAAA7S,EAAAhC,GACA,OACA,OAAAA,EAAAgC,EAAA9C,GACA,OACA,OAAAc,EAAA6U,EAAA7S,GACA,OACA,OAAA9C,EAAAc,EAAAgC,GACA,OACA,OAAAA,EAAAhC,EAAA6U,KAIAjD,QAAAK,IAAAD,IAAA,SAAAC,KACA,IAAAlQ,EAAAkQ,IAAA,GACA,IAAAhS,EAAAgS,IAAA,OACA,IAAAjQ,EAAAiQ,IAAA,OACA,IAAA6C,KAAAlQ,KAAAoO,IAAAhR,EAAA,KACA,IAAAwS,KACA,IAAAO,GACA,IAAA9W,EAEAA,GAAA,EAAAgC,GAAA+B,EACAwS,MAAA,EAAAvU,GAAA6U,KACAC,GAAA9U,EAAA6U,KACAC,IAAAP,MAAA,EAAAA,KAAA,EAAAA,KACAO,OAAA,EACA9W,GAAA,EAEA,OAAA8D,EAAAgT,GAAA,IAAA9W,EAAA,MAIA2T,QAAAM,IAAAL,IAAA,SAAAK,KACA,IAAAnQ,EAAAmQ,IAAA,OACA,IAAA8C,GAAA9C,IAAA,OACA,IAAA+C,GAAA/C,IAAA,OACA,IAAAgD,MAAAF,GAAAC,GACA,IAAAjX,EACA,IAAAgE,EACA,IAAA4S,EACA,IAAAnV,EAGA,GAAAyV,MAAA,GACAF,IAAAE,MACAD,IAAAC,MAGAlX,EAAA4G,KAAA+P,MAAA,EAAA5S,GACAC,EAAA,EAAAiT,GACAL,EAAA,EAAA7S,EAAA/D,EAEA,IAAAA,EAAA,QACA4W,EAAA,EAAAA,EAGAnV,EAAAuV,GAAAJ,GAAA5S,EAAAgT,IAEA,IAAAlW,EACA,IAAA+T,EACA,IAAAC,EACA,OAAA9U,GACA,QACA,OACA,OAAAc,EAAAkD,EAAgB6Q,EAAApT,EAAOqT,EAAAkC,GAAQ,MAC/B,OAAAlW,EAAAW,EAAgBoT,EAAA7Q,EAAO8Q,EAAAkC,GAAQ,MAC/B,OAAAlW,EAAAkW,GAAiBnC,EAAA7Q,EAAO8Q,EAAArT,EAAO,MAC/B,OAAAX,EAAAkW,GAAiBnC,EAAApT,EAAOqT,EAAA9Q,EAAO,MAC/B,OAAAlD,EAAAW,EAAgBoT,EAAAmC,GAAQlC,EAAA9Q,EAAO,MAC/B,OAAAlD,EAAAkD,EAAgB6Q,EAAAmC,GAAQlC,EAAArT,EAAO,MAG/B,OAAAX,EAAA,IAAA+T,EAAA,IAAAC,EAAA,MAGAlB,QAAAO,KAAAN,IAAA,SAAAM,MACA,IAAA9T,EAAA8T,KAAA,OACA,IAAA/T,EAAA+T,KAAA,OACA,IAAAqB,EAAArB,KAAA,OACA,IAAAsB,EAAAtB,KAAA,OACA,IAAArT,EACA,IAAA+T,EACA,IAAAC,EAEAhU,EAAA,EAAA8F,KAAAmO,IAAA,EAAA1U,GAAA,EAAAoV,MACAZ,EAAA,EAAAjO,KAAAmO,IAAA,EAAA3U,GAAA,EAAAqV,MACAX,EAAA,EAAAlO,KAAAmO,IAAA,EAAAS,GAAA,EAAAC,MAEA,OAAA3U,EAAA,IAAA+T,EAAA,IAAAC,EAAA,MAGAlB,QAAAQ,IAAAP,IAAA,SAAAO,KACA,IAAAuB,EAAAvB,IAAA,OACA,IAAAoB,EAAApB,IAAA,OACA,IAAA8B,EAAA9B,IAAA,OACA,IAAAtT,EACA,IAAA+T,EACA,IAAAC,EAEAhU,EAAA6U,EAAA,OAAAH,GAAA,OAAAU,GAAA,MACArB,EAAAc,GAAA,MAAAH,EAAA,OAAAU,EAAA,MACApB,EAAAa,EAAA,MAAAH,GAAA,KAAAU,EAAA,MAGApV,IAAA,SACA,MAAA8F,KAAAgP,IAAA9U,EAAA,YACAA,EAAA,MAEA+T,IAAA,SACA,MAAAjO,KAAAgP,IAAAf,EAAA,YACAA,EAAA,MAEAC,IAAA,SACA,MAAAlO,KAAAgP,IAAAd,EAAA,YACAA,EAAA,MAEAhU,EAAA8F,KAAAmO,IAAAnO,KAAAoO,IAAA,EAAAlU,GAAA,GACA+T,EAAAjO,KAAAmO,IAAAnO,KAAAoO,IAAA,EAAAH,GAAA,GACAC,EAAAlO,KAAAmO,IAAAnO,KAAAoO,IAAA,EAAAF,GAAA,GAEA,OAAAhU,EAAA,IAAA+T,EAAA,IAAAC,EAAA,MAGAlB,QAAAQ,IAAAC,IAAA,SAAAD,KACA,IAAAuB,EAAAvB,IAAA,GACA,IAAAoB,EAAApB,IAAA,GACA,IAAA8B,EAAA9B,IAAA,GACA,IAAAnU,EACA,IAAAgE,EACA,IAAA6Q,EAEAa,GAAA,OACAH,GAAA,IACAU,GAAA,QAEAP,IAAA,QAAA/O,KAAAgP,IAAAD,EAAA,WAAAA,EAAA,OACAH,IAAA,QAAA5O,KAAAgP,IAAAJ,EAAA,WAAAA,EAAA,OACAU,IAAA,QAAAtP,KAAAgP,IAAAM,EAAA,WAAAA,EAAA,OAEAjW,EAAA,IAAAuV,EAAA,GACAvR,EAAA,KAAA0R,EAAAH,GACAV,EAAA,KAAAU,EAAAU,GAEA,OAAAjW,EAAAgE,EAAA6Q,IAGAlB,QAAAS,IAAAD,IAAA,SAAAC,KACA,IAAApU,EAAAoU,IAAA,GACA,IAAApQ,EAAAoQ,IAAA,GACA,IAAAS,EAAAT,IAAA,GACA,IAAAsB,EACA,IAAAH,EACA,IAAAU,EAEAV,GAAAvV,EAAA,QACA0V,EAAA1R,EAAA,IAAAuR,EACAU,EAAAV,EAAAV,EAAA,IAEA,IAAAqC,GAAAvQ,KAAAgP,IAAAJ,EAAA,GACA,IAAA4B,GAAAxQ,KAAAgP,IAAAD,EAAA,GACA,IAAA0B,GAAAzQ,KAAAgP,IAAAM,EAAA,GACAV,EAAA2B,GAAA,QAAAA,IAAA3B,EAAA,cACAG,EAAAyB,GAAA,QAAAA,IAAAzB,EAAA,cACAO,EAAAmB,GAAA,QAAAA,IAAAnB,EAAA,cAEAP,GAAA,OACAH,GAAA,IACAU,GAAA,QAEA,OAAAP,EAAAH,EAAAU,IAGAtC,QAAAS,IAAAC,IAAA,SAAAD,KACA,IAAApU,EAAAoU,IAAA,GACA,IAAApQ,EAAAoQ,IAAA,GACA,IAAAS,EAAAT,IAAA,GACA,IAAAiD,GACA,IAAAvT,EACA,IAAA1D,EAEAiX,GAAA1Q,KAAA2Q,MAAAzC,EAAA7Q,GACAF,EAAAuT,GAAA,MAAA1Q,KAAA4Q,GAEA,GAAAzT,EAAA,GACAA,GAAA,IAGA1D,EAAAuG,KAAA6Q,KAAAxT,IAAA6Q,KAEA,OAAA7U,EAAAI,EAAA0D,IAGA6P,QAAAU,IAAAD,IAAA,SAAAC,KACA,IAAArU,EAAAqU,IAAA,GACA,IAAAjU,EAAAiU,IAAA,GACA,IAAAvQ,EAAAuQ,IAAA,GACA,IAAArQ,EACA,IAAA6Q,EACA,IAAAwC,GAEAA,GAAAvT,EAAA,MAAA6C,KAAA4Q,GACAvT,EAAA5D,EAAAuG,KAAA8Q,IAAAJ,IACAxC,EAAAzU,EAAAuG,KAAA+Q,IAAAL,IAEA,OAAArX,EAAAgE,EAAA6Q,IAGAlB,QAAAC,IAAAY,OAAA,SAAAnQ,MACA,IAAAxD,EAAAwD,KAAA,GACA,IAAAuQ,EAAAvQ,KAAA,GACA,IAAAwQ,EAAAxQ,KAAA,GACA,IAAArD,MAAA,KAAAqB,oBAAA,GAAAsR,QAAAC,IAAAI,IAAA3P,MAAA,GAEArD,MAAA2F,KAAAf,MAAA5E,MAAA,IAEA,GAAAA,QAAA,GACA,UAGA,IAAA2W,KAAA,IACAhR,KAAAf,MAAAiP,EAAA,QACAlO,KAAAf,MAAAgP,EAAA,QACAjO,KAAAf,MAAA/E,EAAA,MAEA,GAAAG,QAAA,GACA2W,MAAA,GAGA,OAAAA,MAGAhE,QAAAK,IAAAQ,OAAA,SAAAnQ,MAGA,OAAAsP,QAAAC,IAAAY,OAAAb,QAAAK,IAAAJ,IAAAvP,WAAA,KAGAsP,QAAAC,IAAAa,QAAA,SAAApQ,MACA,IAAAxD,EAAAwD,KAAA,GACA,IAAAuQ,EAAAvQ,KAAA,GACA,IAAAwQ,EAAAxQ,KAAA,GAIA,GAAAxD,IAAA+T,OAAAC,EAAA,CACA,GAAAhU,EAAA,GACA,UAGA,GAAAA,EAAA,KACA,WAGA,OAAA8F,KAAAf,OAAA/E,EAAA,eAGA,IAAA8W,KAAA,GACA,GAAAhR,KAAAf,MAAA/E,EAAA,OACA,EAAA8F,KAAAf,MAAAgP,EAAA,OACAjO,KAAAf,MAAAiP,EAAA,OAEA,OAAA8C,MAGAhE,QAAAa,OAAAZ,IAAA,SAAAvP,MACA,IAAAb,MAAAa,KAAA,GAGA,GAAAb,QAAA,GAAAA,QAAA,GACA,GAAAa,KAAA,IACAb,OAAA,IAGAA,YAAA,SAEA,OAAAA,mBAGA,IAAAoU,SAAAvT,KAAA,UACA,IAAAxD,GAAA2C,MAAA,GAAAoU,KAAA,IACA,IAAAhD,GAAApR,OAAA,KAAAoU,KAAA,IACA,IAAA/C,GAAArR,OAAA,KAAAoU,KAAA,IAEA,OAAA/W,EAAA+T,EAAAC,IAGAlB,QAAAc,QAAAb,IAAA,SAAAvP,MAEA,GAAAA,MAAA,KACA,IAAAjE,GAAAiE,KAAA,UACA,OAAAjE,OAGAiE,MAAA,GAEA,IAAAwT,IACA,IAAAhX,EAAA8F,KAAA+P,MAAArS,KAAA,UACA,IAAAuQ,EAAAjO,KAAA+P,OAAAmB,IAAAxT,KAAA,aACA,IAAAwQ,EAAAgD,IAAA,QAEA,OAAAhX,EAAA+T,EAAAC,IAGAlB,QAAAC,IAAAU,IAAA,SAAAjQ,MACA,IAAAyT,UAAAnR,KAAAf,MAAAvB,KAAA,gBACAsC,KAAAf,MAAAvB,KAAA,cACAsC,KAAAf,MAAAvB,KAAA,SAEA,IAAAsB,OAAAmS,QAAAC,SAAA,IAAA9N,cACA,eAAA+N,UAAArS,OAAArD,QAAAqD,QAGAgO,QAAAW,IAAAV,IAAA,SAAAvP,MACA,IAAA0C,MAAA1C,KAAA0T,SAAA,IAAAhR,MAAA,4BACA,IAAAA,MAAA,CACA,cAGA,IAAAkR,YAAAlR,MAAA,GAEA,GAAAA,MAAA,GAAAzE,SAAA,GACA2V,wBAAAC,MAAA,IAAAC,IAAA,SAAAC,MACA,OAAAA,YACGC,KAAA,IAGH,IAAAP,QAAAQ,SAAAL,YAAA,IACA,IAAApX,EAAAiX,SAAA,OACA,IAAAlD,EAAAkD,SAAA,MACA,IAAAjD,EAAAiD,QAAA,IAEA,OAAAjX,EAAA+T,EAAAC,IAGAlB,QAAAC,IAAAc,IAAA,SAAAd,KACA,IAAA/S,EAAA+S,IAAA,OACA,IAAAgB,EAAAhB,IAAA,OACA,IAAAiB,EAAAjB,IAAA,OACA,IAAAmB,IAAApO,KAAAoO,IAAApO,KAAAoO,IAAAlU,EAAA+T,GAAAC,GACA,IAAAC,IAAAnO,KAAAmO,IAAAnO,KAAAmO,IAAAjU,EAAA+T,GAAAC,GACA,IAAA0D,OAAAxD,IAAAD,IACA,IAAA0D,UACA,IAAAlT,IAEA,GAAAiT,OAAA,GACAC,UAAA1D,KAAA,EAAAyD,YACE,CACFC,UAAA,EAGA,GAAAD,QAAA,GACAjT,IAAA,OAEA,GAAAyP,MAAAlU,EAAA,CACAyE,KAAAsP,EAAAC,GAAA0D,OAAA,OAEA,GAAAxD,MAAAH,EAAA,CACAtP,IAAA,GAAAuP,EAAAhU,GAAA0X,WACE,CACFjT,IAAA,GAAAzE,EAAA+T,GAAA2D,OAAA,EAGAjT,KAAA,EACAA,KAAA,EAEA,OAAAA,IAAA,IAAAiT,OAAA,IAAAC,UAAA,MAGA7E,QAAAI,IAAAW,IAAA,SAAAX,KACA,IAAA/R,EAAA+R,IAAA,OACA,IAAA/T,EAAA+T,IAAA,OACA,IAAA3T,EAAA,EACA,IAAAuW,EAAA,EAEA,GAAA3W,EAAA,IACAI,EAAA,EAAA4B,EAAAhC,MACE,CACFI,EAAA,EAAA4B,GAAA,EAAAhC,GAGA,GAAAI,EAAA,GACAuW,GAAA3W,EAAA,GAAAI,IAAA,EAAAA,GAGA,OAAA2T,IAAA,GAAA3T,EAAA,IAAAuW,EAAA,MAGAhD,QAAAK,IAAAU,IAAA,SAAAV,KACA,IAAAhS,EAAAgS,IAAA,OACA,IAAAjQ,EAAAiQ,IAAA,OAEA,IAAA5T,EAAA4B,EAAA+B,EACA,IAAA4S,EAAA,EAEA,GAAAvW,EAAA,GACAuW,GAAA5S,EAAA3D,IAAA,EAAAA,GAGA,OAAA4T,IAAA,GAAA5T,EAAA,IAAAuW,EAAA,MAGAhD,QAAAe,IAAAd,IAAA,SAAAc,KACA,IAAA5Q,EAAA4Q,IAAA,OACA,IAAAtU,EAAAsU,IAAA,OACA,IAAAE,EAAAF,IAAA,OAEA,GAAAtU,IAAA,GACA,OAAAwU,EAAA,IAAAA,EAAA,IAAAA,EAAA,KAGA,IAAA6D,MAAA,OACA,IAAAhC,GAAA3S,EAAA,IACA,IAAAC,EAAA0S,GAAA,EACA,IAAAnB,EAAA,EAAAvR,EACA,IAAA2U,GAAA,EAEA,OAAA/R,KAAA+P,MAAAD,KACA,OACAgC,KAAA,KAAeA,KAAA,GAAA1U,EAAa0U,KAAA,KAAa,MACzC,OACAA,KAAA,GAAAnD,EAAemD,KAAA,KAAaA,KAAA,KAAa,MACzC,OACAA,KAAA,KAAeA,KAAA,KAAaA,KAAA,GAAA1U,EAAa,MACzC,OACA0U,KAAA,KAAeA,KAAA,GAAAnD,EAAamD,KAAA,KAAa,MACzC,OACAA,KAAA,GAAA1U,EAAe0U,KAAA,KAAaA,KAAA,KAAa,MACzC,QACAA,KAAA,KAAeA,KAAA,KAAaA,KAAA,GAAAnD,EAG5BoD,IAAA,EAAAtY,GAAAwU,EAEA,QACAxU,EAAAqY,KAAA,GAAAC,IAAA,KACAtY,EAAAqY,KAAA,GAAAC,IAAA,KACAtY,EAAAqY,KAAA,GAAAC,IAAA,MAIA/E,QAAAe,IAAAV,IAAA,SAAAU,KACA,IAAAtU,EAAAsU,IAAA,OACA,IAAAE,EAAAF,IAAA,OAEA,IAAA3Q,EAAA3D,EAAAwU,GAAA,EAAAxU,GACA,IAAAuW,EAAA,EAEA,GAAA5S,EAAA,GACA4S,EAAAvW,EAAA2D,EAGA,OAAA2Q,IAAA,GAAAiC,EAAA,IAAA5S,EAAA,MAGA4P,QAAAe,IAAAX,IAAA,SAAAW,KACA,IAAAtU,EAAAsU,IAAA,OACA,IAAAE,EAAAF,IAAA,OAEA,IAAA1U,EAAA4U,GAAA,EAAAxU,GAAA,GAAAA,EACA,IAAA4B,EAAA,EAEA,GAAAhC,EAAA,GAAAA,EAAA,IACAgC,EAAA5B,GAAA,EAAAJ,QAEA,GAAAA,GAAA,IAAAA,EAAA,GACAgC,EAAA5B,GAAA,KAAAJ,IAGA,OAAA0U,IAAA,GAAA1S,EAAA,IAAAhC,EAAA,MAGA2T,QAAAe,IAAAT,IAAA,SAAAS,KACA,IAAAtU,EAAAsU,IAAA,OACA,IAAAE,EAAAF,IAAA,OACA,IAAA3Q,EAAA3D,EAAAwU,GAAA,EAAAxU,GACA,OAAAsU,IAAA,IAAA3Q,EAAA3D,GAAA,OAAA2D,GAAA,MAGA4P,QAAAM,IAAAS,IAAA,SAAAT,KACA,IAAAqB,EAAArB,IAAA,OACA,IAAAY,EAAAZ,IAAA,OACA,IAAAlQ,EAAA,EAAA8Q,EACA,IAAAzU,EAAA2D,EAAAuR,EACA,IAAAV,EAAA,EAEA,GAAAxU,EAAA,GACAwU,GAAA7Q,EAAA3D,IAAA,EAAAA,GAGA,OAAA6T,IAAA,GAAA7T,EAAA,IAAAwU,EAAA,MAGAjB,QAAAgB,MAAAf,IAAA,SAAAe,OACA,OAAAA,MAAA,aAAAA,MAAA,aAAAA,MAAA,eAGAhB,QAAAC,IAAAe,MAAA,SAAAf,KACA,OAAAA,IAAA,aAAAA,IAAA,aAAAA,IAAA,eAGAD,QAAAjG,KAAAkG,IAAA,SAAAvP,MACA,OAAAA,KAAA,WAAAA,KAAA,WAAAA,KAAA,aAGAsP,QAAAjG,KAAAqG,IAAAJ,QAAAjG,KAAAsG,IAAA,SAAA3P,MACA,WAAAA,KAAA,KAGAsP,QAAAjG,KAAAuG,IAAA,SAAAvG,MACA,aAAAA,KAAA,KAGAiG,QAAAjG,KAAAwG,KAAA,SAAAxG,MACA,aAAAA,KAAA,KAGAiG,QAAAjG,KAAA0G,IAAA,SAAA1G,MACA,OAAAA,KAAA,SAGAiG,QAAAjG,KAAA4G,IAAA,SAAA5G,MACA,IAAA2I,IAAA1P,KAAAf,MAAA8H,KAAA,gBACA,IAAAoK,SAAAzB,KAAA,KAAAA,KAAA,GAAAA,IAEA,IAAA1Q,OAAAmS,QAAAC,SAAA,IAAA9N,cACA,eAAA+N,UAAArS,OAAArD,QAAAqD,QAGAgO,QAAAC,IAAAlG,KAAA,SAAAkG,KACA,IAAAyC,KAAAzC,IAAA,GAAAA,IAAA,GAAAA,IAAA,MACA,OAAAyC,IAAA,4SCh2BA,IAAAsC,aAAA9Y,oBAAA,0DACA,IAAAoC,QAAApC,oBAAA,kIAEA,IAAI+Y,OAAS,cAEb/V,iBAAE+V,QAAUC,sBAGZhW,iBAAEsB,GAAGyU,QAAU,SAAUE,QACvB,IAAIC,OAASzU,MAAMzC,UAAUmX,MAAM9Y,KAAKmC,UAAW,GACjD4W,gBAAmBxW,KAAKH,SAAW,EACnC4W,YAAc,KAEhB,IAAIC,UAAY1W,KAAK2W,KAAK,WACxB,IAAIC,OAAQ,EAAAC,SAAApU,SAAEzC,MACZ8W,KAAOF,MAAMG,KAAKZ,QAClBxW,gBAAmB0W,SAAP,wBAAAjP,QAAOiP,WAAW,SAAYA,UAG5C,IAAKS,KAAM,CACTA,KAAO,IAAIV,sBAAYpW,KAAML,SAC7BiX,MAAMG,KAAKZ,OAAQW,MAGrB,IAAKN,gBAAiB,CACpB,OAGFC,YAAcG,MAEd,UAAWP,SAAW,SAAU,CAC9B,GAAIA,SAAW,cAAe,CAE5BI,YAAcK,UACT,GAAI1W,iBAAE4W,WAAWF,KAAKT,SAAU,CAErCI,YAAcK,KAAKT,QAAQrU,MAAM8U,KAAMR,YAClC,CAELG,YAAcK,KAAKT,YAKzB,OAAOG,gBAAkBC,YAAcC,WAGzCtW,iBAAEsB,GAAGyU,QAAQc,YAAcb,qoBC/C3B,IAAAc,WAAA9Z,oBAAA,sDACA,IAAA+Z,SAAA/Z,oBAAA,kDACA,IAAAga,YAAAha,oBAAA,wDACA,IAAAoC,QAAApC,oBAAA,gDACA,IAAAia,eAAAja,oBAAA,+DACA,IAAAka,cAAAla,oBAAA,6DACA,IAAAma,cAAAna,oBAAA,6DACA,IAAAoa,cAAApa,oBAAA,6DACA,IAAAqa,eAAAra,oBAAA,+DACA,IAAAsa,cAAAta,oBAAA,6DACA,IAAAua,WAAAva,oBAAA,wRAEA,IAAIwa,qBAAuB,EAE3B,IAAIlb,YAAemb,OAAS,YAAcA,KAA9B/X,cAKNsW,iFA2BF,OAAOpW,KAAK8X,aAAa/W,yCASzB,OAAOf,KAAK8X,aAAa5V,0CASzB,OAAOlC,KAAK+X,cAAcC,2CArC1B,OAAOvW,0DAUP,OAAOhC,wBAoCT,SAAA2W,YAAYnW,QAASN,SAASI,gBAAAC,KAAAoW,aAC5BwB,sBAAwB,EAKxB5X,KAAKiY,GAAKL,qBAOV5X,KAAKkY,WACHC,MAAO,KACP/T,EAAG,MAQLpE,KAAKC,SAAU,EAAA4W,SAAApU,SAAExC,SACdmY,SAAS,uBACTC,KAAK,sBAAuBrY,KAAKiY,IAKpCjY,KAAKL,QAAUS,iBAAE+G,OAAO,QAAUP,kBAAUjH,QAASK,KAAKC,QAAQ8W,QAMlE/W,KAAKsY,SAAW,MAOhBtY,KAAKiG,cAMLjG,KAAKqF,UACHrF,KAAKL,QAAQ0F,YAAc,MAC1BrF,KAAKL,QAAQ0F,YAAc,MAAQrF,KAAKL,QAAQyF,SAAW,KAC1DpF,KAAKC,QAAUD,KAAKL,QAAQ0F,UAEhCrF,KAAKqF,UAAarF,KAAKqF,YAAc,OAAS,EAAAwR,SAAApU,SAAEzC,KAAKqF,WAAa,MAKlErF,KAAKuY,aAAe,IAAIC,uBAAaxY,MAIrCA,KAAK8X,aAAe,IAAIW,uBAAazY,MAIrCA,KAAK0Y,cAAgB,IAAIC,wBAAc3Y,MAIvCA,KAAK4Y,aAAe,IAAIC,uBAAa7Y,KAAMtD,MAI3CsD,KAAK+X,cAAgB,IAAIe,wBAAc9Y,MAIvCA,KAAK+Y,aAAe,IAAIC,uBAAahZ,MAErCA,KAAKiZ,QAGL,EAAApC,SAAApU,SAAErC,iBAAEC,MAAM,WAMRL,KAAKkZ,QAAQ,sBACZlZ,mEASHA,KAAK+Y,aAAaja,OAGlBkB,KAAKuY,aAAazZ,OAGlBkB,KAAKmZ,iBAGLnZ,KAAK8X,aAAahZ,OAGlBkB,KAAK+X,cAAcjZ,OAGnBkB,KAAK0Y,cAAc5Z,OACnBkB,KAAK4Y,aAAa9Z,OAGlBkB,KAAK+X,cAAcqB,SAGnBpZ,KAAKqZ,SAEL,GAAIrZ,KAAKuY,aAAae,aAAc,CAClCtZ,KAAKuZ,mEAQQ,IAAAxS,MAAA/G,KACf,IAAK6B,MAAM6B,QAAQ1D,KAAKL,QAAQsG,YAAa,CAC3CjG,KAAKL,QAAQsG,cAGf,GAAIjG,KAAKL,QAAQ+F,MAAO,CACtB1F,KAAKL,QAAQsG,WAAWhC,MAAMpG,KAAM,aAItCmC,KAAKL,QAAQsG,WAAWnC,QAAQ,SAAC0V,KAC/BzS,MAAK0S,kBAAkBrD,YAAYnQ,WAAWuT,IAAI3b,KAAK0G,eAAgBiV,IAAI7Z,2EAW7D+Z,gBAA6B,IAAbC,OAAa/Z,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,MAC7C,IAAI4Z,IAAM,IAAIE,eAAe1Z,KAAM2Z,QAEnC3Z,KAAKiG,WAAWhC,KAAKuV,KACrB,OAAOA,8CASP,IAAIzY,MAAQf,KAAKe,MAEjBf,KAAK0Y,cAAckB,SACnB5Z,KAAKuY,aAAaqB,SAClB5Z,KAAK4Y,aAAagB,SAClB5Z,KAAK8X,aAAa8B,SAClB5Z,KAAK+Y,aAAaa,SAClB5Z,KAAK+X,cAAc6B,SAEnB5Z,KAAKC,QACF4Z,YAAY,uBACZC,WAAW,cAAe,SAC1B5Y,IAAI,gBAOPlB,KAAKkZ,QAAQ,qBAAsBnY,yCAUhCqD,GACHpE,KAAK4Y,aAAamB,KAAK3V,qCASpBA,GACHpE,KAAK4Y,aAAaoB,KAAK5V,yCAUlBA,GACLpE,KAAK4Y,aAAaqB,OAAO7V,+CASG,IAArBuD,aAAqB/H,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,GAAN,KACtB,IAAIgU,IAAM5T,KAAK8X,aAAa/W,MAE5B6S,IAAOA,eAAenS,oBAAamS,IAAMjM,aAEzC,GAAIiM,eAAenS,oBAAW,CAC5B,OAAOmS,IAAI1Q,OAAOlD,KAAKkC,QAGzB,OAAO0R,8CASAA,KACP,GAAI5T,KAAKsZ,aAAc,CACrB,OAEF,IAAIY,GAAKla,KAAK8X,aAEd,GACGoC,GAAGC,cAAgBvG,KAAOsG,GAAGnZ,MAAMqZ,OAAOxG,OACzCsG,GAAGC,aAAevG,IACpB,CAEA,OAGFsG,GAAGnZ,MAAQ6S,IAAMsG,GAAGG,YAAYzG,IAAK5T,KAAKL,QAAQkG,mBAAqB,KAOvE7F,KAAKkZ,QAAQ,oBAAqBgB,GAAGnZ,MAAO6S,KAG5C5T,KAAKqZ,iDASL,GAAIrZ,KAAK8X,aAAaqC,WAAY,CAChCna,KAAKuY,aAAac,aACb,CACLrZ,KAAK8X,aAAawC,cAGpBta,KAAK+Y,aAAaM,SAClBrZ,KAAK+X,cAAcsB,SAOnBrZ,KAAKkZ,QAAQ,6DAUblZ,KAAKuY,aAAagC,SAClBva,KAAKsY,SAAW,MAChBtY,KAAKgY,OAAO6B,YAAY,wBAOxB7Z,KAAKkZ,QAAQ,qBACb,OAAO,+CAUPlZ,KAAKuY,aAAagB,UAClBvZ,KAAKsY,SAAW,KAChBtY,KAAKgY,OAAOI,SAAS,wBAOrBpY,KAAKkZ,QAAQ,sBACb,OAAO,mDAQP,OAAQlZ,KAAKsZ,6DAQb,OAAOtZ,KAAKsY,WAAa,6CAUnBkC,WAAuC,IAA5BzZ,MAA4BnB,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,GAApB,KAAoB,IAAdrB,MAAcqB,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,GAAN,KACvCI,KAAKC,QAAQiZ,SACXuB,KAAMD,UACN9a,YAAaM,KACbe,MAAOA,MAAQA,MAAQf,KAAKe,MAC5BxC,MAAOA,MAAQA,MAAQyB,KAAKyH,uCAWlC2O,YAAYnQ,WAAayU,qCAEVtE,2OCpcf,IAAAuE,UAAAvd,oBAAA,qDACA,IAAAwd,SAAAxd,oBAAA,mDACA,IAAAyd,UAAAzd,oBAAA,qDACA,IAAA0d,SAAA1d,oBAAA,4IAGE2d,oCAAUC,kCAASC,oCAAUnU,2CAI7BoU,SAAYH,mBACZI,QAAWH,kBACXI,SAAYH,mBACZI,QAAWvU,6iCCXb,IAAAH,YAAAvJ,oBAAA,uDACA,IAAAoC,QAAApC,oBAAA,+6BAOM2d,6DACJ,SAAAA,SAAYrb,aAA2B,IAAdC,QAAcC,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,MAAAG,gBAAAC,KAAA+a,UAAA,IAAAhU,MAAAC,2BAAAhH,MAAA+a,SAAA9T,WAAAjJ,OAAAkJ,eAAA6T,WAAAtd,KAAAuC,KAC/BN,YAAaC,UAKnBoH,MAAKuU,aAAe,EACpB,GAAIvU,MAAKrH,YAAY6Y,aAAagD,WAAY,CAC5CxU,MAAKrH,YAAY6Y,aAAa5S,MAAMxF,GAAG,yBAA0BC,iBAAEC,MAAM0G,MAAKyU,cAAbzU,QAR9B,OAAAA,2DAiBnCyT,WAAoB,IAAAiB,SAAA,QAAA9Z,KAAA/B,UAAAC,OAAN+B,KAAMC,MAAAF,KAAA,EAAAA,KAAA,KAAAG,KAAA,EAAAA,KAAAH,KAAAG,OAAA,CAANF,KAAME,KAAA,GAAAlC,UAAAkC,MACtB9B,KAAKsb,cAAgB,EAErB,IAAII,eAAiB1b,KAAKsb,aAAtB,iBAAmDtb,KAAKN,YAAYuY,GAApE,KAA2EuC,UAA3E,KAEJiB,SAAAE,SAAQjW,MAAR1D,MAAAyZ,UAAcC,YAAdE,OAA6Bha,OAY7B5B,KAAKN,YAAYO,QAAQiZ,SACvBuB,KAAM,mBACN/a,YAAaM,KAAKN,YAClBqB,MAAOf,KAAKe,MACZxC,MAAO,KACPmH,OACEwV,SAAUlb,KACVwa,UACAqB,QAASja,KACT8Z,gEAKO3a,OAAyB,IAAlBC,UAAkBpB,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,GAAN,KAC9BI,KAAK8b,IAAI,iBAAkB/a,MAAOC,WAClC,OAAO,gDAGAC,OACPjB,KAAK8b,IAAI,qBACT,OAAAC,KAAAhB,SAAA3b,UAAA6H,WAAAjJ,OAAAkJ,eAAA6T,SAAA3b,WAAA,WAAAY,MAAAvC,KAAAuC,KAAsBiB,mDAGdA,OACRjB,KAAK8b,IAAI,sBACT9b,KAAKsb,aAAe,EAEpB,GAAItb,KAAKN,YAAY6Y,aAAagD,WAAY,CAC5Cvb,KAAKN,YAAY6Y,aAAa5S,MAAMzE,IAAI,oBAG1C,OAAA6a,KAAAhB,SAAA3b,UAAA6H,WAAAjJ,OAAAkJ,eAAA6T,SAAA3b,WAAA,YAAAY,MAAAvC,KAAAuC,KAAuBiB,iDAGhBA,OACPjB,KAAK8b,IAAI,yEAOG7a,OACZjB,KAAK8b,IAAI,2BAA4B7a,MAAM1C,MAAO0C,MAAMF,iDAGjDE,OACPjB,KAAK8b,IAAI,oBAAqB7a,MAAM1C,MAAO0C,MAAMF,mDAGzCE,OACRjB,KAAK8b,IAAI,qBAAsB7a,MAAM1C,MAAO0C,MAAMF,6CAG7CE,OACLjB,KAAK8b,IAAI,mBACT9b,KAAKsb,aAAe,wCAGfra,OACLjB,KAAK8b,IAAI,+DAGD7a,OACRjB,KAAK8b,IAAI,gEAGF7a,OACPjB,KAAK8b,IAAI,0CAxGUrc,qCA4GRsb,kkCCpHf,IAAApU,YAAAvJ,oBAAA,uDACA,IAAAoC,QAAApC,oBAAA,+6BAMM4d,2DACJ,SAAAA,QAAYtb,aAA2B,IAAdC,QAAcC,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,MAAAG,gBAAAC,KAAAgb,SAAA,IAAAjU,MAAAC,2BAAAhH,MAAAgb,QAAA/T,WAAAjJ,OAAAkJ,eAAA8T,UAAAvd,KAAAuC,KAC/BN,YAAaU,iBAAE+G,OAAO,SAExBnB,SAAU,iEACVE,SAAU,KACVhE,OAAQxC,YAAYwC,QAEtBvC,WAGFoH,MAAK9G,SAAU,EAAA4W,SAAApU,SAAEsE,MAAKpH,QAAQqG,UAC9Be,MAAKiV,aAAejV,MAAK9G,QAAQgc,KAAK,OAXD,OAAAlV,oEAc9B9F,OACP8a,KAAAf,QAAA5b,UAAA6H,WAAAjJ,OAAAkJ,eAAA8T,QAAA5b,WAAA,WAAAY,MAAAvC,KAAAuC,KAAeiB,OACfjB,KAAKN,YAAYsY,OAAOkE,OAAOlc,KAAKC,mDAG7BgB,OACP8a,KAAAf,QAAA5b,UAAA6H,WAAAjJ,OAAAkJ,eAAA8T,QAAA5b,WAAA,WAAAY,MAAAvC,KAAAuC,KAAeiB,OAEf,IAAKA,MAAMF,MAAO,CAChBf,KAAKgc,aACFG,IAAI,kBAAmB,MACvBA,IAAI,QAAS,MACbC,KAAK,IACR,OAGFpc,KAAKgc,aACFG,IAAI,kBAAmBlb,MAAMF,MAAMsb,eAEtC,GAAIrc,KAAKL,QAAQuG,SAAU,CACzBlG,KAAKgc,aACFI,KAAKnb,MAAMF,MAAMmC,OAAOlD,KAAKL,QAAQuC,QAAUlC,KAAKN,YAAYwC,SAEnE,GAAIjB,MAAMF,MAAMuC,UAAarC,MAAMF,MAAMgC,MAAQ,GAAM,CACrD/C,KAAKgc,aAAaG,IAAI,QAAS,aAC1B,CACLnc,KAAKgc,aAAaG,IAAI,QAAS,+BAzCjB1c,qCA+CPub,ikCCtDf,IAAAsB,UAAAlf,oBAAA,mDACA,IAAAoC,QAAApC,oBAAA,26BAEA,IAAIwJ,UACF2V,8JAGAC,eAAgB,mFAOZvB,yDACJ,SAAAA,SAAYvb,aAA2B,IAAdC,QAAcC,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,MAAAG,gBAAAC,KAAAib,UAAA,IAAAlU,MAAAC,2BAAAhH,MAAAib,SAAAhU,WAAAjJ,OAAAkJ,eAAA+T,WAAAxd,KAAAuC,KAC/BN,YAAaU,iBAAE+G,OAAO,QAAUP,SAAUjH,WAChDoH,MAAK9G,QAAU,KAFsB,OAAA8G,yEAMrC,OAAO/G,KAAKsH,YAAc,4CAGnBrG,OACP8a,KAAAd,SAAA7b,UAAA6H,WAAAjJ,OAAAkJ,eAAA+T,SAAA7b,WAAA,WAAAY,MAAAvC,KAAAuC,KAAeiB,OAEf,IAAKjB,KAAKyc,YAAa,CACrB,OAGFzc,KAAKC,SAAU,EAAA4W,SAAApU,SAAEzC,KAAKL,QAAQ4c,aAC9Bvc,KAAK0c,OACL1c,KAAKN,YAAYsY,OAAOkE,OAAOlc,KAAKC,6CAG/B,IAAA0c,OAAA3c,KACL,IAAIN,YAAcM,KAAKN,YACrBkd,gBAAkB5c,KAAKC,QAAQgc,KAAK,gCACpCY,UAAa7c,KAAKL,QAAQkH,gBAAkB,OAAUhF,MAAM6B,QAAQ1D,KAAK4D,QAE3EgZ,gBAAgBE,QAEhB1c,iBAAEuW,KAAK3W,KAAK4D,OAAQ,SAAC/F,KAAMU,OACzB,IAAIwe,SAAU,EAAAlG,SAAApU,SAAEka,OAAKhd,QAAQ6c,gBAC1BnE,KAAK,YAAaxa,MAClBwa,KAAK,aAAc9Z,OACnB8Z,KAAK,QAASwE,UAAehf,KAAf,KAAwBU,MAAUA,OAChD4B,GAAG,+CACF,SAAUiE,GACR,IAAI4Y,KAAM,EAAAnG,SAAApU,SAAEzC,MAIZN,YAAYud,SAASJ,UAAYG,IAAI3E,KAAK,aAAe2E,IAAI3E,KAAK,iBAIxE0E,QAAQd,KAAK,8BACVE,IAAI,mBAAoB5d,OAE3Bqe,gBAAgBV,OAAOa,WAGzBH,gBAAgBV,QAAO,EAAArF,SAAApU,SAAE,2DAlDNqE,mCAsDRmU,upBCpEf,IAAAzb,QAAApC,oBAAA,sRAMMub,yBAIJ,SAAAA,cAAYjZ,aAAaK,gBAAAC,KAAA2Y,eAIvB3Y,KAAKN,YAAcA,YAKnBM,KAAKkd,cAAgB,KAKrBld,KAAKmd,cACHC,KAAM,EACNC,IAAK,GAMPrd,KAAKsd,OAASld,iBAAEC,MAAML,KAAKud,cAAevd,oFAU9Bqd,IAAKD,MACjB,IAAKpd,KAAKkd,cAAe,CACvB,OAGF,IAAIM,OAASxd,KAAKkd,cAAeO,GAAKzd,KAAKN,YAAawa,GAAKuD,GAAG3F,aAGhE,IAAI/W,OAASmZ,GAAGC,WAAaD,GAAGwD,mBAAqBxD,GAAGnZ,MAAM4c,WAG9DH,OAAOI,WAAWR,KAAOA,KAAO,KAChCI,OAAOI,WAAWP,IAAMA,IAAM,KAG9B,GAAIG,OAAOjX,SAAU,CACnBxF,MAAMyc,OAAOjX,UAAU6W,KAAOI,OAAOnX,SAEvC,GAAImX,OAAOhX,QAAS,CAClBzF,MAAMyc,OAAOhX,SAAS6W,IAAMG,OAAOlX,QAIrCmX,GAAGR,SAASlc,OACZ0c,GAAG7E,aAAaiF,4CAOhB,IAAI1X,QAAUnG,KAAKN,YAAYC,QAAQwF,WAAanF,KAAKN,YACtDC,QAAQ+G,YAAc1G,KAAKN,YAAYC,QAAQwG,QAElD,IAAI2X,iBAEJ,IAAK,IAAIC,cAAc5X,QAAS,CAC9B,IAAKA,QAAQ9G,eAAe0e,YAAa,CACvC,SAGFD,cAAc7Z,KAAKkC,QAAQ4X,YAAY3X,UAGzCpG,KAAKN,YAAYsY,OAAOiE,KAAK6B,cAAclI,KAAK,OAC7CzV,GAAG,+CAAgDC,iBAAEC,MAAML,KAAKge,QAAShe,gDAO5E,EAAA6W,SAAApU,SAAEzC,KAAKN,YAAYsY,QAAQ9W,KACzB+c,wBAAyB7d,iBAAEC,MAAML,KAAKke,MAAOle,MAC7Cme,wBAAyB/d,iBAAEC,MAAML,KAAKke,MAAOle,MAC7Coe,sBAAuBhe,iBAAEC,MAAML,KAAKqe,SAAUre,MAC9Cse,uBAAwBle,iBAAEC,MAAML,KAAKqe,SAAUre,gDAW3CoE,GACN,GAAIpE,KAAKN,YAAY4Z,aAAc,CACjC,OAEFtZ,KAAKN,YAAYwY,UAAUC,MAAQ,UACnCnY,KAAKN,YAAYwY,UAAU9T,EAAIA,EAE/B,IAAKA,EAAEma,QAAUna,EAAEoa,OAASpa,EAAEqa,eAAiBra,EAAEqa,cAAcC,QAAS,CACtEta,EAAEma,MAAQna,EAAEqa,cAAcC,QAAQ,GAAGH,MACrCna,EAAEoa,MAAQpa,EAAEqa,cAAcC,QAAQ,GAAGF,MAKvC,IAAIG,QAAS,EAAA9H,SAAApU,SAAE2B,EAAEua,QAGjB,IAAIC,KAAOD,OAAOE,QAAQ,OAE1B,IAAI1Y,QAAUnG,KAAKN,YAAYC,QAAQwF,WAAanF,KAAKN,YACtDC,QAAQ+G,YAAc1G,KAAKN,YAAYC,QAAQwG,QAElD,GAAIyY,KAAKE,GAAG,gBAAiB,CAC3B,OAGF9e,KAAKkd,cAAgB,KAErB,IAAK,IAAIa,cAAc5X,QAAS,CAC9B,IAAKA,QAAQ9G,eAAe0e,YAAa,CACvC,SAGF,IAAIP,OAASrX,QAAQ4X,YAErB,GAAIa,KAAKE,GAAGtB,OAAOpX,UAAW,CAC5BpG,KAAKkd,cAAgB9c,iBAAE+G,UAAWqW,QAAS3f,KAAMkgB,aACjD,WACK,GAAIP,OAAO/W,gBAAkB3G,WAAa8e,KAAKE,GAAGtB,OAAO/W,eAAgB,CAC9EzG,KAAKkd,cAAgB9c,iBAAE+G,UAAWqW,QAAS3f,KAAMkgB,aACjDa,KAAOA,KAAKG,SACZ,OAIJ,IAAIC,MAAQJ,KAAK3C,KAAK,sBAAsB9d,IAAI,GAEhD,GAAI6B,KAAKkd,gBAAkB,MAAQ8B,QAAU,KAAM,CACjD,OAGF,IAAIC,OAASL,KAAKK,SAGlBjf,KAAKkd,cAAcU,WAAaoB,MAAME,MACtClf,KAAKkd,cAAcE,KAAOhZ,EAAEma,MAAQU,OAAO7B,KAC3Cpd,KAAKkd,cAAcG,IAAMjZ,EAAEoa,MAAQS,OAAO5B,IAC1Crd,KAAKmd,cACHC,KAAMhZ,EAAEma,MACRlB,IAAKjZ,EAAEoa,QAUT,EAAA3H,SAAApU,SAAEzC,KAAKN,YAAYsY,QAAQ7X,IACzB8d,wBAAyB7d,iBAAEC,MAAML,KAAKke,MAAOle,MAC7Cme,wBAAyB/d,iBAAEC,MAAML,KAAKke,MAAOle,MAC7Coe,sBAAuBhe,iBAAEC,MAAML,KAAKqe,SAAUre,MAC9Cse,uBAAwBle,iBAAEC,MAAML,KAAKqe,SAAUre,QAC9CkZ,QAAQ,iDASP9U,GACJpE,KAAKN,YAAYwY,UAAUC,MAAQ,QACnCnY,KAAKN,YAAYwY,UAAU9T,EAAIA,EAE/B,IAAKA,EAAEma,QAAUna,EAAEoa,OAASpa,EAAEqa,eAAiBra,EAAEqa,cAAcC,QAAS,CACtEta,EAAEma,MAAQna,EAAEqa,cAAcC,QAAQ,GAAGH,MACrCna,EAAEoa,MAAQpa,EAAEqa,cAAcC,QAAQ,GAAGF,MAIvCpa,EAAE+a,iBAEF,IAAI/B,KAAOlZ,KAAKoO,IACd,EACApO,KAAKmO,IACHrS,KAAKkd,cAAc7W,QACnBrG,KAAKkd,cAAcE,OAAShZ,EAAEma,OAASve,KAAKmd,aAAaC,MAAQpd,KAAKmd,aAAaC,QAIvF,IAAIC,IAAMnZ,KAAKoO,IACb,EACApO,KAAKmO,IACHrS,KAAKkd,cAAc5W,OACnBtG,KAAKkd,cAAcG,MAAQjZ,EAAEoa,OAASxe,KAAKmd,aAAaE,KAAOrd,KAAKmd,aAAaE,OAIrFrd,KAAKsd,OAAOD,IAAKD,gDASVhZ,GACPpE,KAAKN,YAAYwY,UAAUC,MAAQ,WACnCnY,KAAKN,YAAYwY,UAAU9T,EAAIA,GAK/B,EAAAyS,SAAApU,SAAEzC,KAAKN,YAAYsY,QAAQ9W,KACzB+c,wBAAyBje,KAAKke,MAC9BC,wBAAyBne,KAAKke,MAC9BE,sBAAuBpe,KAAKqe,SAC5BC,uBAAwBte,KAAKqe,uDAKpB1F,4pBCrPf,IAAAnZ,QAAApC,oBAAA,gDACA,IAAA+Z,SAAA/Z,oBAAA,wRAMMyb,wBAKJ,SAAAA,aAAYnZ,YAAahD,MAAMqD,gBAAAC,KAAA6Y,cAI7B7Y,KAAKtD,KAAOA,KAIZsD,KAAKN,YAAcA,YAInBM,KAAKof,cAAgB,KAIrBpf,KAAKqf,WAAa,KAMlBrf,KAAKsf,SAAW,MAIhBtf,KAAKuf,QAAU,MAIfvf,KAAKwf,QAAU,mEAgDf,IAAI/B,GAAKzd,KAAKN,YAEd,GAAI+d,GAAG9d,QAAQyF,OAAQ,CACrBqY,GAAGzF,OAAOI,SAAS,0CACnB,OAGFqF,GAAGzF,OAAOI,SAAS,wCAGnB,IAAKpY,KAAKub,WAAavb,KAAKyf,SAAU,CACpC,OAIF,GAAIhC,GAAG9d,QAAQ2F,QAAS,CACtBtF,KAAK0f,gBAIP,GAAI1f,KAAKyf,SAAU,CAEjB,IAAKzf,KAAK4F,MAAMyS,KAAK,YAAa,CAChCrY,KAAK4F,MAAMyS,KAAK,WAAY,GAG9BrY,KAAK4F,MAAMzF,IACTwf,+CAAgDvf,iBAAEC,MAAML,KAAKia,OAAQja,QAGvEA,KAAK4F,MAAMzF,IACTyf,oBAAqBxf,iBAAEC,MAAML,KAAK+Z,KAAM/Z,QAG1CA,KAAK4F,MAAMzF,IACT0f,uBAAwBzf,iBAAEC,MAAML,KAAKga,KAAMha,QAK/C,GAAIA,KAAKub,WAAavb,KAAKyf,SAAU,CACnCzf,KAAK2F,MAAMxF,IACTwf,+CAAgDvf,iBAAEC,MAAML,KAAK+Z,KAAM/Z,MACnE4f,oBAAqBxf,iBAAEC,MAAML,KAAK+Z,KAAM/Z,QAG1CA,KAAK2F,MAAMxF,IACT0f,uBAAwBzf,iBAAEC,MAAML,KAAKga,KAAMha,SAK/C,EAAA6W,SAAApU,SAAEzC,KAAKtD,MAAMyD,GAAG,qBAAsBC,iBAAEC,MAAML,KAAK8f,WAAY9f,+CAO/D,GAAIA,KAAKub,SAAU,CACjBvb,KAAK2F,MAAMzE,KACTye,+CAAgDvf,iBAAEC,MAAML,KAAK+Z,KAAM/Z,MACnE4f,oBAAqBxf,iBAAEC,MAAML,KAAK+Z,KAAM/Z,QAE1CA,KAAK2F,MAAMzE,KACT2e,uBAAwBzf,iBAAEC,MAAML,KAAKga,KAAMha,QAI/C,GAAIA,KAAKyf,SAAU,CACjBzf,KAAK4F,MAAM1E,KACTye,+CAAgDvf,iBAAEC,MAAML,KAAKia,OAAQja,QAEvEA,KAAK4F,MAAM1E,KACT0e,oBAAqBxf,iBAAEC,MAAML,KAAK+Z,KAAM/Z,QAE1CA,KAAK4F,MAAM1E,KACT2e,uBAAwBzf,iBAAEC,MAAML,KAAKga,KAAMha,QAI/C,GAAIA,KAAKof,cAAe,CACtBpf,KAAKof,cAAc9Z,QAAQ,YAG7B,EAAAuR,SAAApU,SAAEzC,KAAKtD,MAAMwE,IAAI,qBAAsBd,iBAAEC,MAAML,KAAK8f,WAAY9f,QAChE,EAAA6W,SAAApU,SAAEzC,KAAKtD,KAAKqjB,UAAU7e,IAAI,+CAAgDd,iBAAEC,MAAML,KAAKga,KAAMha,QAC7F,EAAA6W,SAAApU,SAAEzC,KAAKtD,KAAKqjB,UAAU7e,IAAI,+CAAgDd,iBAAEC,MAAML,KAAKggB,iBAAkBhgB,iEAG1FoE,GACf,IAAKA,EAAG,CACN,OAAO,MAGT,OACEpE,KAAKigB,aAAajgB,KAAKqf,WAAYjb,EAAE8b,gBACrClgB,KAAKigB,aAAajgB,KAAKqf,WAAYjb,EAAEua,SACrC3e,KAAKigB,aAAajgB,KAAKN,YAAYsY,OAAQ5T,EAAE8b,gBAC7ClgB,KAAKigB,aAAajgB,KAAKN,YAAYsY,OAAQ5T,EAAEua,0DAIpCtZ,UAAWpF,SACtB,IAAKoF,YAAcpF,QAAS,CAC1B,OAAO,MAGTA,SAAU,EAAA4W,SAAApU,SAAExC,SAEZ,OACEA,QAAQ6e,GAAGzZ,YACXA,UAAU4W,KAAKhc,SAASJ,OAAS,4DAIpBuE,GACfpE,KAAKsf,SAAWtf,KAAKmgB,iBAAiB/b,yDAItC,IAAIqZ,GAAKzd,KAAKN,YAEdM,KAAKof,cAAgBpf,KAAKyf,SAAWzf,KAAK4F,MAAQ5F,KAAK2F,MAEvD8X,GAAGzF,OAAOI,SAAS,kCAEnBpY,KAAKof,cAAc9Z,QACjBlF,iBAAE+G,OACA,QAEAiZ,kBAAU9a,QACVmY,GAAG9d,QAAQ2F,SACV4T,QAAS,SAAUmH,QAAS5C,GAAGzF,OAAQoE,KAAM,QAIlDpc,KAAKqf,YAAa,EAAAxI,SAAApU,SAAEzC,KAAKof,cAAc9Z,QAAQ,iBAAiByR,KAAK,cAAcuJ,KACnFtgB,KAAKqf,WAAWjH,SAAS,0BAEzBpY,KAAKof,cAAcjf,GAAG,mBAAoBC,iBAAEC,MAAML,KAAKugB,SAAUvgB,OACjEA,KAAKof,cAAcjf,GAAG,oBAAqBC,iBAAEC,MAAML,KAAKwgB,SAAUxgB,qDASzDoE,GACT,GAAIpE,KAAKof,eAAiBpf,KAAKygB,YAAa,CAC1CzgB,KAAKof,cAAc9Z,QAAQ,iDAWxBlB,GACL,GAAIpE,KAAKygB,YAAa,CACpBzgB,KAAKga,KAAK5V,OACL,CACLpE,KAAK+Z,KAAK3V,sCAUTA,GACH,GAAIpE,KAAKygB,aAAezgB,KAAKwf,SAAWxf,KAAKuf,QAAS,CACpD,OAGFvf,KAAKwf,QAAU,KACfxf,KAAKuf,QAAU,MACfvf,KAAKsf,SAAW,MAEhB,IAAI7B,GAAKzd,KAAKN,YAEd+d,GAAGvF,UAAUC,MAAQ,OACrBsF,GAAGvF,UAAU9T,EAAIA,EAGjB,GACGA,KAAOpE,KAAKub,UAAYvb,KAAK2F,MAAM0S,KAAK,UAAY,UACpDjU,GAAKA,EAAE+a,eACR,CACA/a,EAAEsc,kBACFtc,EAAE+a,iBAIJ,GAAInf,KAAK2gB,UAAW,EAClB,EAAA9J,SAAApU,SAAEzC,KAAKtD,MAAMyD,GAAG,qBAAsBC,iBAAEC,MAAML,KAAK8f,WAAY9f,OAIjEyd,GAAGzF,OAAOI,SAAS,uBAAuByB,YAAY,sBAEtD,GAAI7Z,KAAKof,cAAe,CACtBpf,KAAKof,cAAc9Z,QAAQ,YACtB,CACLtF,KAAKugB,wDAKPvgB,KAAKuf,QAAU,MACfvf,KAAKwf,QAAU,MAEf,GAAIxf,KAAK2gB,UAAW,EAElB,EAAA9J,SAAApU,SAAEzC,KAAKtD,KAAKqjB,UAAU5f,GAAG,+CAAgDC,iBAAEC,MAAML,KAAKga,KAAMha,QAC5F,EAAA6W,SAAApU,SAAEzC,KAAKtD,KAAKqjB,UAAU5f,GAAG,+CAAgDC,iBAAEC,MAAML,KAAKggB,iBAAkBhgB,OAQ1GA,KAAKN,YAAYwZ,QAAQ,qDAUtB9U,GACH,GAAIpE,KAAK4gB,YAAc5gB,KAAKwf,SAAWxf,KAAKuf,QAAS,CACnD,OAGF,IAAI9B,GAAKzd,KAAKN,YAAa4f,SAAYtf,KAAKsf,UAAYtf,KAAKmgB,iBAAiB/b,GAE9EpE,KAAKuf,QAAU,KACfvf,KAAKwf,QAAU,MACfxf,KAAKsf,SAAW,MAEhB7B,GAAGvF,UAAUC,MAAQ,OACrBsF,GAAGvF,UAAU9T,EAAIA,EAKjB,GAAIkb,SAAU,CACZtf,KAAKuf,QAAU,MACf,OAGF,GAAIvf,KAAKof,cAAe,CACtBpf,KAAKof,cAAc9Z,QAAQ,YACtB,CACLtF,KAAKwgB,wDAKPxgB,KAAKuf,QAAU,MACfvf,KAAKwf,QAAU,MAEf,IAAI/B,GAAKzd,KAAKN,YAGd+d,GAAGzF,OAAOI,SAAS,sBAAsByB,YAAY,wBAGrD,EAAAhD,SAAApU,SAAEzC,KAAKtD,MAAMwE,IAAI,qBAAsBd,iBAAEC,MAAML,KAAK8f,WAAY9f,QAChE,EAAA6W,SAAApU,SAAEzC,KAAKtD,KAAKqjB,UAAU7e,IAAI,+CAAgDd,iBAAEC,MAAML,KAAKga,KAAMha,QAC7F,EAAA6W,SAAApU,SAAEzC,KAAKtD,KAAKqjB,UAAU7e,IAAI,+CAAgDd,iBAAEC,MAAML,KAAKggB,iBAAkBhgB,OAOzGyd,GAAGvE,QAAQ,yDAIX,GAAIlZ,KAAKyf,SAAU,CACjB,OAAOzf,KAAK4F,MAAMiY,QAEpB,GAAI7d,KAAKub,SAAU,CACjB,OAAOvb,KAAK2F,MAAMkY,QAEpB,OAAO,oDAUP,OAAO7d,KAAKN,YAAYsY,OAAO6I,SAAS,yBACrC7gB,KAAKN,YAAYsY,OAAO6I,SAAS,kEAUpC,OAAO7gB,KAAKN,YAAYsY,OAAO6I,SAAS,wBACrC7gB,KAAKN,YAAYsY,OAAO6I,SAAS,yDAxWpC,OAAO7gB,KAAKN,YAAY6Y,aAAa5S,2CAQrC,OAAO3F,KAAKN,YAAY6Y,aAAagD,6CAQrC,OAAOvb,KAAKN,YAAYqZ,aAAanT,2CAQrC,OAAO5F,KAAKN,YAAYqZ,aAAa0G,iDAQrC,OAAQzf,KAAKN,YAAYC,QAAQyF,UAAYpF,KAAKqf,sDA4UvCxG,2pBC9Zf,IAAArZ,QAAApC,oBAAA,gDACA,IAAAua,WAAAva,oBAAA,4RAMMob,wBAIJ,SAAAA,aAAY9Y,aAAaK,gBAAAC,KAAAwY,cAIvBxY,KAAKN,YAAcA,YAInBM,KAAK2F,MAAQ3F,KAAKN,YAAYO,QAAQ6e,GAAG,SAAW9e,KAAKN,YAAYO,QAAWD,KAAKN,YAAYC,QAAQgG,MACvG3F,KAAKN,YAAYO,QAAQgc,KAAKjc,KAAKN,YAAYC,QAAQgG,OAAS,MAElE,GAAI3F,KAAK2F,OAAU3F,KAAK2F,MAAM9F,SAAW,EAAI,CAC3CG,KAAK2F,MAAQ,MAGf3F,KAAK8gB,0EAIL,IAAK9gB,KAAKub,WAAY,CACpB,OAEFvb,KAAK2F,MAAMxF,IACT4gB,oBAAqB3gB,iBAAEC,MAAML,KAAKghB,QAAShhB,QAE7CA,KAAK2F,MAAMxF,IACT8gB,qBAAsB7gB,iBAAEC,MAAML,KAAKkhB,SAAUlhB,gDAK/C,IAAKA,KAAKub,WAAY,CACpB,OAEFvb,KAAK2F,MAAMzE,IAAI,gEAIf,IAAKlB,KAAKub,WAAY,CACpB,OAGF,IAAI3H,IAAM,IAIR5T,KAAK2F,MAAMiO,MACX5T,KAAK2F,MAAMoR,KAAK,SAChB/W,KAAK2F,MAAM0S,KAAK,eAChB3C,IAAI,SAACyL,MACL,GAAIA,MAASvN,MAAQ,GAAK,CACxBA,IAAMuN,QAIV,GAAIvN,eAAenS,oBAAW,CAC5BmS,IAAM5T,KAAKohB,kBAAkBxN,IAAI1Q,OAAOlD,KAAKN,YAAYwC,cACpD,YAAa0R,MAAQ,UAAYA,eAAevP,QAAS,CAC9DuP,IAAM,GAGR5T,KAAK2F,MAAM0b,KAAK,QAASzN,iDAUzB,IAAK5T,KAAKub,WAAY,CACpB,OAAO,MAGT,OAAOvb,KAAK2F,MAAMiO,gDAWXA,KACP,IAAK5T,KAAKub,WAAY,CACpB,OAGF,IAAI+F,SAAWthB,KAAK2F,MAAM0b,KAAK,SAE/BzN,IAAMA,IAAMA,IAAM,GAElB,GAAIA,OAAS0N,SAAWA,SAAW,IAAK,CAEtC,OAGFthB,KAAK2F,MAAM0b,KAAK,QAASzN,KAOzB5T,KAAK2F,MAAMuT,SACTuB,KAAM,SACN/a,YAAaM,KAAKN,YAClBqB,MAAOf,KAAKN,YAAYqB,MACxBxC,MAAOqV,oEAYmB,IAAZA,IAAYhU,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,GAAN,KACtBgU,IAAMA,IAAMA,IAAM5T,KAAKN,YAAYoY,aAAayJ,iBAEhD,IAAK3N,IAAK,CACR,MAAO,GAGTA,IAAM5T,KAAKN,YAAYoY,aAAa0J,qBAAqB5N,IAAK,OAE9D,GAAI5T,KAAKN,YAAYC,QAAQmG,gBAAkB,MAAO,CACpD8N,IAAMA,IAAIxR,QAAQ,MAAO,IAG3B,OAAOwR,gDAQP,OAAQ5T,KAAK2F,QAAU,oDAQvB,OAAO3F,KAAKub,aAAevb,KAAKsZ,6DAQhC,OAAOtZ,KAAKub,YAAevb,KAAK2F,MAAM0b,KAAK,cAAgB,+CAU3D,GAAIrhB,KAAKub,WAAY,CACnBvb,KAAK2F,MAAM0b,KAAK,WAAY,+CAW9B,GAAIrhB,KAAKub,WAAY,CACnBvb,KAAK2F,MAAM0b,KAAK,WAAY,gDAU9B,IAAKrhB,KAAKub,WAAY,CACpB,OAGF,GACGvb,KAAKN,YAAYC,QAAQkG,oBAAsB,OAChD7F,KAAKN,YAAYoY,aAAa2J,iBAC9B,CAEA,OAGFzhB,KAAKid,SAASjd,KAAKohB,+DAUZhd,GACPpE,KAAKN,YAAYwY,UAAUC,MAAQ,eACnCnY,KAAKN,YAAYwY,UAAU9T,EAAIA,EAE/B,IAAIwP,IAAM5T,KAAKyH,WAEf,GAAImM,MAAQxP,EAAE7F,MAAO,CACnByB,KAAKN,YAAYud,SAASrJ,8CAWtBxP,GACNpE,KAAKN,YAAYwY,UAAUC,MAAQ,cACnCnY,KAAKN,YAAYwY,UAAU9T,EAAIA,EAE/B,IAAIwP,IAAM5T,KAAKyH,WAEf,GAAImM,MAAQxP,EAAE7F,MAAO,CACnByB,KAAKN,YAAYud,SAASrJ,iDAKjB4E,uGChQf,IAAAhD,YAAkBpY,oBAAQ,IAC1B,IAAA8T,QAAc9T,oBAAQ,IAEtB,IAAAskB,UAAAnL,MAEA,IAAAoL,eAEA,UAGA,OAGA,OAGA,IAAAC,mBACA5jB,OAAAqJ,KAAA6J,SAAApN,QAAA,SAAAlB,OACAgf,gBAAAF,OAAAjkB,KAAAyT,QAAAtO,OAAAyO,QAAAwQ,OAAAjM,KAAA,KAAAhT,QAGA,IAAAkf,YAEA,SAAAC,MAAAC,IAAApf,OACA,KAAA5C,gBAAA+hB,OAAA,CACA,WAAAA,MAAAC,IAAApf,OAGA,GAAAA,gBAAA+e,cAAA,CACA/e,MAAA,KAGA,GAAAA,kBAAAsO,SAAA,CACA,UAAAhR,MAAA,kBAAA0C,OAGA,IAAAtF,EACA,IAAA8T,SAEA,GAAA4Q,KAAA,MACAhiB,KAAA4C,MAAA,MACA5C,KAAAe,OAAA,OACAf,KAAAiiB,OAAA,OACE,GAAAD,eAAAD,MAAA,CACF/hB,KAAA4C,MAAAof,IAAApf,MACA5C,KAAAe,MAAAihB,IAAAjhB,MAAAwV,QACAvW,KAAAiiB,OAAAD,IAAAC,YACE,UAAAD,MAAA,UACF,IAAAjgB,OAAAyT,YAAArX,IAAA6jB,KACA,GAAAjgB,SAAA,MACA,UAAA7B,MAAA,sCAAA8hB,KAGAhiB,KAAA4C,MAAAb,OAAAa,MACAwO,SAAAF,QAAAlR,KAAA4C,OAAAwO,SACApR,KAAAe,MAAAgB,OAAAxD,MAAAgY,MAAA,EAAAnF,UACApR,KAAAiiB,cAAAlgB,OAAAxD,MAAA6S,YAAA,SAAArP,OAAAxD,MAAA6S,UAAA,OACE,GAAA4Q,IAAAniB,OAAA,CACFG,KAAA4C,aAAA,MACAwO,SAAAF,QAAAlR,KAAA4C,OAAAwO,SACA,IAAA8Q,OAAAR,OAAAjkB,KAAAukB,IAAA,EAAA5Q,UACApR,KAAAe,MAAAohB,UAAAD,OAAA9Q,UACApR,KAAAiiB,cAAAD,IAAA5Q,YAAA,SAAA4Q,IAAA5Q,UAAA,OACE,UAAA4Q,MAAA,UAEFA,KAAA,SACAhiB,KAAA4C,MAAA,MACA5C,KAAAe,OACAihB,KAAA,OACAA,KAAA,MACAA,IAAA,KAEAhiB,KAAAiiB,OAAA,MACE,CACFjiB,KAAAiiB,OAAA,EAEA,IAAA5a,KAAArJ,OAAAqJ,KAAA2a,KACA,aAAAA,IAAA,CACA3a,KAAA+a,OAAA/a,KAAAE,QAAA,YACAvH,KAAAiiB,cAAAD,IAAAjf,QAAA,SAAAif,IAAAjf,MAAA,EAGA,IAAAsf,WAAAhb,KAAAwa,OAAAjM,KAAA,IACA,KAAAyM,cAAAT,iBAAA,CACA,UAAA1hB,MAAA,sCAAAoiB,KAAAC,UAAAP,MAGAhiB,KAAA4C,MAAAgf,gBAAAS,YAEA,IAAAhR,OAAAH,QAAAlR,KAAA4C,OAAAyO,OACA,IAAAtQ,SACA,IAAAzD,EAAA,EAAaA,EAAA+T,OAAAxR,OAAmBvC,IAAA,CAChCyD,MAAAkD,KAAA+d,IAAA3Q,OAAA/T,KAGA0C,KAAAe,MAAAohB,UAAAphB,OAIA,GAAA+gB,SAAA9hB,KAAA4C,OAAA,CACAwO,SAAAF,QAAAlR,KAAA4C,OAAAwO,SACA,IAAA9T,EAAA,EAAaA,EAAA8T,SAAc9T,IAAA,CAC3B,IAAAklB,MAAAV,SAAA9hB,KAAA4C,OAAAtF,GACA,GAAAklB,MAAA,CACAxiB,KAAAe,MAAAzD,GAAAklB,MAAAxiB,KAAAe,MAAAzD,MAKA0C,KAAAiiB,OAAA/d,KAAAoO,IAAA,EAAApO,KAAAmO,IAAA,EAAArS,KAAAiiB,SAEA,GAAAjkB,OAAAykB,OAAA,CACAzkB,OAAAykB,OAAAziB,OAIA+hB,MAAA3iB,WACAkW,SAAA,WACA,OAAAtV,KAAAkD,UAGAwf,OAAA,WACA,OAAA1iB,UAAA4C,UAGAM,OAAA,SAAAyf,QACA,IAAA9K,KAAA7X,KAAA4C,SAAA4S,YAAAoN,GAAA5iB,UAAAmR,MACA0G,UAAA1U,aAAAwf,SAAA,SAAAA,OAAA,GACA,IAAA/gB,KAAAiW,KAAAoK,SAAA,EAAApK,KAAA9W,MAAA8W,KAAA9W,MAAA6a,OAAA5b,KAAAiiB,QACA,OAAAzM,YAAAoN,GAAA/K,KAAAjV,OAAAhB,OAGAihB,cAAA,SAAAF,QACA,IAAA9K,KAAA7X,KAAAmR,MAAAhO,aAAAwf,SAAA,SAAAA,OAAA,GACA,IAAA/gB,KAAAiW,KAAAoK,SAAA,EAAApK,KAAA9W,MAAA8W,KAAA9W,MAAA6a,OAAA5b,KAAAiiB,QACA,OAAAzM,YAAAoN,GAAAzR,IAAA2R,QAAAlhB,OAGAmhB,MAAA,WACA,OAAA/iB,KAAAiiB,SAAA,EAAAjiB,KAAAe,MAAAwV,QAAAvW,KAAAe,MAAA6a,OAAA5b,KAAAiiB,SAGA/iB,OAAA,WACA,IAAA6C,UACA,IAAAqP,SAAAF,QAAAlR,KAAA4C,OAAAwO,SACA,IAAAC,OAAAH,QAAAlR,KAAA4C,OAAAyO,OAEA,QAAA/T,EAAA,EAAiBA,EAAA8T,SAAc9T,IAAA,CAC/ByE,OAAAsP,OAAA/T,IAAA0C,KAAAe,MAAAzD,GAGA,GAAA0C,KAAAiiB,SAAA,GACAlgB,OAAAgB,MAAA/C,KAAAiiB,OAGA,OAAAlgB,QAGAihB,UAAA,WACA,IAAA7R,IAAAnR,KAAAmR,MAAApQ,MACAoQ,IAAA,QACAA,IAAA,QACAA,IAAA,QAEA,GAAAnR,KAAAiiB,SAAA,GACA9Q,IAAAlN,KAAAjE,KAAAiiB,QAGA,OAAA9Q,KAGA8R,WAAA,WACA,IAAA9R,IAAAnR,KAAAmR,MAAAjS,SACAiS,IAAA/S,GAAA,IACA+S,IAAAgB,GAAA,IACAhB,IAAAiB,GAAA,IAEA,GAAApS,KAAAiiB,SAAA,GACA9Q,IAAApO,MAAA/C,KAAAiiB,OAGA,OAAA9Q,KAGAhO,MAAA,SAAAwf,QACAA,OAAAze,KAAAoO,IAAAqQ,QAAA,KACA,WAAAZ,MAAA/hB,KAAAe,MAAA2U,IAAAwN,aAAAP,SAAA/G,OAAA5b,KAAAiiB,QAAAjiB,KAAA4C,QAGAG,MAAA,SAAA6Q,KACA,GAAAhU,UAAAC,OAAA,CACA,WAAAkiB,MAAA/hB,KAAAe,MAAA6a,OAAA1X,KAAAoO,IAAA,EAAApO,KAAAmO,IAAA,EAAAuB,OAAA5T,KAAA4C,OAGA,OAAA5C,KAAAiiB,QAIA7S,IAAA+T,OAAA,QAAAC,MAAA,MACAlY,MAAAiY,OAAA,QAAAC,MAAA,MACA/a,KAAA8a,OAAA,QAAAC,MAAA,MAEAvgB,IAAAsgB,QAAA,0CAAAvP,KAAqE,OAAAA,IAAA,eAErEyP,YAAAF,OAAA,QAAAC,MAAA,MACAE,UAAAH,OAAA,QAAAC,MAAA,MAEApf,YAAAmf,OAAA,QAAAC,MAAA,MACA7kB,MAAA4kB,OAAA,QAAAC,MAAA,MAEAtN,OAAAqN,OAAA,QAAAC,MAAA,MACAnY,KAAAkY,OAAA,QAAAC,MAAA,MAEAxS,MAAAuS,OAAA,QAAAC,MAAA,MACAG,OAAAJ,OAAA,QAAAC,MAAA,MAEApa,KAAAma,OAAA,SAAAC,MAAA,MACAnW,QAAAkW,OAAA,SAAAC,MAAA,MACAtS,OAAAqS,OAAA,SAAAC,MAAA,MACAjb,MAAAgb,OAAA,SAAAC,MAAA,MAEAnQ,EAAAkQ,OAAA,QAAAC,MAAA,MACAtQ,EAAAqQ,OAAA,QAAAC,MAAA,MACA5P,EAAA2P,OAAA,QAAAC,MAAA,MAEA7lB,EAAA4lB,OAAA,QAAAC,MAAA,MACA7hB,EAAA4hB,OAAA,SACA/Q,EAAA+Q,OAAA,SAEArR,QAAA,SAAA8B,KACA,GAAAhU,UAAAC,OAAA,CACA,WAAAkiB,MAAAnO,KAGA,OAAA1C,QAAAlR,KAAA4C,OAAAkP,QAAA9R,KAAAe,QAGA8Q,IAAA,SAAA+B,KACA,GAAAhU,UAAAC,OAAA,CACA,WAAAkiB,MAAAnO,KAGA,OAAA4B,YAAAoN,GAAA/Q,IAAA7R,KAAAmR,MAAAhO,QAAApC,QAGAyiB,UAAA,WACA,IAAArS,IAAAnR,KAAAmR,MAAApQ,MACA,OAAAoQ,IAAA,aAAAA,IAAA,WAAAA,IAAA,QAGAsS,WAAA,WAEA,IAAAtS,IAAAnR,KAAAmR,MAAApQ,MAEA,IAAA2iB,OACA,QAAApmB,EAAA,EAAiBA,EAAA6T,IAAAtR,OAAgBvC,IAAA,CACjC,IAAAqmB,KAAAxS,IAAA7T,GAAA,IACAomB,IAAApmB,GAAAqmB,MAAA,OAAAA,KAAA,MAAAzf,KAAAgP,KAAAyQ,KAAA,iBAGA,YAAAD,IAAA,SAAAA,IAAA,SAAAA,IAAA,IAGAE,SAAA,SAAAC,QAEA,IAAAC,KAAA9jB,KAAAyjB,aACA,IAAAM,KAAAF,OAAAJ,aAEA,GAAAK,KAAAC,KAAA,CACA,OAAAD,KAAA,MAAAC,KAAA,KAGA,OAAAA,KAAA,MAAAD,KAAA,MAGAE,MAAA,SAAAH,QACA,IAAAI,cAAAjkB,KAAA4jB,SAAAC,QACA,GAAAI,eAAA,KACA,YAGA,OAAAA,eAAA,aAGA3gB,OAAA,WAEA,IAAA6N,IAAAnR,KAAAmR,MAAApQ,MACA,IAAAmjB,KAAA/S,IAAA,OAAAA,IAAA,OAAAA,IAAA,YACA,OAAA+S,IAAA,KAGA3gB,QAAA,WACA,OAAAvD,KAAAsD,UAGA6gB,OAAA,WACA,IAAAhT,IAAAnR,KAAAmR,MACA,QAAA7T,EAAA,EAAiBA,EAAA,EAAOA,IAAA,CACxB6T,IAAApQ,MAAAzD,GAAA,IAAA6T,IAAApQ,MAAAzD,GAEA,OAAA6T,KAGAiT,QAAA,SAAA5P,OACA,IAAAlD,IAAAtR,KAAAsR,MACAA,IAAAvQ,MAAA,IAAAuQ,IAAAvQ,MAAA,GAAAyT,MACA,OAAAlD,KAGA+S,OAAA,SAAA7P,OACA,IAAAlD,IAAAtR,KAAAsR,MACAA,IAAAvQ,MAAA,IAAAuQ,IAAAvQ,MAAA,GAAAyT,MACA,OAAAlD,KAGAgT,SAAA,SAAA9P,OACA,IAAAlD,IAAAtR,KAAAsR,MACAA,IAAAvQ,MAAA,IAAAuQ,IAAAvQ,MAAA,GAAAyT,MACA,OAAAlD,KAGAiT,WAAA,SAAA/P,OACA,IAAAlD,IAAAtR,KAAAsR,MACAA,IAAAvQ,MAAA,IAAAuQ,IAAAvQ,MAAA,GAAAyT,MACA,OAAAlD,KAGAkT,OAAA,SAAAhQ,OACA,IAAAhD,IAAAxR,KAAAwR,MACAA,IAAAzQ,MAAA,IAAAyQ,IAAAzQ,MAAA,GAAAyT,MACA,OAAAhD,KAGAiT,QAAA,SAAAjQ,OACA,IAAAhD,IAAAxR,KAAAwR,MACAA,IAAAzQ,MAAA,IAAAyQ,IAAAzQ,MAAA,GAAAyT,MACA,OAAAhD,KAGAuE,UAAA,WAEA,IAAA5E,IAAAnR,KAAAmR,MAAApQ,MACA,IAAA6S,IAAAzC,IAAA,MAAAA,IAAA,OAAAA,IAAA,OACA,OAAA4Q,MAAA5Q,IAAAyC,cAGA8Q,KAAA,SAAAlQ,OACA,OAAAxU,KAAA+C,MAAA/C,KAAAiiB,OAAAjiB,KAAAiiB,OAAAzN,QAGAmQ,QAAA,SAAAnQ,OACA,OAAAxU,KAAA+C,MAAA/C,KAAAiiB,OAAAjiB,KAAAiiB,OAAAzN,QAGAoQ,OAAA,SAAAC,SACA,IAAAvT,IAAAtR,KAAAsR,MACA,IAAAzO,IAAAyO,IAAAvQ,MAAA,GACA8B,SAAAgiB,SAAA,IACAhiB,QAAA,MAAAA,QACAyO,IAAAvQ,MAAA,GAAA8B,IACA,OAAAyO,KAGAwT,IAAA,SAAAC,WAAAC,QAGA,IAAAD,wBAAA5T,IAAA,CACA,UAAAjR,MAAA,gFAAA6kB,YAEA,IAAAE,OAAAF,WAAA5T,MACA,IAAA0S,OAAA7jB,KAAAmR,MACA,IAAA7R,EAAA0lB,SAAAllB,UAAA,GAAAklB,OAEA,IAAAnS,EAAA,EAAAvT,EAAA,EACA,IAAAiC,EAAA0jB,OAAAliB,QAAA8gB,OAAA9gB,QAEA,IAAAmiB,KAAArS,EAAAtR,KAAA,EAAAsR,KAAAtR,IAAA,EAAAsR,EAAAtR,IAAA,KACA,IAAA4jB,GAAA,EAAAD,GAEA,OAAAnD,MAAA5Q,IACA+T,GAAAD,OAAA7V,MAAA+V,GAAAtB,OAAAzU,MACA8V,GAAAD,OAAA/Z,QAAAia,GAAAtB,OAAA3Y,QACAga,GAAAD,OAAA5c,OAAA8c,GAAAtB,OAAAxb,OACA4c,OAAAliB,QAAAzD,EAAAukB,OAAA9gB,SAAA,EAAAzD,MAKAtB,OAAAqJ,KAAA6J,SAAApN,QAAA,SAAAlB,OACA,GAAA+e,cAAApa,QAAA3E,UAAA,GACA,OAGA,IAAAwO,SAAAF,QAAAtO,OAAAwO,SAGA2Q,MAAA3iB,UAAAwD,OAAA,WACA,GAAA5C,KAAA4C,cAAA,CACA,WAAAmf,MAAA/hB,MAGA,GAAAJ,UAAAC,OAAA,CACA,WAAAkiB,MAAAniB,UAAAgD,OAGA,IAAAwiB,gBAAAxlB,UAAAwR,YAAA,SAAAA,SAAApR,KAAAiiB,OACA,WAAAF,MAAAsD,YAAAnU,QAAAlR,KAAA4C,cAAA0iB,IAAAtlB,KAAAe,QAAA6a,OAAAwJ,UAAAxiB,QAIAmf,MAAAnf,OAAA,SAAA7B,OACA,UAAAA,QAAA,UACAA,MAAAohB,UAAAT,OAAAjkB,KAAAmC,WAAAwR,UAEA,WAAA2Q,MAAAhhB,MAAA6B,UAIA,SAAA2iB,QAAAC,IAAA7C,QACA,OAAA8C,OAAAD,IAAAE,QAAA/C,SAGA,SAAAO,aAAAP,QACA,gBAAA6C,KACA,OAAAD,QAAAC,IAAA7C,SAIA,SAAAQ,OAAAvgB,MAAA+iB,QAAAC,UACAhjB,MAAAf,MAAA6B,QAAAd,qBAEAA,MAAAkB,QAAA,SAAApG,IACAokB,SAAApkB,KAAAokB,SAAApkB,QAAAioB,SAAAC,WAGAhjB,YAAA,GAEA,gBAAAgR,KACA,IAAA7R,OAEA,GAAAnC,UAAAC,OAAA,CACA,GAAA+lB,SAAA,CACAhS,IAAAgS,SAAAhS,KAGA7R,OAAA/B,KAAA4C,SACAb,OAAAhB,MAAA4kB,SAAA/R,IACA,OAAA7R,OAGAA,OAAA/B,KAAA4C,SAAA7B,MAAA4kB,SACA,GAAAC,SAAA,CACA7jB,OAAA6jB,SAAA7jB,QAGA,OAAAA,QAIA,SAAAqhB,MAAA9Q,KACA,gBAAAhR,GACA,OAAA4C,KAAAoO,IAAA,EAAApO,KAAAmO,IAAAC,IAAAhR,KAIA,SAAA+jB,YAAAzR,KACA,OAAA/R,MAAA6B,QAAAkQ,eAGA,SAAAuO,UAAA0D,IAAAhmB,QACA,QAAAvC,EAAA,EAAgBA,EAAAuC,OAAYvC,IAAA,CAC5B,UAAAuoB,IAAAvoB,KAAA,UACAuoB,IAAAvoB,GAAA,GAIA,OAAAuoB,IAGAhpB,OAAAD,QAAAmlB,oDCheA,IAAA+D,WAAiB1oB,oBAAQ,GACzB,IAAA2oB,QAAc3oB,oBAAQ,IAEtB,IAAA4oB,gBAGA,QAAAnoB,QAAAioB,WAAA,CACA,GAAAA,WAAAzmB,eAAAxB,MAAA,CACAmoB,aAAAF,WAAAjoB,aAIA,IAAAooB,GAAAppB,OAAAD,SACAgmB,MACAzkB,QAGA8nB,GAAA9nB,IAAA,SAAA+E,QACA,IAAAgjB,OAAAhjB,OAAAqS,UAAA,KAAAhR,cACA,IAAAqP,IACA,IAAAhR,MACA,OAAAsjB,QACA,UACAtS,IAAAqS,GAAA9nB,IAAAmT,IAAApO,QACAN,MAAA,MACA,MACA,UACAgR,IAAAqS,GAAA9nB,IAAAqT,IAAAtO,QACAN,MAAA,MACA,MACA,QACAgR,IAAAqS,GAAA9nB,IAAAgT,IAAAjO,QACAN,MAAA,MACA,MAGA,IAAAgR,IAAA,CACA,YAGA,OAAShR,MAAArE,MAAAqV,MAGTqS,GAAA9nB,IAAAgT,IAAA,SAAAjO,QACA,IAAAA,OAAA,CACA,YAGA,IAAAijB,KAAA,sBACA,IAAAtU,IAAA,kCACA,IAAAuU,KAAA,0FACA,IAAAC,IAAA,4GACA,IAAAvU,QAAA,QAEA,IAAAX,KAAA,SACA,IAAA7M,MACA,IAAAhH,EACA,IAAAgpB,SAEA,GAAAhiB,MAAApB,OAAAoB,MAAAuN,KAAA,CACAyU,SAAAhiB,MAAA,GACAA,YAAA,GAEA,IAAAhH,EAAA,EAAaA,EAAA,EAAOA,IAAA,CAEpB,IAAAipB,GAAAjpB,EAAA,EACA6T,IAAA7T,GAAAuY,SAAAvR,MAAAiS,MAAAgQ,MAAA,OAGA,GAAAD,SAAA,CACAnV,IAAA,GAAAjN,KAAAf,MAAA0S,SAAAyQ,SAAA,uBAEE,GAAAhiB,MAAApB,OAAAoB,MAAA6hB,MAAA,CACF7hB,YAAA,GACAgiB,SAAAhiB,MAAA,GAEA,IAAAhH,EAAA,EAAaA,EAAA,EAAOA,IAAA,CACpB6T,IAAA7T,GAAAuY,SAAAvR,MAAAhH,GAAAgH,MAAAhH,GAAA,IAGA,GAAAgpB,SAAA,CACAnV,IAAA,GAAAjN,KAAAf,MAAA0S,SAAAyQ,kBAAA,uBAEE,GAAAhiB,MAAApB,OAAAoB,MAAA8hB,MAAA,CACF,IAAA9oB,EAAA,EAAaA,EAAA,EAAOA,IAAA,CACpB6T,IAAA7T,GAAAuY,SAAAvR,MAAAhH,EAAA,MAGA,GAAAgH,MAAA,IACA6M,IAAA,GAAAqV,WAAAliB,MAAA,UAEE,GAAAA,MAAApB,OAAAoB,MAAA+hB,KAAA,CACF,IAAA/oB,EAAA,EAAaA,EAAA,EAAOA,IAAA,CACpB6T,IAAA7T,GAAA4G,KAAAf,MAAAqjB,WAAAliB,MAAAhH,EAAA,UAGA,GAAAgH,MAAA,IACA6M,IAAA,GAAAqV,WAAAliB,MAAA,UAEE,GAAAA,MAAApB,OAAAoB,MAAAwN,SAAA,CACF,GAAAxN,MAAA,oBACA,gBAGA6M,IAAA2U,WAAAxhB,MAAA,IAEA,IAAA6M,IAAA,CACA,YAGAA,IAAA,KAEA,OAAAA,QACE,CACF,YAGA,IAAA7T,EAAA,EAAYA,EAAA,EAAOA,IAAA,CACnB6T,IAAA7T,GAAAmpB,MAAAtV,IAAA7T,GAAA,OAEA6T,IAAA,GAAAsV,MAAAtV,IAAA,QAEA,OAAAA,KAGA8U,GAAA9nB,IAAAmT,IAAA,SAAApO,QACA,IAAAA,OAAA,CACA,YAGA,IAAAoO,IAAA,sHACA,IAAAhN,MAAApB,OAAAoB,MAAAgN,KAEA,GAAAhN,MAAA,CACA,IAAAvB,MAAAyjB,WAAAliB,MAAA,IACA,IAAAjD,GAAAmlB,WAAAliB,MAAA,aACA,IAAA/E,EAAAknB,MAAAD,WAAAliB,MAAA,WACA,IAAA/G,EAAAkpB,MAAAD,WAAAliB,MAAA,WACA,IAAA/C,EAAAklB,MAAAjlB,MAAAuB,OAAA,EAAAA,MAAA,KAEA,OAAA1B,EAAA9B,EAAAhC,EAAAgE,GAGA,aAGA0kB,GAAA9nB,IAAAqT,IAAA,SAAAtO,QACA,IAAAA,OAAA,CACA,YAGA,IAAAsO,IAAA,kHACA,IAAAlN,MAAApB,OAAAoB,MAAAkN,KAEA,GAAAlN,MAAA,CACA,IAAAvB,MAAAyjB,WAAAliB,MAAA,IACA,IAAAjD,GAAAmlB,WAAAliB,MAAA,iBACA,IAAAuO,EAAA4T,MAAAD,WAAAliB,MAAA,WACA,IAAA8N,EAAAqU,MAAAD,WAAAliB,MAAA,WACA,IAAA/C,EAAAklB,MAAAjlB,MAAAuB,OAAA,EAAAA,MAAA,KACA,OAAA1B,EAAAwR,EAAAT,EAAA7Q,GAGA,aAGA0kB,GAAArD,GAAA/Q,IAAA,WACA,IAAAuU,KAAAL,QAAAnmB,WAEA,MACA,IACA8mB,UAAAN,KAAA,IACAM,UAAAN,KAAA,IACAM,UAAAN,KAAA,KACAA,KAAA,KACAM,UAAAxiB,KAAAf,MAAAijB,KAAA,SACA,KAIAH,GAAArD,GAAAzR,IAAA,WACA,IAAAiV,KAAAL,QAAAnmB,WAEA,OAAAwmB,KAAAvmB,OAAA,GAAAumB,KAAA,OACA,OAAAliB,KAAAf,MAAAijB,KAAA,SAAAliB,KAAAf,MAAAijB,KAAA,SAAAliB,KAAAf,MAAAijB,KAAA,QACA,QAAAliB,KAAAf,MAAAijB,KAAA,SAAAliB,KAAAf,MAAAijB,KAAA,SAAAliB,KAAAf,MAAAijB,KAAA,SAAAA,KAAA,QAGAH,GAAArD,GAAAzR,IAAA2R,QAAA,WACA,IAAAsD,KAAAL,QAAAnmB,WAEA,IAAAxB,EAAA8F,KAAAf,MAAAijB,KAAA,YACA,IAAAjU,EAAAjO,KAAAf,MAAAijB,KAAA,YACA,IAAAhU,EAAAlO,KAAAf,MAAAijB,KAAA,YAEA,OAAAA,KAAAvmB,OAAA,GAAAumB,KAAA,OACA,OAAAhoB,EAAA,MAAA+T,EAAA,MAAAC,EAAA,KACA,QAAAhU,EAAA,MAAA+T,EAAA,MAAAC,EAAA,MAAAgU,KAAA,QAGAH,GAAArD,GAAAtR,IAAA,WACA,IAAAqV,KAAAZ,QAAAnmB,WACA,OAAA+mB,KAAA9mB,OAAA,GAAA8mB,KAAA,OACA,OAAAA,KAAA,QAAAA,KAAA,SAAAA,KAAA,QACA,QAAAA,KAAA,QAAAA,KAAA,SAAAA,KAAA,SAAAA,KAAA,QAKAV,GAAArD,GAAApR,IAAA,WACA,IAAAoV,KAAAb,QAAAnmB,WAEA,IAAA2B,EAAA,GACA,GAAAqlB,KAAA/mB,QAAA,GAAA+mB,KAAA,QACArlB,EAAA,KAAAqlB,KAAA,GAGA,aAAAA,KAAA,QAAAA,KAAA,SAAAA,KAAA,OAAArlB,EAAA,KAGA0kB,GAAArD,GAAA9Q,QAAA,SAAAX,KACA,OAAA6U,aAAA7U,IAAAoF,MAAA,OAIA,SAAAkQ,MAAAjB,IAAAnT,IAAAC,KACA,OAAApO,KAAAmO,IAAAnO,KAAAoO,IAAAD,IAAAmT,KAAAlT,KAGA,SAAAoU,UAAAlB,KACA,IAAApiB,IAAAoiB,IAAAlQ,SAAA,IAAA9N,cACA,OAAApE,IAAAvD,OAAA,MAAAuD,oECtOA,IAAAyjB,WAAiBzpB,oBAAQ,IAEzB,IAAAwe,OAAA/Z,MAAAzC,UAAAwc,OACA,IAAArF,MAAA1U,MAAAzC,UAAAmX,MAEA,IAAAwP,QAAAlpB,OAAAD,QAAA,SAAAmpB,QAAAnkB,MACA,IAAAklB,WAEA,QAAAxpB,EAAA,EAAAypB,IAAAnlB,KAAA/B,OAAmCvC,EAAAypB,IAASzpB,IAAA,CAC5C,IAAA0pB,IAAAplB,KAAAtE,GAEA,GAAAupB,WAAAG,KAAA,CAEAF,QAAAlL,OAAAne,KAAAqpB,QAAAvQ,MAAA9Y,KAAAupB,UACG,CACHF,QAAA7iB,KAAA+iB,MAIA,OAAAF,SAGAf,QAAAkB,KAAA,SAAAvlB,IACA,kBACA,OAAAA,GAAAqkB,QAAAnmB,yECxBA/C,OAAAD,QAAA,SAAAiqB,WAAA7E,KACA,IAAAA,IAAA,CACA,aAGA,OAAAA,eAAAngB,aAAA6B,QAAAse,MACAA,IAAAniB,QAAA,GAAAmiB,IAAAI,kBAAA8E,wDCRA,IAAAC,YAAkB/pB,oBAAQ,GAC1B,IAAAgqB,MAAYhqB,oBAAQ,IAEpB,IAAA8T,WAEA,IAAAmW,OAAArpB,OAAAqJ,KAAA8f,aAEA,SAAAG,QAAA5lB,IACA,IAAA6lB,UAAA,SAAA3lB,MACA,GAAAA,OAAA9B,WAAA8B,OAAA,MACA,OAAAA,KAGA,GAAAhC,UAAAC,OAAA,GACA+B,KAAAC,MAAAzC,UAAAmX,MAAA9Y,KAAAmC,WAGA,OAAA8B,GAAAE,OAIA,kBAAAF,GAAA,CACA6lB,UAAAC,WAAA9lB,GAAA8lB,WAGA,OAAAD,UAGA,SAAAE,YAAA/lB,IACA,IAAA6lB,UAAA,SAAA3lB,MACA,GAAAA,OAAA9B,WAAA8B,OAAA,MACA,OAAAA,KAGA,GAAAhC,UAAAC,OAAA,GACA+B,KAAAC,MAAAzC,UAAAmX,MAAA9Y,KAAAmC,WAGA,IAAAmC,OAAAL,GAAAE,MAKA,UAAAG,SAAA,UACA,QAAAglB,IAAAhlB,OAAAlC,OAAAvC,EAAA,EAAuCA,EAAAypB,IAASzpB,IAAA,CAChDyE,OAAAzE,GAAA4G,KAAAf,MAAApB,OAAAzE,KAIA,OAAAyE,QAIA,kBAAAL,GAAA,CACA6lB,UAAAC,WAAA9lB,GAAA8lB,WAGA,OAAAD,UAGAF,OAAAvjB,QAAA,SAAA4jB,WACAxW,QAAAwW,cAEA1pB,OAAAC,eAAAiT,QAAAwW,WAAA,YAAwDnpB,MAAA4oB,YAAAO,WAAAtW,WACxDpT,OAAAC,eAAAiT,QAAAwW,WAAA,UAAsDnpB,MAAA4oB,YAAAO,WAAArW,SAEtD,IAAAsW,OAAAP,MAAAM,WACA,IAAAE,YAAA5pB,OAAAqJ,KAAAsgB,QAEAC,YAAA9jB,QAAA,SAAA+jB,SACA,IAAAnmB,GAAAimB,OAAAE,SAEA3W,QAAAwW,WAAAG,SAAAJ,YAAA/lB,IACAwP,QAAAwW,WAAAG,SAAAvC,IAAAgC,QAAA5lB,QAIA7E,OAAAD,QAAAsU,sDC7EA,IAAAiW,YAAkB/pB,oBAAQ,GAa1B,SAAA0qB,aACA,IAAAC,SAEA,IAAAV,OAAArpB,OAAAqJ,KAAA8f,aAEA,QAAAJ,IAAAM,OAAAxnB,OAAAvC,EAAA,EAAqCA,EAAAypB,IAASzpB,IAAA,CAC9CyqB,MAAAV,OAAA/pB,KAGAiW,UAAA,EACAwL,OAAA,MAIA,OAAAgJ,MAIA,SAAAC,UAAAN,WACA,IAAAK,MAAAD,aACA,IAAAG,OAAAP,WAEAK,MAAAL,WAAAnU,SAAA,EAEA,MAAA0U,MAAApoB,OAAA,CACA,IAAAqoB,QAAAD,MAAAE,MACA,IAAAC,UAAApqB,OAAAqJ,KAAA8f,YAAAe,UAEA,QAAAnB,IAAAqB,UAAAvoB,OAAAvC,EAAA,EAAyCA,EAAAypB,IAASzpB,IAAA,CAClD,IAAA+qB,SAAAD,UAAA9qB,GACA,IAAAgrB,KAAAP,MAAAM,UAEA,GAAAC,KAAA/U,YAAA,GACA+U,KAAA/U,SAAAwU,MAAAG,SAAA3U,SAAA,EACA+U,KAAAvJ,OAAAmJ,QACAD,MAAAM,QAAAF,YAKA,OAAAN,MAGA,SAAAS,KAAAC,KAAA7F,IACA,gBAAAhhB,MACA,OAAAghB,GAAA6F,KAAA7mB,QAIA,SAAA8mB,eAAAb,QAAAE,OACA,IAAAY,MAAAZ,MAAAF,SAAA9I,OAAA8I,SACA,IAAAnmB,GAAAylB,YAAAY,MAAAF,SAAA9I,QAAA8I,SAEA,IAAAe,IAAAb,MAAAF,SAAA9I,OACA,MAAAgJ,MAAAa,KAAA7J,OAAA,CACA4J,KAAAJ,QAAAR,MAAAa,KAAA7J,QACArd,GAAA8mB,KAAArB,YAAAY,MAAAa,KAAA7J,QAAA6J,KAAAlnB,IACAknB,IAAAb,MAAAa,KAAA7J,OAGArd,GAAA8lB,WAAAmB,KACA,OAAAjnB,GAGA7E,OAAAD,QAAA,SAAA8qB,WACA,IAAAK,MAAAC,UAAAN,WACA,IAAAF,cAEA,IAAAH,OAAArpB,OAAAqJ,KAAA0gB,OACA,QAAAhB,IAAAM,OAAAxnB,OAAAvC,EAAA,EAAqCA,EAAAypB,IAASzpB,IAAA,CAC9C,IAAAuqB,QAAAR,OAAA/pB,GACA,IAAAgrB,KAAAP,MAAAF,SAEA,GAAAS,KAAAvJ,SAAA,MAEA,SAGAyI,WAAAK,SAAAa,eAAAb,QAAAE,OAGA,OAAAP,2nBC5FA,IAAAhoB,QAAApC,oBAAA,gDACA,IAAAua,WAAAva,oBAAA,4RAMMqb,wBAIJ,SAAAA,aAAY/Y,aAAaK,gBAAAC,KAAAyY,cAIvBzY,KAAKN,YAAcA,yEAwDnB,GAAIM,KAAKN,YAAYC,QAAQoB,MAAO,CAClCf,KAAKe,MAAQf,KAAKqa,YAAYra,KAAKN,YAAYC,QAAQoB,OACvD,OAIF,IAAKf,KAAKe,SAAWf,KAAKN,YAAY6Y,aAAa9Q,WAAY,CAC7DzH,KAAKe,MAAQf,KAAKqa,YAChBra,KAAKN,YAAY6Y,aAAa9Q,WAAYzH,KAAKN,YAAYC,QAAQkG,4DAMvE7F,KAAKN,YAAYO,QAAQ6Z,WAAW,iEAUpC,IAAK9Z,KAAKma,WAAY,CACpB,MAAO,GAGT,OAAOna,KAAKe,MAAMmC,OAAOlD,KAAKkC,8DAQjB0R,KACb,IAAI7S,MAAQ6S,IAAM5T,KAAKqa,YAAYzG,KAAO,KAE1C5T,KAAKe,MAAQA,MAAQA,MAAQ,qDAWnB6S,KAA+B,IAA1BiV,kBAA0BjpB,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,GAAN,KACnC,IAAImB,MAAQ,IAAIU,oBAAUzB,KAAKwhB,qBAAqB5N,KAAM5T,KAAKkC,QAE/D,IAAKnB,MAAMsC,UAAW,CACpB,GAAIwlB,kBAAmB,CACrB9nB,MAAQf,KAAK0d,mBAQf1d,KAAKN,YAAYwZ,QAAQ,qBAAsBnY,MAAO6S,KAGxD,IAAK5T,KAAK8oB,iBAAkB,CAE1B/nB,MAAMgC,MAAQ,EAGhB,OAAOhC,kEAIP,GAAIf,KAAK+oB,UAAa/oB,KAAK+oB,WAAa/oB,KAAKe,MAAQ,CACnD,OAAOf,KAAKe,MAGd,IAAIgoB,SAAW/oB,KAAKwhB,qBAAqBxhB,KAAK+oB,UAE9C,IAAIhoB,MAAQ,IAAIU,oBAAUsnB,SAAU/oB,KAAKkC,QAEzC,IAAKnB,MAAMsC,UAAW,CACpBsY,QAAQqN,KAAK,sFACb,OAAOhpB,KAAKe,MAAQf,KAAKe,MAAQ,IAAIU,oBAAU,UAAWzB,KAAKkC,QAGjE,OAAOnB,wDAOP,IAAKf,KAAKma,WAAY,CACpBna,KAAKe,MAAQf,KAAK0d,mBAGpB,OAAO1d,KAAKe,wEAUOA,OAAyB,IAAlBC,UAAkBpB,UAAAC,OAAA,GAAAD,UAAA,KAAAE,UAAAF,UAAA,GAAN,KACtC,IAAIqpB,iBAAmB,MAEvB7oB,iBAAEuW,KAAK3W,KAAKN,YAAYuG,WAAY,SAAUpI,KAAM2b,KAClD,GAAIyP,mBAAqB,MAAO,CAE9B,OAEFA,iBAAmBzP,IAAI0P,aAAanoB,MAAOC,aAG7C,OAAOioB,iBAAmBA,iBAAmBloB,8DAQ7C,OAAQf,KAAKma,aAAena,KAAKe,MAAMsC,kEAQvC,OAAQrD,KAAKN,YAAYC,QAAQoG,WAAa,kDAQ9C,OAAO/F,KAAKe,iBAAiBU,yDAjM7B,OAAOzB,KAAKN,YAAYC,QAAQuF,cAC9BlF,KAAKN,YAAYC,QAAQuF,cAAiBlF,KAAKma,WAAana,KAAKe,MAAQ,wCAO3E,GAAIf,KAAKN,YAAYC,QAAQuC,OAAQ,CACnC,OAAOlC,KAAKN,YAAYC,QAAQuC,OAGlC,GAAIlC,KAAKma,YAAcna,KAAKe,MAAMooB,mBAAqBnpB,KAAKe,MAAMmB,OAAOoC,MAAM,QAAS,CACtF,OAAOtE,KAAK8oB,iBAAmB,OAAS,MAG1C,GAAI9oB,KAAKma,WAAY,CACnB,OAAOna,KAAKe,MAAMmB,OAGpB,MAAO,wCASP,OAAOlC,KAAKN,YAAYO,QAAQ8W,KAAK,2BAS7BxY,OACRyB,KAAKN,YAAYO,QAAQ8W,KAAK,QAASxY,OAEvC,GAAKA,iBAAiBkD,qBAAezB,KAAKN,YAAYC,QAAQuC,SAAW,OAAS,CAEhFlC,KAAKN,YAAYC,QAAQuC,OAASlC,KAAKe,MAAMmB,mDA0JpCuW,2pBC3Nf,IAAAjZ,QAAApC,oBAAA,sRAMM0b,yBAIJ,SAAAA,cAAYpZ,aAAaK,gBAAAC,KAAA8Y,eAIvB9Y,KAAKN,YAAcA,YAInBM,KAAKgY,OAAS,mEAed,IAAIA,OAAShY,KAAKgY,QAAS,EAAAnB,SAAApU,SAAEzC,KAAKL,QAAQqG,UAE1C,GAAIhG,KAAKL,QAAQsF,YAAa,CAC5B+S,OAAOI,SAASpY,KAAKL,QAAQsF,aAG/B,GAAIjF,KAAKL,QAAQwF,WAAY,CAC3B6S,OAAOI,SAAS,0BAGlB,GAAIpY,KAAKopB,oBAAqB,CAC5BppB,KAAKL,QAAQoG,SAAW,KACxBiS,OAAOI,SAAS,8BACX,CACLpY,KAAKL,QAAQoG,SAAW,+CAM1B,IAAIsjB,aAAerpB,KAAKN,YAAY2F,UAAYrF,KAAKN,YAAY2F,UAAY,KAE7E,GAAIgkB,aAAc,CAChBrpB,KAAKgY,OAAOsR,SAASD,uDAKvBrpB,KAAKgY,OAAOuR,uEAIZ,OACGvpB,KAAKL,QAAQoG,UAAa/F,KAAKN,YAAYoY,aAAaqC,YAAcna,KAAKe,MAAMooB,oBACjFnpB,KAAKL,QAAQoG,WAAa,SACzB/F,KAAKL,QAAQuC,QAAWlC,KAAKL,QAAQuC,SAAWlC,KAAKL,QAAQuC,OAAOoC,MAAM,0DAQ9E,IAAKtE,KAAKN,YAAYoY,aAAaqC,WAAY,CAC7C,OAGF,IAAIqP,SAAYxpB,KAAKL,QAAQwF,aAAe,KAC1CqY,OAASgM,SAAWxpB,KAAKL,QAAQwG,QAAUnG,KAAKL,QAAQ+G,YAE1D,IAAI+iB,gBAAkBzpB,KAAKgY,OAAOiE,KAAK,8CACrCyN,SAAW1pB,KAAKgY,OAAOiE,KAAK,uCAC5B0N,WAAa3pB,KAAKgY,OAAOiE,KAAK,yCAEhC,IAAI2N,KAAO5pB,KAAKe,MAAM8oB,cAGtB,GAAIH,SAAS7pB,OAAQ,CACnB6pB,SAASvN,IAAIqN,SAAW,MAAQ,QAASA,SAAWhM,OAAO3a,IAAIyD,OAASkX,OAAO3a,IAAIwD,UAAY,EAAIujB,KAAKvoB,IAE1G,GAAIsoB,WAAW9pB,OAAQ,CACrB8pB,WAAWxN,IAAIqN,SAAW,MAAQ,QAASA,SAAWhM,OAAOza,MAAMuD,OAASkX,OAAOza,MAAMsD,UAAY,EAAIujB,KAAKroB,IAEhH,GAAIkoB,gBAAgB5pB,OAAQ,CAC1B4pB,gBAAgBtN,KACdkB,IAAOG,OAAO1a,WAAWwD,OAASsjB,KAAKtoB,EAAIkc,OAAO1a,WAAWwD,OAC7D8W,KAAQwM,KAAKrqB,EAAIie,OAAO1a,WAAWuD,UAKvCrG,KAAKgY,OAAOiE,KAAK,2BACdE,IAAI,kBAAmBnc,KAAKe,MAAM+oB,kBAAkBC,eAGvD,IAAIC,SAAWhqB,KAAKe,MAAMgpB,cAE1B,IAAIE,QAAU,GAEd,GAAIjqB,KAAKL,QAAQwF,WAAY,CAC3B8kB,qCAAuCD,SAAvC,6BACK,CACLC,sCAAwCD,SAAxC,yBAGFhqB,KAAKgY,OAAOiE,KAAK,4BAA4BE,IAAI,aAAc8N,6CAhG/D,OAAOjqB,KAAKN,YAAYC,0CAIxB,OAAOK,KAAKN,YAAYoY,aAAa/W,kDAgG1B+X,gzBCtHTE,wBAIJ,SAAAA,aAAYtZ,aAAaK,gBAAAC,KAAAgZ,cAIvBhZ,KAAKN,YAAcA,YAInBM,KAAK4F,MAAQ,0EAIb,QAAS5F,KAAK4F,0CAOd5F,KAAK4F,MAAQ5F,KAAKN,YAAYC,QAAQiG,MACpC5F,KAAKN,YAAYO,QAAQgc,KAAKjc,KAAKN,YAAYC,QAAQiG,OAAS,KAElE,GAAI5F,KAAK4F,OAAU5F,KAAK4F,MAAM/F,SAAW,EAAI,CAE3CG,KAAK4F,MAAQ,8CAKf,GAAI5F,KAAKyf,WAAY,CACnBzf,KAAK4F,MAAM1E,IAAI,yDAQjB,IAAKlB,KAAKN,YAAYoY,aAAaqC,aAAena,KAAKyf,WAAY,CACjE,OAGF,IAAIyK,SAAWlqB,KAAKN,YAAYoY,aAAayJ,iBAE7C,IAAI4I,QAAUC,WAAcF,UAE5B,IAAIG,IAAMrqB,KAAK4F,MAAMqW,KAAK,KAAKqO,GAAG,GAElC,GAAID,IAAIxqB,OAAS,EAAG,CAClBwqB,IAAIlO,IAAIgO,YACH,CACLnqB,KAAK4F,MAAMuW,IAAIgO,oDAKNnR", "file": "bootstrap-colorpicker.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"jquery\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"bootstrap-colorpicker\", [\"jquery\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"bootstrap-colorpicker\"] = factory(require(\"jquery\"));\n\telse\n\t\troot[\"bootstrap-colorpicker\"] = factory(root[\"jQuery\"]);\n})(window, function(__WEBPACK_EXTERNAL_MODULE__0__) {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 7);\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__0__;", "'use strict';\n\nimport $ from 'jquery';\n\n/**\n * Colorpicker extension class.\n */\nclass Extension {\n  /**\n   * @param {Colorpicker} colorpicker\n   * @param {Object} options\n   */\n  constructor(colorpicker, options = {}) {\n    /**\n     * The colorpicker instance\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * Extension options\n     *\n     * @type {Object}\n     */\n    this.options = options;\n\n    if (!(this.colorpicker.element && this.colorpicker.element.length)) {\n      throw new Error('Extension: this.colorpicker.element is not valid');\n    }\n\n    this.colorpicker.element.on('colorpickerCreate.colorpicker-ext', $.proxy(this.onCreate, this));\n    this.colorpicker.element.on('colorpickerDestroy.colorpicker-ext', $.proxy(this.onDestroy, this));\n    this.colorpicker.element.on('colorpickerUpdate.colorpicker-ext', $.proxy(this.onUpdate, this));\n    this.colorpicker.element.on('colorpickerChange.colorpicker-ext', $.proxy(this.onChange, this));\n    this.colorpicker.element.on('colorpickerInvalid.colorpicker-ext', $.proxy(this.onInvalid, this));\n    this.colorpicker.element.on('colorpickerShow.colorpicker-ext', $.proxy(this.onShow, this));\n    this.colorpicker.element.on('colorpickerHide.colorpicker-ext', $.proxy(this.onHide, this));\n    this.colorpicker.element.on('colorpickerEnable.colorpicker-ext', $.proxy(this.onEnable, this));\n    this.colorpicker.element.on('colorpickerDisable.colorpicker-ext', $.proxy(this.onDisable, this));\n  }\n\n  /**\n   * Function called every time a new color needs to be created.\n   * Return false to skip this resolver and continue with other extensions' ones\n   * or return anything else to consider the color resolved.\n   *\n   * @param {ColorItem|String|*} color\n   * @param {boolean} realColor if true, the color should resolve into a real (not named) color code\n   * @return {ColorItem|String|*}\n   */\n  resolveColor(color, realColor = true) {\n    return false;\n  }\n\n  /**\n   * Method called after the colorpicker is created\n   *\n   * @listens Colorpicker#colorpickerCreate\n   * @param {Event} event\n   */\n  onCreate(event) {\n    // to be extended\n  }\n\n  /**\n   * Method called after the colorpicker is destroyed\n   *\n   * @listens Colorpicker#colorpickerDestroy\n   * @param {Event} event\n   */\n  onDestroy(event) {\n    this.colorpicker.element.off('.colorpicker-ext');\n  }\n\n  /**\n   * Method called after the colorpicker is updated\n   *\n   * @listens Colorpicker#colorpickerUpdate\n   * @param {Event} event\n   */\n  onUpdate(event) {\n    // to be extended\n  }\n\n  /**\n   * Method called after the colorpicker color is changed\n   *\n   * @listens Colorpicker#colorpickerChange\n   * @param {Event} event\n   */\n  onChange(event) {\n    // to be extended\n  }\n\n  /**\n   * Method called when the colorpicker color is invalid\n   *\n   * @listens Colorpicker#colorpickerInvalid\n   * @param {Event} event\n   */\n  onInvalid(event) {\n    // to be extended\n  }\n\n  /**\n   * Method called after the colorpicker is hidden\n   *\n   * @listens Colorpicker#colorpickerHide\n   * @param {Event} event\n   */\n  onHide(event) {\n    // to be extended\n  }\n\n  /**\n   * Method called after the colorpicker is shown\n   *\n   * @listens Colorpicker#colorpickerShow\n   * @param {Event} event\n   */\n  onShow(event) {\n    // to be extended\n  }\n\n  /**\n   * Method called after the colorpicker is disabled\n   *\n   * @listens Colorpicker#colorpickerDisable\n   * @param {Event} event\n   */\n  onDisable(event) {\n    // to be extended\n  }\n\n  /**\n   * Method called after the colorpicker is enabled\n   *\n   * @listens Colorpicker#colorpickerEnable\n   * @param {Event} event\n   */\n  onEnable(event) {\n    // to be extended\n  }\n}\n\nexport default Extension;\n", "/**\n * Color manipulation class, specific for Bootstrap Colorpicker\n */\nimport QixColor from 'color';\n\n/**\n * HSVA color data class, containing the hue, saturation, value and alpha\n * information.\n */\nclass HSVAColor {\n  /**\n   * @param {number|int} h\n   * @param {number|int} s\n   * @param {number|int} v\n   * @param {number|int} a\n   */\n  constructor(h, s, v, a) {\n    this.h = isNaN(h) ? 0 : h;\n    this.s = isNaN(s) ? 0 : s;\n    this.v = isNaN(v) ? 0 : v;\n    this.a = isNaN(h) ? 1 : a;\n  }\n\n  toString() {\n    return `${this.h}, ${this.s}%, ${this.v}%, ${this.a}`;\n  }\n}\n\n/**\n * HSVA color manipulation\n */\nclass ColorItem {\n\n  /**\n   * Returns the HSVAColor class\n   *\n   * @static\n   * @example let colorData = new ColorItem.HSVAColor(360, 100, 100, 1);\n   * @returns {HSVAColor}\n   */\n  static get HSVAColor() {\n    return HSVAColor;\n  }\n\n  /**\n   * Applies a method of the QixColor API and returns a new Color object or\n   * the return value of the method call.\n   *\n   * If no argument is provided, the internal QixColor object is returned.\n   *\n   * @param {String} fn QixColor function name\n   * @param args QixColor function arguments\n   * @example let darkerColor = color.api('darken', 0.25);\n   * @example let luminosity = color.api('luminosity');\n   * @example color = color.api('negate');\n   * @example let qColor = color.api().negate();\n   * @returns {ColorItem|QixColor|*}\n   */\n  api(fn, ...args) {\n    if (arguments.length === 0) {\n      return this._color;\n    }\n\n    let result = this._color[fn].apply(this._color, args);\n\n    if (!(result instanceof QixColor)) {\n      // return result of the method call\n      return result;\n    }\n\n    return new ColorItem(result, this.format);\n  }\n\n  /**\n   * Returns the original ColorItem constructor data,\n   * plus a 'valid' flag to know if it's valid or not.\n   *\n   * @returns {{color: *, format: String, valid: boolean}}\n   */\n  get original() {\n    return this._original;\n  }\n\n  /**\n   * @param {ColorItem|HSVAColor|QixColor|String|*|null} color Color data\n   * @param {String|null} format Color model to convert to by default. Supported: 'rgb', 'hsl', 'hex'.\n   */\n  constructor(color = null, format = null) {\n    this.replace(color, format);\n  }\n\n  /**\n   * Replaces the internal QixColor object with a new one.\n   * This also replaces the internal original color data.\n   *\n   * @param {ColorItem|HSVAColor|QixColor|String|*|null} color Color data to be parsed (if needed)\n   * @param {String|null} format Color model to convert to by default. Supported: 'rgb', 'hsl', 'hex'.\n   * @example color.replace('rgb(255,0,0)', 'hsl');\n   * @example color.replace(hsvaColorData);\n   */\n  replace(color, format = null) {\n    format = ColorItem.sanitizeFormat(format);\n\n    /**\n     * @type {{color: *, format: String}}\n     * @private\n     */\n    this._original = {\n      color: color,\n      format: format,\n      valid: true\n    };\n    /**\n     * @type {QixColor}\n     * @private\n     */\n    this._color = ColorItem.parse(color);\n\n    if (this._color === null) {\n      this._color = QixColor();\n      this._original.valid = false;\n      return;\n    }\n\n    /**\n     * @type {*|string}\n     * @private\n     */\n    this._format = format ? format :\n      (ColorItem.isHex(color) ? 'hex' : this._color.model);\n  }\n\n  /**\n   * Parses the color returning a Qix Color object or null if cannot be\n   * parsed.\n   *\n   * @param {ColorItem|HSVAColor|QixColor|String|*|null} color Color data\n   * @example let qColor = ColorItem.parse('rgb(255,0,0)');\n   * @static\n   * @returns {QixColor|null}\n   */\n  static parse(color) {\n    if (color instanceof QixColor) {\n      return color;\n    }\n\n    if (color instanceof ColorItem) {\n      return color._color;\n    }\n\n    let format = null;\n\n    if (color instanceof HSVAColor) {\n      color = [color.h, color.s, color.v, isNaN(color.a) ? 1 : color.a];\n    } else {\n      color = ColorItem.sanitizeString(color);\n    }\n\n    if (color === null) {\n      return null;\n    }\n\n    if (Array.isArray(color)) {\n      format = 'hsv';\n    }\n\n    try {\n      return QixColor(color, format);\n    } catch (e) {\n      return null;\n    }\n  }\n\n  /**\n   * Sanitizes a color string, adding missing hash to hexadecimal colors\n   * and converting 'transparent' to a color code.\n   *\n   * @param {String|*} str Color string\n   * @example let colorStr = ColorItem.sanitizeString('ffaa00');\n   * @static\n   * @returns {String|*}\n   */\n  static sanitizeString(str) {\n    if (!(typeof str === 'string' || str instanceof String)) {\n      return str;\n    }\n\n    if (str.match(/^[0-9a-f]{2,}$/i)) {\n      return `#${str}`;\n    }\n\n    if (str.toLowerCase() === 'transparent') {\n      return '#FFFFFF00';\n    }\n\n    return str;\n  }\n\n  /**\n   * Detects if a value is a string and a color in hexadecimal format (in any variant).\n   *\n   * @param {String} str\n   * @example ColorItem.isHex('rgba(0,0,0)'); // false\n   * @example ColorItem.isHex('ffaa00'); // true\n   * @example ColorItem.isHex('#ffaa00'); // true\n   * @static\n   * @returns {boolean}\n   */\n  static isHex(str) {\n    if (!(typeof str === 'string' || str instanceof String)) {\n      return false;\n    }\n\n    return !!str.match(/^#?[0-9a-f]{2,}$/i);\n  }\n\n  /**\n   * Sanitizes a color format to one supported by web browsers.\n   * Returns an empty string of the format can't be recognised.\n   *\n   * @param {String|*} format\n   * @example ColorItem.sanitizeFormat('rgba'); // 'rgb'\n   * @example ColorItem.isHex('hex8'); // 'hex'\n   * @example ColorItem.isHex('invalid'); // ''\n   * @static\n   * @returns {String} 'rgb', 'hsl', 'hex' or ''.\n   */\n  static sanitizeFormat(format) {\n    switch (format) {\n      case 'hex':\n      case 'hex3':\n      case 'hex4':\n      case 'hex6':\n      case 'hex8':\n        return 'hex';\n      case 'rgb':\n      case 'rgba':\n      case 'keyword':\n      case 'name':\n        return 'rgb';\n      case 'hsl':\n      case 'hsla':\n      case 'hsv':\n      case 'hsva':\n      case 'hwb': // HWB this is supported by Qix Color, but not by browsers\n      case 'hwba':\n        return 'hsl';\n      default :\n        return '';\n    }\n  }\n\n  /**\n   * Returns true if the color is valid, false if not.\n   *\n   * @returns {boolean}\n   */\n  isValid() {\n    return this._original.valid === true;\n  }\n\n  /**\n   * Hue value from 0 to 360\n   *\n   * @returns {int}\n   */\n  get hue() {\n    return this._color.hue();\n  }\n\n  /**\n   * Saturation value from 0 to 100\n   *\n   * @returns {int}\n   */\n  get saturation() {\n    return this._color.saturationv();\n  }\n\n  /**\n   * Value channel value from 0 to 100\n   *\n   * @returns {int}\n   */\n  get value() {\n    return this._color.value();\n  }\n\n  /**\n   * Alpha value from 0.0 to 1.0\n   *\n   * @returns {number}\n   */\n  get alpha() {\n    let a = this._color.alpha();\n\n    return isNaN(a) ? 1 : a;\n  }\n\n  /**\n   * Default color format to convert to when calling toString() or string()\n   *\n   * @returns {String} 'rgb', 'hsl', 'hex' or ''\n   */\n  get format() {\n    return this._format ? this._format : this._color.model;\n  }\n\n  /**\n   * Sets the hue value\n   *\n   * @param {int} value Integer from 0 to 360\n   */\n  set hue(value) {\n    this._color = this._color.hue(value);\n  }\n\n  /**\n   * Sets the hue ratio, where 1.0 is 0, 0.5 is 180 and 0.0 is 360.\n   *\n   * @ignore\n   * @param {number} h Ratio from 1.0 to 0.0\n   */\n  setHueRatio(h) {\n    this.hue = ((1 - h) * 360);\n  }\n\n  /**\n   * Sets the saturation value\n   *\n   * @param {int} value Integer from 0 to 100\n   */\n  set saturation(value) {\n    this._color = this._color.saturationv(value);\n  }\n\n  /**\n   * Sets the saturation ratio, where 1.0 is 100 and 0.0 is 0.\n   *\n   * @ignore\n   * @param {number} s Ratio from 0.0 to 1.0\n   */\n  setSaturationRatio(s) {\n    this.saturation = (s * 100);\n  }\n\n  /**\n   * Sets the 'value' channel value\n   *\n   * @param {int} value Integer from 0 to 100\n   */\n  set value(value) {\n    this._color = this._color.value(value);\n  }\n\n  /**\n   * Sets the value ratio, where 1.0 is 0 and 0.0 is 100.\n   *\n   * @ignore\n   * @param {number} v Ratio from 1.0 to 0.0\n   */\n  setValueRatio(v) {\n    this.value = ((1 - v) * 100);\n  }\n\n  /**\n   * Sets the alpha value. It will be rounded to 2 decimals.\n   *\n   * @param {int} value Float from 0.0 to 1.0\n   */\n  set alpha(value) {\n    // 2 decimals max\n    this._color = this._color.alpha(Math.round(value * 100) / 100);\n  }\n\n  /**\n   * Sets the alpha ratio, where 1.0 is 0.0 and 0.0 is 1.0.\n   *\n   * @ignore\n   * @param {number} a Ratio from 1.0 to 0.0\n   */\n  setAlphaRatio(a) {\n    this.alpha = 1 - a;\n  }\n\n  /**\n   * Sets the default color format\n   *\n   * @param {String} value Supported: 'rgb', 'hsl', 'hex'\n   */\n  set format(value) {\n    this._format = ColorItem.sanitizeFormat(value);\n  }\n\n  /**\n   * Returns true if the saturation value is zero, false otherwise\n   *\n   * @returns {boolean}\n   */\n  isDesaturated() {\n    return this.saturation === 0;\n  }\n\n  /**\n   * Returns true if the alpha value is zero, false otherwise\n   *\n   * @returns {boolean}\n   */\n  isTransparent() {\n    return this.alpha === 0;\n  }\n\n  /**\n   * Returns true if the alpha value is numeric and less than 1, false otherwise\n   *\n   * @returns {boolean}\n   */\n  hasTransparency() {\n    return this.hasAlpha() && (this.alpha < 1);\n  }\n\n  /**\n   * Returns true if the alpha value is numeric, false otherwise\n   *\n   * @returns {boolean}\n   */\n  hasAlpha() {\n    return !isNaN(this.alpha);\n  }\n\n  /**\n   * Returns a new HSVAColor object, based on the current color\n   *\n   * @returns {HSVAColor}\n   */\n  toObject() {\n    return new HSVAColor(this.hue, this.saturation, this.value, this.alpha);\n  }\n\n  /**\n   * Alias of toObject()\n   *\n   * @returns {HSVAColor}\n   */\n  toHsva() {\n    return this.toObject();\n  }\n\n  /**\n   * Returns a new HSVAColor object with the ratio values (from 0.0 to 1.0),\n   * based on the current color.\n   *\n   * @ignore\n   * @returns {HSVAColor}\n   */\n  toHsvaRatio() {\n    return new HSVAColor(\n      this.hue / 360,\n      this.saturation / 100,\n      this.value / 100,\n      this.alpha\n    );\n  }\n\n  /**\n   * Converts the current color to its string representation,\n   * using the internal format of this instance.\n   *\n   * @returns {String}\n   */\n  toString() {\n    return this.string();\n  }\n\n  /**\n   * Converts the current color to its string representation,\n   * using the given format.\n   *\n   * @param {String|null} format Format to convert to. If empty or null, the internal format will be used.\n   * @returns {String}\n   */\n  string(format = null) {\n    format = ColorItem.sanitizeFormat(format ? format : this.format);\n\n    if (!format) {\n      return this._color.round().string();\n    }\n\n    if (this._color[format] === undefined) {\n      throw new Error(`Unsupported color format: '${format}'`);\n    }\n\n    let str = this._color[format]();\n\n    return str.round ? str.round().string() : str;\n  }\n\n  /**\n   * Returns true if the given color values equals this one, false otherwise.\n   * The format is not compared.\n   * If any of the colors is invalid, the result will be false.\n   *\n   * @param {ColorItem|HSVAColor|QixColor|String|*|null} color Color data\n   *\n   * @returns {boolean}\n   */\n  equals(color) {\n    color = (color instanceof ColorItem) ? color : new ColorItem(color);\n\n    if (!color.isValid() || !this.isValid()) {\n      return false;\n    }\n\n    return (\n      this.hue === color.hue &&\n      this.saturation === color.saturation &&\n      this.value === color.value &&\n      this.alpha === color.alpha\n    );\n  }\n\n  /**\n   * Creates a copy of this instance\n   *\n   * @returns {ColorItem}\n   */\n  getClone() {\n    return new ColorItem(this._color, this.format);\n  }\n\n  /**\n   * Creates a copy of this instance, only copying the hue value,\n   * and setting the others to its max value.\n   *\n   * @returns {ColorItem}\n   */\n  getCloneHueOnly() {\n    return new ColorItem([this.hue, 100, 100, 1], this.format);\n  }\n\n  /**\n   * Creates a copy of this instance setting the alpha to the max.\n   *\n   * @returns {ColorItem}\n   */\n  getCloneOpaque() {\n    return new ColorItem(this._color.alpha(1), this.format);\n  }\n\n  /**\n   * Converts the color to a RGB string\n   *\n   * @returns {String}\n   */\n  toRgbString() {\n    return this.string('rgb');\n  }\n\n  /**\n   * Converts the color to a Hexadecimal string\n   *\n   * @returns {String}\n   */\n  toHexString() {\n    return this.string('hex');\n  }\n\n  /**\n   * Converts the color to a HSL string\n   *\n   * @returns {String}\n   */\n  toHslString() {\n    return this.string('hsl');\n  }\n\n  /**\n   * Returns true if the color is dark, false otherwhise.\n   * This is useful to decide a text color.\n   *\n   * @returns {boolean}\n   */\n  isDark() {\n    return this._color.isDark();\n  }\n\n  /**\n   * Returns true if the color is light, false otherwhise.\n   * This is useful to decide a text color.\n   *\n   * @returns {boolean}\n   */\n  isLight() {\n    return this._color.isLight();\n  }\n\n  /**\n   * Generates a list of colors using the given hue-based formula or the given array of hue values.\n   * Hue formulas can be extended using ColorItem.colorFormulas static property.\n   *\n   * @param {String|Number[]} formula Examples: 'complementary', 'triad', 'tetrad', 'splitcomplement', [180, 270]\n   * @example let colors = color.generate('triad');\n   * @example let colors = color.generate([45, 80, 112, 200]);\n   * @returns {ColorItem[]}\n   */\n  generate(formula) {\n    let hues = [];\n\n    if (Array.isArray(formula)) {\n      hues = formula;\n    } else if (!ColorItem.colorFormulas.hasOwnProperty(formula)) {\n      throw new Error(`No color formula found with the name '${formula}'.`);\n    } else {\n      hues = ColorItem.colorFormulas[formula];\n    }\n\n    let colors = [], mainColor = this._color, format = this.format;\n\n    hues.forEach(function (hue) {\n      let levels = [\n        hue ? ((mainColor.hue() + hue) % 360) : mainColor.hue(),\n        mainColor.saturationv(),\n        mainColor.value(),\n        mainColor.alpha()\n      ];\n\n      colors.push(new ColorItem(levels, format));\n    });\n\n    return colors;\n  }\n}\n\n/**\n * List of hue-based color formulas used by ColorItem.prototype.generate()\n *\n * @static\n * @type {{complementary: number[], triad: number[], tetrad: number[], splitcomplement: number[]}}\n */\nColorItem.colorFormulas = {\n  complementary: [180],\n  triad: [0, 120, 240],\n  tetrad: [0, 90, 180, 270],\n  splitcomplement: [0, 72, 216]\n};\n\nexport default ColorItem;\n\nexport {\n  HSVAColor,\n  ColorItem\n};\n", "'use strict';\n/**\n * @module\n */\n\n// adjust these values accordingly to the sass vars\nlet sassVars = {\n  'bar_size_short': 16,\n  'base_margin': 6,\n  'columns': 6\n};\n\nlet sliderSize = (sassVars.bar_size_short * sassVars.columns) + (sassVars.base_margin * (sassVars.columns - 1));\n\n/**\n * Colorpicker default options\n */\nexport default {\n  /**\n   * Custom class to be added to the `.colorpicker-element` element\n   *\n   * @type {String|null}\n   * @default null\n   */\n  customClass: null,\n  /**\n   * Sets a initial color, ignoring the one from the element/input value or the data-color attribute.\n   *\n   * @type {(String|ColorItem|boolean)}\n   * @default false\n   */\n  color: false,\n  /**\n   * Fallback color to use when the given color is invalid.\n   * If false, the latest valid color will be used as a fallback.\n   *\n   * @type {String|ColorItem|boolean}\n   * @default false\n   */\n  fallbackColor: false,\n  /**\n   * Forces an specific color format. If 'auto', it will be automatically detected the first time only,\n   * but if null it will be always recalculated.\n   *\n   * Note that the ending 'a' of the format meaning \"alpha\" has currently no effect, meaning that rgb is the same as\n   * rgba excepting if the alpha channel is disabled (see useAlpha).\n   *\n   * @type {('rgb'|'hex'|'hsl'|'auto'|null)}\n   * @default 'auto'\n   */\n  format: 'auto',\n  /**\n   * Horizontal mode layout.\n   *\n   * If true, the hue and alpha channel bars will be rendered horizontally, above the saturation selector.\n   *\n   * @type {boolean}\n   * @default false\n   */\n  horizontal: false,\n  /**\n   * Forces to show the colorpicker as an inline element.\n   *\n   * Note that if there is no container specified, the inline element\n   * will be added to the body, so you may want to set the container option.\n   *\n   * @type {boolean}\n   * @default false\n   */\n  inline: false,\n  /**\n   * Container where the colorpicker is appended to in the DOM.\n   *\n   * If is a string (CSS selector), the colorpicker will be placed inside this container.\n   * If true, the `.colorpicker-element` element itself will be used as the container.\n   * If false, the document body is used as the container, unless it is a popover (in this case it is appended to the\n   * popover body instead).\n   *\n   * @type {String|boolean}\n   * @default false\n   */\n  container: false,\n  /**\n   * Bootstrap Popover options.\n   * The trigger, content and html options are always ignored.\n   *\n   * @type {boolean}\n   * @default Object\n   */\n  popover: {\n    animation: true,\n    placement: 'bottom',\n    fallbackPlacement: 'flip'\n  },\n  /**\n   * If true, loads the 'debugger' extension automatically, which logs the events in the console\n   * @type {boolean}\n   * @default false\n   */\n  debug: false,\n  /**\n   * Child CSS selector for the colorpicker input.\n   *\n   * @type {String}\n   * @default 'input'\n   */\n  input: 'input',\n  /**\n   * Child CSS selector for the colorpicker addon.\n   * If it exists, the child <i> element background will be changed on color change.\n   *\n   * @type {String}\n   * @default '.colorpicker-trigger, .colorpicker-input-addon'\n   */\n  addon: '.colorpicker-input-addon',\n  /**\n   * If true, the input content will be replaced always with a valid color,\n   * if false, the invalid color will be left in the input,\n   *   while the internal color object will still resolve into a valid one.\n   *\n   * @type {boolean}\n   * @default true\n   */\n  autoInputFallback: true,\n  /**\n   * If true a hash will be prepended to hexadecimal colors.\n   * If false, the hash will be removed.\n   * This only affects the input values in hexadecimal format.\n   *\n   * @type {boolean}\n   * @default true\n   */\n  useHashPrefix: true,\n  /**\n   * If true, the alpha channel bar will be displayed no matter what.\n   *\n   * If false, it will be always hidden and alpha channel will be disabled also programmatically, meaning that\n   * the selected or typed color will be always opaque.\n   *\n   * If null, the alpha channel will be automatically disabled/enabled depending if the initial color format supports\n   * alpha or not.\n   *\n   * @type {boolean}\n   * @default true\n   */\n  useAlpha: true,\n  /**\n   * Colorpicker widget template\n   * @type {String}\n   * @example\n   * <!-- This is the default template: -->\n   * <div class=\"colorpicker\">\n   *   <div class=\"colorpicker-saturation\"><i class=\"colorpicker-guide\"></i></div>\n   *   <div class=\"colorpicker-hue\"><i class=\"colorpicker-guide\"></i></div>\n   *   <div class=\"colorpicker-alpha\">\n   *     <div class=\"colorpicker-alpha-color\"></div>\n   *     <i class=\"colorpicker-guide\"></i>\n   *   </div>\n   * </div>\n   */\n  template: `<div class=\"colorpicker\">\n      <div class=\"colorpicker-saturation\"><i class=\"colorpicker-guide\"></i></div>\n      <div class=\"colorpicker-hue\"><i class=\"colorpicker-guide\"></i></div>\n      <div class=\"colorpicker-alpha\">\n        <div class=\"colorpicker-alpha-color\"></div>\n        <i class=\"colorpicker-guide\"></i>\n      </div>\n    </div>`,\n  /**\n   *\n   * Associative object with the extension class name and its config.\n   * Colorpicker comes with many bundled extensions: debugger, palette, preview and swatches (a superset of palette).\n   *\n   * @type {Object[]}\n   * @example\n   *   extensions: [\n   *     {\n   *       name: 'swatches'\n   *       options: {\n   *         colors: {\n   *           'primary': '#337ab7',\n   *           'success': '#5cb85c',\n   *           'info': '#5bc0de',\n   *           'warning': '#f0ad4e',\n   *           'danger': '#d9534f'\n   *         },\n   *         namesAsValues: true\n   *       }\n   *     }\n   *   ]\n   */\n  extensions: [\n    {\n      name: 'preview',\n      options: {\n        showText: true\n      }\n    }\n  ],\n  /**\n   * Vertical sliders configuration\n   * @type {Object}\n   */\n  sliders: {\n    saturation: {\n      selector: '.colorpicker-saturation',\n      maxLeft: sliderSize,\n      maxTop: sliderSize,\n      callLeft: 'setSaturationRatio',\n      callTop: 'setValueRatio'\n    },\n    hue: {\n      selector: '.colorpicker-hue',\n      maxLeft: 0,\n      maxTop: sliderSize,\n      callLeft: false,\n      callTop: 'setHueRatio'\n    },\n    alpha: {\n      selector: '.colorpicker-alpha',\n      childSelector: '.colorpicker-alpha-color',\n      maxLeft: 0,\n      maxTop: sliderSize,\n      callLeft: false,\n      callTop: 'setAlphaRatio'\n    }\n  },\n  /**\n   * Horizontal sliders configuration\n   * @type {Object}\n   */\n  slidersHorz: {\n    saturation: {\n      selector: '.colorpicker-saturation',\n      maxLeft: sliderSize,\n      maxTop: sliderSize,\n      callLeft: 'setSaturationRatio',\n      callTop: 'setValueRatio'\n    },\n    hue: {\n      selector: '.colorpicker-hue',\n      maxLeft: sliderSize,\n      maxTop: 0,\n      callLeft: 'setHueRatio',\n      callTop: false\n    },\n    alpha: {\n      selector: '.colorpicker-alpha',\n      childSelector: '.colorpicker-alpha-color',\n      maxLeft: sliderSize,\n      maxTop: 0,\n      callLeft: 'setAlphaRatio',\n      callTop: false\n    }\n  }\n};\n", "'use strict';\n\nimport Extension from 'Extension';\nimport $ from 'jquery';\n\nlet defaults = {\n  /**\n   * Key-value pairs defining a color alias and its CSS color representation.\n   *\n   * They can also be just an array of values. In that case, no special names are used, only the real colors.\n   *\n   * @type {Object|Array}\n   * @default null\n   * @example\n   *  {\n   *   'black': '#000000',\n   *   'white': '#ffffff',\n   *   'red': '#FF0000',\n   *   'default': '#777777',\n   *   'primary': '#337ab7',\n   *   'success': '#5cb85c',\n   *   'info': '#5bc0de',\n   *   'warning': '#f0ad4e',\n   *   'danger': '#d9534f'\n   *  }\n   *\n   * @example ['#f0ad4e', '#337ab7', '#5cb85c']\n   */\n  colors: null,\n  /**\n   * If true, when a color swatch is selected the name (alias) will be used as input value,\n   * otherwise the swatch real color value will be used.\n   *\n   * @type {boolean}\n   * @default true\n   */\n  namesAsValues: true\n};\n\n/**\n * Palette extension\n * @ignore\n */\nclass Palette extends Extension {\n\n  /**\n   * @returns {Object|Array}\n   */\n  get colors() {\n    return this.options.colors;\n  }\n\n  constructor(colorpicker, options = {}) {\n    super(colorpicker, $.extend(true, {}, defaults, options));\n\n    if ((!Array.isArray(this.options.colors)) && (typeof this.options.colors !== 'object')) {\n      this.options.colors = null;\n    }\n  }\n\n  /**\n   * @returns {int}\n   */\n  getLength() {\n    if (!this.options.colors) {\n      return 0;\n    }\n\n    if (Array.isArray(this.options.colors)) {\n      return this.options.colors.length;\n    }\n\n    if (typeof this.options.colors === 'object') {\n      return Object.keys(this.options.colors).length;\n    }\n\n    return 0;\n  }\n\n  resolveColor(color, realColor = true) {\n    if (this.getLength() <= 0) {\n      return false;\n    }\n\n    // Array of colors\n    if (Array.isArray(this.options.colors)) {\n      if (this.options.colors.indexOf(color) >= 0) {\n        return color;\n      }\n      if (this.options.colors.indexOf(color.toUpperCase()) >= 0) {\n        return color.toUpperCase();\n      }\n      if (this.options.colors.indexOf(color.toLowerCase()) >= 0) {\n        return color.toLowerCase();\n      }\n      return false;\n    }\n\n    if (typeof this.options.colors !== 'object') {\n      return false;\n    }\n\n    // Map of objects\n    if (!this.options.namesAsValues || realColor) {\n      return this.getValue(color, false);\n    }\n    return this.getName(color, this.getName('#' + color));\n  }\n\n  /**\n   * Given a color value, returns the corresponding color name or defaultValue.\n   *\n   * @param {String} value\n   * @param {*} defaultValue\n   * @returns {*}\n   */\n  getName(value, defaultValue = false) {\n    if (!(typeof value === 'string') || !this.options.colors) {\n      return defaultValue;\n    }\n    for (let name in this.options.colors) {\n      if (!this.options.colors.hasOwnProperty(name)) {\n        continue;\n      }\n      if (this.options.colors[name].toLowerCase() === value.toLowerCase()) {\n        return name;\n      }\n    }\n    return defaultValue;\n  }\n\n  /**\n   * Given a color name, returns the corresponding color value or defaultValue.\n   *\n   * @param {String} name\n   * @param {*} defaultValue\n   * @returns {*}\n   */\n  getValue(name, defaultValue = false) {\n    if (!(typeof name === 'string') || !this.options.colors) {\n      return defaultValue;\n    }\n    if (this.options.colors.hasOwnProperty(name)) {\n      return this.options.colors[name];\n    }\n    return defaultValue;\n  }\n}\n\nexport default Palette;\n", "'use strict'\r\n\r\nmodule.exports = {\r\n\t\"aliceblue\": [240, 248, 255],\r\n\t\"antiquewhite\": [250, 235, 215],\r\n\t\"aqua\": [0, 255, 255],\r\n\t\"aquamarine\": [127, 255, 212],\r\n\t\"azure\": [240, 255, 255],\r\n\t\"beige\": [245, 245, 220],\r\n\t\"bisque\": [255, 228, 196],\r\n\t\"black\": [0, 0, 0],\r\n\t\"blanchedalmond\": [255, 235, 205],\r\n\t\"blue\": [0, 0, 255],\r\n\t\"blueviolet\": [138, 43, 226],\r\n\t\"brown\": [165, 42, 42],\r\n\t\"burlywood\": [222, 184, 135],\r\n\t\"cadetblue\": [95, 158, 160],\r\n\t\"chartreuse\": [127, 255, 0],\r\n\t\"chocolate\": [210, 105, 30],\r\n\t\"coral\": [255, 127, 80],\r\n\t\"cornflowerblue\": [100, 149, 237],\r\n\t\"cornsilk\": [255, 248, 220],\r\n\t\"crimson\": [220, 20, 60],\r\n\t\"cyan\": [0, 255, 255],\r\n\t\"darkblue\": [0, 0, 139],\r\n\t\"darkcyan\": [0, 139, 139],\r\n\t\"darkgoldenrod\": [184, 134, 11],\r\n\t\"darkgray\": [169, 169, 169],\r\n\t\"darkgreen\": [0, 100, 0],\r\n\t\"darkgrey\": [169, 169, 169],\r\n\t\"darkkhaki\": [189, 183, 107],\r\n\t\"darkmagenta\": [139, 0, 139],\r\n\t\"darkolivegreen\": [85, 107, 47],\r\n\t\"darkorange\": [255, 140, 0],\r\n\t\"darkorchid\": [153, 50, 204],\r\n\t\"darkred\": [139, 0, 0],\r\n\t\"darksalmon\": [233, 150, 122],\r\n\t\"darkseagreen\": [143, 188, 143],\r\n\t\"darkslateblue\": [72, 61, 139],\r\n\t\"darkslategray\": [47, 79, 79],\r\n\t\"darkslategrey\": [47, 79, 79],\r\n\t\"darkturquoise\": [0, 206, 209],\r\n\t\"darkviolet\": [148, 0, 211],\r\n\t\"deeppink\": [255, 20, 147],\r\n\t\"deepskyblue\": [0, 191, 255],\r\n\t\"dimgray\": [105, 105, 105],\r\n\t\"dimgrey\": [105, 105, 105],\r\n\t\"dodgerblue\": [30, 144, 255],\r\n\t\"firebrick\": [178, 34, 34],\r\n\t\"floralwhite\": [255, 250, 240],\r\n\t\"forestgreen\": [34, 139, 34],\r\n\t\"fuchsia\": [255, 0, 255],\r\n\t\"gainsboro\": [220, 220, 220],\r\n\t\"ghostwhite\": [248, 248, 255],\r\n\t\"gold\": [255, 215, 0],\r\n\t\"goldenrod\": [218, 165, 32],\r\n\t\"gray\": [128, 128, 128],\r\n\t\"green\": [0, 128, 0],\r\n\t\"greenyellow\": [173, 255, 47],\r\n\t\"grey\": [128, 128, 128],\r\n\t\"honeydew\": [240, 255, 240],\r\n\t\"hotpink\": [255, 105, 180],\r\n\t\"indianred\": [205, 92, 92],\r\n\t\"indigo\": [75, 0, 130],\r\n\t\"ivory\": [255, 255, 240],\r\n\t\"khaki\": [240, 230, 140],\r\n\t\"lavender\": [230, 230, 250],\r\n\t\"lavenderblush\": [255, 240, 245],\r\n\t\"lawngreen\": [124, 252, 0],\r\n\t\"lemonchiffon\": [255, 250, 205],\r\n\t\"lightblue\": [173, 216, 230],\r\n\t\"lightcoral\": [240, 128, 128],\r\n\t\"lightcyan\": [224, 255, 255],\r\n\t\"lightgoldenrodyellow\": [250, 250, 210],\r\n\t\"lightgray\": [211, 211, 211],\r\n\t\"lightgreen\": [144, 238, 144],\r\n\t\"lightgrey\": [211, 211, 211],\r\n\t\"lightpink\": [255, 182, 193],\r\n\t\"lightsalmon\": [255, 160, 122],\r\n\t\"lightseagreen\": [32, 178, 170],\r\n\t\"lightskyblue\": [135, 206, 250],\r\n\t\"lightslategray\": [119, 136, 153],\r\n\t\"lightslategrey\": [119, 136, 153],\r\n\t\"lightsteelblue\": [176, 196, 222],\r\n\t\"lightyellow\": [255, 255, 224],\r\n\t\"lime\": [0, 255, 0],\r\n\t\"limegreen\": [50, 205, 50],\r\n\t\"linen\": [250, 240, 230],\r\n\t\"magenta\": [255, 0, 255],\r\n\t\"maroon\": [128, 0, 0],\r\n\t\"mediumaquamarine\": [102, 205, 170],\r\n\t\"mediumblue\": [0, 0, 205],\r\n\t\"mediumorchid\": [186, 85, 211],\r\n\t\"mediumpurple\": [147, 112, 219],\r\n\t\"mediumseagreen\": [60, 179, 113],\r\n\t\"mediumslateblue\": [123, 104, 238],\r\n\t\"mediumspringgreen\": [0, 250, 154],\r\n\t\"mediumturquoise\": [72, 209, 204],\r\n\t\"mediumvioletred\": [199, 21, 133],\r\n\t\"midnightblue\": [25, 25, 112],\r\n\t\"mintcream\": [245, 255, 250],\r\n\t\"mistyrose\": [255, 228, 225],\r\n\t\"moccasin\": [255, 228, 181],\r\n\t\"navajowhite\": [255, 222, 173],\r\n\t\"navy\": [0, 0, 128],\r\n\t\"oldlace\": [253, 245, 230],\r\n\t\"olive\": [128, 128, 0],\r\n\t\"olivedrab\": [107, 142, 35],\r\n\t\"orange\": [255, 165, 0],\r\n\t\"orangered\": [255, 69, 0],\r\n\t\"orchid\": [218, 112, 214],\r\n\t\"palegoldenrod\": [238, 232, 170],\r\n\t\"palegreen\": [152, 251, 152],\r\n\t\"paleturquoise\": [175, 238, 238],\r\n\t\"palevioletred\": [219, 112, 147],\r\n\t\"papayawhip\": [255, 239, 213],\r\n\t\"peachpuff\": [255, 218, 185],\r\n\t\"peru\": [205, 133, 63],\r\n\t\"pink\": [255, 192, 203],\r\n\t\"plum\": [221, 160, 221],\r\n\t\"powderblue\": [176, 224, 230],\r\n\t\"purple\": [128, 0, 128],\r\n\t\"rebeccapurple\": [102, 51, 153],\r\n\t\"red\": [255, 0, 0],\r\n\t\"rosybrown\": [188, 143, 143],\r\n\t\"royalblue\": [65, 105, 225],\r\n\t\"saddlebrown\": [139, 69, 19],\r\n\t\"salmon\": [250, 128, 114],\r\n\t\"sandybrown\": [244, 164, 96],\r\n\t\"seagreen\": [46, 139, 87],\r\n\t\"seashell\": [255, 245, 238],\r\n\t\"sienna\": [160, 82, 45],\r\n\t\"silver\": [192, 192, 192],\r\n\t\"skyblue\": [135, 206, 235],\r\n\t\"slateblue\": [106, 90, 205],\r\n\t\"slategray\": [112, 128, 144],\r\n\t\"slategrey\": [112, 128, 144],\r\n\t\"snow\": [255, 250, 250],\r\n\t\"springgreen\": [0, 255, 127],\r\n\t\"steelblue\": [70, 130, 180],\r\n\t\"tan\": [210, 180, 140],\r\n\t\"teal\": [0, 128, 128],\r\n\t\"thistle\": [216, 191, 216],\r\n\t\"tomato\": [255, 99, 71],\r\n\t\"turquoise\": [64, 224, 208],\r\n\t\"violet\": [238, 130, 238],\r\n\t\"wheat\": [245, 222, 179],\r\n\t\"white\": [255, 255, 255],\r\n\t\"whitesmoke\": [245, 245, 245],\r\n\t\"yellow\": [255, 255, 0],\r\n\t\"yellowgreen\": [154, 205, 50]\r\n};\r\n", "/* MIT license */\nvar cssKeywords = require('color-name');\n\n// NOTE: conversions should only return primitive values (i.e. arrays, or\n//       values that give correct `typeof` results).\n//       do not use box values types (i.e. Number(), String(), etc.)\n\nvar reverseKeywords = {};\nfor (var key in cssKeywords) {\n\tif (cssKeywords.hasOwnProperty(key)) {\n\t\treverseKeywords[cssKeywords[key]] = key;\n\t}\n}\n\nvar convert = module.exports = {\n\trgb: {channels: 3, labels: 'rgb'},\n\thsl: {channels: 3, labels: 'hsl'},\n\thsv: {channels: 3, labels: 'hsv'},\n\thwb: {channels: 3, labels: 'hwb'},\n\tcmyk: {channels: 4, labels: 'cmyk'},\n\txyz: {channels: 3, labels: 'xyz'},\n\tlab: {channels: 3, labels: 'lab'},\n\tlch: {channels: 3, labels: 'lch'},\n\thex: {channels: 1, labels: ['hex']},\n\tkeyword: {channels: 1, labels: ['keyword']},\n\tansi16: {channels: 1, labels: ['ansi16']},\n\tansi256: {channels: 1, labels: ['ansi256']},\n\thcg: {channels: 3, labels: ['h', 'c', 'g']},\n\tapple: {channels: 3, labels: ['r16', 'g16', 'b16']},\n\tgray: {channels: 1, labels: ['gray']}\n};\n\n// hide .channels and .labels properties\nfor (var model in convert) {\n\tif (convert.hasOwnProperty(model)) {\n\t\tif (!('channels' in convert[model])) {\n\t\t\tthrow new Error('missing channels property: ' + model);\n\t\t}\n\n\t\tif (!('labels' in convert[model])) {\n\t\t\tthrow new Error('missing channel labels property: ' + model);\n\t\t}\n\n\t\tif (convert[model].labels.length !== convert[model].channels) {\n\t\t\tthrow new Error('channel and label counts mismatch: ' + model);\n\t\t}\n\n\t\tvar channels = convert[model].channels;\n\t\tvar labels = convert[model].labels;\n\t\tdelete convert[model].channels;\n\t\tdelete convert[model].labels;\n\t\tObject.defineProperty(convert[model], 'channels', {value: channels});\n\t\tObject.defineProperty(convert[model], 'labels', {value: labels});\n\t}\n}\n\nconvert.rgb.hsl = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar min = Math.min(r, g, b);\n\tvar max = Math.max(r, g, b);\n\tvar delta = max - min;\n\tvar h;\n\tvar s;\n\tvar l;\n\n\tif (max === min) {\n\t\th = 0;\n\t} else if (r === max) {\n\t\th = (g - b) / delta;\n\t} else if (g === max) {\n\t\th = 2 + (b - r) / delta;\n\t} else if (b === max) {\n\t\th = 4 + (r - g) / delta;\n\t}\n\n\th = Math.min(h * 60, 360);\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tl = (min + max) / 2;\n\n\tif (max === min) {\n\t\ts = 0;\n\t} else if (l <= 0.5) {\n\t\ts = delta / (max + min);\n\t} else {\n\t\ts = delta / (2 - max - min);\n\t}\n\n\treturn [h, s * 100, l * 100];\n};\n\nconvert.rgb.hsv = function (rgb) {\n\tvar rdif;\n\tvar gdif;\n\tvar bdif;\n\tvar h;\n\tvar s;\n\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar v = Math.max(r, g, b);\n\tvar diff = v - Math.min(r, g, b);\n\tvar diffc = function (c) {\n\t\treturn (v - c) / 6 / diff + 1 / 2;\n\t};\n\n\tif (diff === 0) {\n\t\th = s = 0;\n\t} else {\n\t\ts = diff / v;\n\t\trdif = diffc(r);\n\t\tgdif = diffc(g);\n\t\tbdif = diffc(b);\n\n\t\tif (r === v) {\n\t\t\th = bdif - gdif;\n\t\t} else if (g === v) {\n\t\t\th = (1 / 3) + rdif - bdif;\n\t\t} else if (b === v) {\n\t\t\th = (2 / 3) + gdif - rdif;\n\t\t}\n\t\tif (h < 0) {\n\t\t\th += 1;\n\t\t} else if (h > 1) {\n\t\t\th -= 1;\n\t\t}\n\t}\n\n\treturn [\n\t\th * 360,\n\t\ts * 100,\n\t\tv * 100\n\t];\n};\n\nconvert.rgb.hwb = function (rgb) {\n\tvar r = rgb[0];\n\tvar g = rgb[1];\n\tvar b = rgb[2];\n\tvar h = convert.rgb.hsl(rgb)[0];\n\tvar w = 1 / 255 * Math.min(r, Math.min(g, b));\n\n\tb = 1 - 1 / 255 * Math.max(r, Math.max(g, b));\n\n\treturn [h, w * 100, b * 100];\n};\n\nconvert.rgb.cmyk = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar c;\n\tvar m;\n\tvar y;\n\tvar k;\n\n\tk = Math.min(1 - r, 1 - g, 1 - b);\n\tc = (1 - r - k) / (1 - k) || 0;\n\tm = (1 - g - k) / (1 - k) || 0;\n\ty = (1 - b - k) / (1 - k) || 0;\n\n\treturn [c * 100, m * 100, y * 100, k * 100];\n};\n\n/**\n * See https://en.m.wikipedia.org/wiki/Euclidean_distance#Squared_Euclidean_distance\n * */\nfunction comparativeDistance(x, y) {\n\treturn (\n\t\tMath.pow(x[0] - y[0], 2) +\n\t\tMath.pow(x[1] - y[1], 2) +\n\t\tMath.pow(x[2] - y[2], 2)\n\t);\n}\n\nconvert.rgb.keyword = function (rgb) {\n\tvar reversed = reverseKeywords[rgb];\n\tif (reversed) {\n\t\treturn reversed;\n\t}\n\n\tvar currentClosestDistance = Infinity;\n\tvar currentClosestKeyword;\n\n\tfor (var keyword in cssKeywords) {\n\t\tif (cssKeywords.hasOwnProperty(keyword)) {\n\t\t\tvar value = cssKeywords[keyword];\n\n\t\t\t// Compute comparative distance\n\t\t\tvar distance = comparativeDistance(rgb, value);\n\n\t\t\t// Check if its less, if so set as closest\n\t\t\tif (distance < currentClosestDistance) {\n\t\t\t\tcurrentClosestDistance = distance;\n\t\t\t\tcurrentClosestKeyword = keyword;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn currentClosestKeyword;\n};\n\nconvert.keyword.rgb = function (keyword) {\n\treturn cssKeywords[keyword];\n};\n\nconvert.rgb.xyz = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\n\t// assume sRGB\n\tr = r > 0.04045 ? Math.pow(((r + 0.055) / 1.055), 2.4) : (r / 12.92);\n\tg = g > 0.04045 ? Math.pow(((g + 0.055) / 1.055), 2.4) : (g / 12.92);\n\tb = b > 0.04045 ? Math.pow(((b + 0.055) / 1.055), 2.4) : (b / 12.92);\n\n\tvar x = (r * 0.4124) + (g * 0.3576) + (b * 0.1805);\n\tvar y = (r * 0.2126) + (g * 0.7152) + (b * 0.0722);\n\tvar z = (r * 0.0193) + (g * 0.1192) + (b * 0.9505);\n\n\treturn [x * 100, y * 100, z * 100];\n};\n\nconvert.rgb.lab = function (rgb) {\n\tvar xyz = convert.rgb.xyz(rgb);\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.hsl.rgb = function (hsl) {\n\tvar h = hsl[0] / 360;\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar t1;\n\tvar t2;\n\tvar t3;\n\tvar rgb;\n\tvar val;\n\n\tif (s === 0) {\n\t\tval = l * 255;\n\t\treturn [val, val, val];\n\t}\n\n\tif (l < 0.5) {\n\t\tt2 = l * (1 + s);\n\t} else {\n\t\tt2 = l + s - l * s;\n\t}\n\n\tt1 = 2 * l - t2;\n\n\trgb = [0, 0, 0];\n\tfor (var i = 0; i < 3; i++) {\n\t\tt3 = h + 1 / 3 * -(i - 1);\n\t\tif (t3 < 0) {\n\t\t\tt3++;\n\t\t}\n\t\tif (t3 > 1) {\n\t\t\tt3--;\n\t\t}\n\n\t\tif (6 * t3 < 1) {\n\t\t\tval = t1 + (t2 - t1) * 6 * t3;\n\t\t} else if (2 * t3 < 1) {\n\t\t\tval = t2;\n\t\t} else if (3 * t3 < 2) {\n\t\t\tval = t1 + (t2 - t1) * (2 / 3 - t3) * 6;\n\t\t} else {\n\t\t\tval = t1;\n\t\t}\n\n\t\trgb[i] = val * 255;\n\t}\n\n\treturn rgb;\n};\n\nconvert.hsl.hsv = function (hsl) {\n\tvar h = hsl[0];\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar smin = s;\n\tvar lmin = Math.max(l, 0.01);\n\tvar sv;\n\tvar v;\n\n\tl *= 2;\n\ts *= (l <= 1) ? l : 2 - l;\n\tsmin *= lmin <= 1 ? lmin : 2 - lmin;\n\tv = (l + s) / 2;\n\tsv = l === 0 ? (2 * smin) / (lmin + smin) : (2 * s) / (l + s);\n\n\treturn [h, sv * 100, v * 100];\n};\n\nconvert.hsv.rgb = function (hsv) {\n\tvar h = hsv[0] / 60;\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar hi = Math.floor(h) % 6;\n\n\tvar f = h - Math.floor(h);\n\tvar p = 255 * v * (1 - s);\n\tvar q = 255 * v * (1 - (s * f));\n\tvar t = 255 * v * (1 - (s * (1 - f)));\n\tv *= 255;\n\n\tswitch (hi) {\n\t\tcase 0:\n\t\t\treturn [v, t, p];\n\t\tcase 1:\n\t\t\treturn [q, v, p];\n\t\tcase 2:\n\t\t\treturn [p, v, t];\n\t\tcase 3:\n\t\t\treturn [p, q, v];\n\t\tcase 4:\n\t\t\treturn [t, p, v];\n\t\tcase 5:\n\t\t\treturn [v, p, q];\n\t}\n};\n\nconvert.hsv.hsl = function (hsv) {\n\tvar h = hsv[0];\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\tvar vmin = Math.max(v, 0.01);\n\tvar lmin;\n\tvar sl;\n\tvar l;\n\n\tl = (2 - s) * v;\n\tlmin = (2 - s) * vmin;\n\tsl = s * vmin;\n\tsl /= (lmin <= 1) ? lmin : 2 - lmin;\n\tsl = sl || 0;\n\tl /= 2;\n\n\treturn [h, sl * 100, l * 100];\n};\n\n// http://dev.w3.org/csswg/css-color/#hwb-to-rgb\nconvert.hwb.rgb = function (hwb) {\n\tvar h = hwb[0] / 360;\n\tvar wh = hwb[1] / 100;\n\tvar bl = hwb[2] / 100;\n\tvar ratio = wh + bl;\n\tvar i;\n\tvar v;\n\tvar f;\n\tvar n;\n\n\t// wh + bl cant be > 1\n\tif (ratio > 1) {\n\t\twh /= ratio;\n\t\tbl /= ratio;\n\t}\n\n\ti = Math.floor(6 * h);\n\tv = 1 - bl;\n\tf = 6 * h - i;\n\n\tif ((i & 0x01) !== 0) {\n\t\tf = 1 - f;\n\t}\n\n\tn = wh + f * (v - wh); // linear interpolation\n\n\tvar r;\n\tvar g;\n\tvar b;\n\tswitch (i) {\n\t\tdefault:\n\t\tcase 6:\n\t\tcase 0: r = v; g = n; b = wh; break;\n\t\tcase 1: r = n; g = v; b = wh; break;\n\t\tcase 2: r = wh; g = v; b = n; break;\n\t\tcase 3: r = wh; g = n; b = v; break;\n\t\tcase 4: r = n; g = wh; b = v; break;\n\t\tcase 5: r = v; g = wh; b = n; break;\n\t}\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.cmyk.rgb = function (cmyk) {\n\tvar c = cmyk[0] / 100;\n\tvar m = cmyk[1] / 100;\n\tvar y = cmyk[2] / 100;\n\tvar k = cmyk[3] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = 1 - Math.min(1, c * (1 - k) + k);\n\tg = 1 - Math.min(1, m * (1 - k) + k);\n\tb = 1 - Math.min(1, y * (1 - k) + k);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.rgb = function (xyz) {\n\tvar x = xyz[0] / 100;\n\tvar y = xyz[1] / 100;\n\tvar z = xyz[2] / 100;\n\tvar r;\n\tvar g;\n\tvar b;\n\n\tr = (x * 3.2406) + (y * -1.5372) + (z * -0.4986);\n\tg = (x * -0.9689) + (y * 1.8758) + (z * 0.0415);\n\tb = (x * 0.0557) + (y * -0.2040) + (z * 1.0570);\n\n\t// assume sRGB\n\tr = r > 0.0031308\n\t\t? ((1.055 * Math.pow(r, 1.0 / 2.4)) - 0.055)\n\t\t: r * 12.92;\n\n\tg = g > 0.0031308\n\t\t? ((1.055 * Math.pow(g, 1.0 / 2.4)) - 0.055)\n\t\t: g * 12.92;\n\n\tb = b > 0.0031308\n\t\t? ((1.055 * Math.pow(b, 1.0 / 2.4)) - 0.055)\n\t\t: b * 12.92;\n\n\tr = Math.min(Math.max(0, r), 1);\n\tg = Math.min(Math.max(0, g), 1);\n\tb = Math.min(Math.max(0, b), 1);\n\n\treturn [r * 255, g * 255, b * 255];\n};\n\nconvert.xyz.lab = function (xyz) {\n\tvar x = xyz[0];\n\tvar y = xyz[1];\n\tvar z = xyz[2];\n\tvar l;\n\tvar a;\n\tvar b;\n\n\tx /= 95.047;\n\ty /= 100;\n\tz /= 108.883;\n\n\tx = x > 0.008856 ? Math.pow(x, 1 / 3) : (7.787 * x) + (16 / 116);\n\ty = y > 0.008856 ? Math.pow(y, 1 / 3) : (7.787 * y) + (16 / 116);\n\tz = z > 0.008856 ? Math.pow(z, 1 / 3) : (7.787 * z) + (16 / 116);\n\n\tl = (116 * y) - 16;\n\ta = 500 * (x - y);\n\tb = 200 * (y - z);\n\n\treturn [l, a, b];\n};\n\nconvert.lab.xyz = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar x;\n\tvar y;\n\tvar z;\n\n\ty = (l + 16) / 116;\n\tx = a / 500 + y;\n\tz = y - b / 200;\n\n\tvar y2 = Math.pow(y, 3);\n\tvar x2 = Math.pow(x, 3);\n\tvar z2 = Math.pow(z, 3);\n\ty = y2 > 0.008856 ? y2 : (y - 16 / 116) / 7.787;\n\tx = x2 > 0.008856 ? x2 : (x - 16 / 116) / 7.787;\n\tz = z2 > 0.008856 ? z2 : (z - 16 / 116) / 7.787;\n\n\tx *= 95.047;\n\ty *= 100;\n\tz *= 108.883;\n\n\treturn [x, y, z];\n};\n\nconvert.lab.lch = function (lab) {\n\tvar l = lab[0];\n\tvar a = lab[1];\n\tvar b = lab[2];\n\tvar hr;\n\tvar h;\n\tvar c;\n\n\thr = Math.atan2(b, a);\n\th = hr * 360 / 2 / Math.PI;\n\n\tif (h < 0) {\n\t\th += 360;\n\t}\n\n\tc = Math.sqrt(a * a + b * b);\n\n\treturn [l, c, h];\n};\n\nconvert.lch.lab = function (lch) {\n\tvar l = lch[0];\n\tvar c = lch[1];\n\tvar h = lch[2];\n\tvar a;\n\tvar b;\n\tvar hr;\n\n\thr = h / 360 * 2 * Math.PI;\n\ta = c * Math.cos(hr);\n\tb = c * Math.sin(hr);\n\n\treturn [l, a, b];\n};\n\nconvert.rgb.ansi16 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\tvar value = 1 in arguments ? arguments[1] : convert.rgb.hsv(args)[2]; // hsv -> ansi16 optimization\n\n\tvalue = Math.round(value / 50);\n\n\tif (value === 0) {\n\t\treturn 30;\n\t}\n\n\tvar ansi = 30\n\t\t+ ((Math.round(b / 255) << 2)\n\t\t| (Math.round(g / 255) << 1)\n\t\t| Math.round(r / 255));\n\n\tif (value === 2) {\n\t\tansi += 60;\n\t}\n\n\treturn ansi;\n};\n\nconvert.hsv.ansi16 = function (args) {\n\t// optimization here; we already know the value and don't need to get\n\t// it converted for us.\n\treturn convert.rgb.ansi16(convert.hsv.rgb(args), args[2]);\n};\n\nconvert.rgb.ansi256 = function (args) {\n\tvar r = args[0];\n\tvar g = args[1];\n\tvar b = args[2];\n\n\t// we use the extended greyscale palette here, with the exception of\n\t// black and white. normal palette only has 4 greyscale shades.\n\tif (r === g && g === b) {\n\t\tif (r < 8) {\n\t\t\treturn 16;\n\t\t}\n\n\t\tif (r > 248) {\n\t\t\treturn 231;\n\t\t}\n\n\t\treturn Math.round(((r - 8) / 247) * 24) + 232;\n\t}\n\n\tvar ansi = 16\n\t\t+ (36 * Math.round(r / 255 * 5))\n\t\t+ (6 * Math.round(g / 255 * 5))\n\t\t+ Math.round(b / 255 * 5);\n\n\treturn ansi;\n};\n\nconvert.ansi16.rgb = function (args) {\n\tvar color = args % 10;\n\n\t// handle greyscale\n\tif (color === 0 || color === 7) {\n\t\tif (args > 50) {\n\t\t\tcolor += 3.5;\n\t\t}\n\n\t\tcolor = color / 10.5 * 255;\n\n\t\treturn [color, color, color];\n\t}\n\n\tvar mult = (~~(args > 50) + 1) * 0.5;\n\tvar r = ((color & 1) * mult) * 255;\n\tvar g = (((color >> 1) & 1) * mult) * 255;\n\tvar b = (((color >> 2) & 1) * mult) * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.ansi256.rgb = function (args) {\n\t// handle greyscale\n\tif (args >= 232) {\n\t\tvar c = (args - 232) * 10 + 8;\n\t\treturn [c, c, c];\n\t}\n\n\targs -= 16;\n\n\tvar rem;\n\tvar r = Math.floor(args / 36) / 5 * 255;\n\tvar g = Math.floor((rem = args % 36) / 6) / 5 * 255;\n\tvar b = (rem % 6) / 5 * 255;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hex = function (args) {\n\tvar integer = ((Math.round(args[0]) & 0xFF) << 16)\n\t\t+ ((Math.round(args[1]) & 0xFF) << 8)\n\t\t+ (Math.round(args[2]) & 0xFF);\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.hex.rgb = function (args) {\n\tvar match = args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);\n\tif (!match) {\n\t\treturn [0, 0, 0];\n\t}\n\n\tvar colorString = match[0];\n\n\tif (match[0].length === 3) {\n\t\tcolorString = colorString.split('').map(function (char) {\n\t\t\treturn char + char;\n\t\t}).join('');\n\t}\n\n\tvar integer = parseInt(colorString, 16);\n\tvar r = (integer >> 16) & 0xFF;\n\tvar g = (integer >> 8) & 0xFF;\n\tvar b = integer & 0xFF;\n\n\treturn [r, g, b];\n};\n\nconvert.rgb.hcg = function (rgb) {\n\tvar r = rgb[0] / 255;\n\tvar g = rgb[1] / 255;\n\tvar b = rgb[2] / 255;\n\tvar max = Math.max(Math.max(r, g), b);\n\tvar min = Math.min(Math.min(r, g), b);\n\tvar chroma = (max - min);\n\tvar grayscale;\n\tvar hue;\n\n\tif (chroma < 1) {\n\t\tgrayscale = min / (1 - chroma);\n\t} else {\n\t\tgrayscale = 0;\n\t}\n\n\tif (chroma <= 0) {\n\t\thue = 0;\n\t} else\n\tif (max === r) {\n\t\thue = ((g - b) / chroma) % 6;\n\t} else\n\tif (max === g) {\n\t\thue = 2 + (b - r) / chroma;\n\t} else {\n\t\thue = 4 + (r - g) / chroma + 4;\n\t}\n\n\thue /= 6;\n\thue %= 1;\n\n\treturn [hue * 360, chroma * 100, grayscale * 100];\n};\n\nconvert.hsl.hcg = function (hsl) {\n\tvar s = hsl[1] / 100;\n\tvar l = hsl[2] / 100;\n\tvar c = 1;\n\tvar f = 0;\n\n\tif (l < 0.5) {\n\t\tc = 2.0 * s * l;\n\t} else {\n\t\tc = 2.0 * s * (1.0 - l);\n\t}\n\n\tif (c < 1.0) {\n\t\tf = (l - 0.5 * c) / (1.0 - c);\n\t}\n\n\treturn [hsl[0], c * 100, f * 100];\n};\n\nconvert.hsv.hcg = function (hsv) {\n\tvar s = hsv[1] / 100;\n\tvar v = hsv[2] / 100;\n\n\tvar c = s * v;\n\tvar f = 0;\n\n\tif (c < 1.0) {\n\t\tf = (v - c) / (1 - c);\n\t}\n\n\treturn [hsv[0], c * 100, f * 100];\n};\n\nconvert.hcg.rgb = function (hcg) {\n\tvar h = hcg[0] / 360;\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tif (c === 0.0) {\n\t\treturn [g * 255, g * 255, g * 255];\n\t}\n\n\tvar pure = [0, 0, 0];\n\tvar hi = (h % 1) * 6;\n\tvar v = hi % 1;\n\tvar w = 1 - v;\n\tvar mg = 0;\n\n\tswitch (Math.floor(hi)) {\n\t\tcase 0:\n\t\t\tpure[0] = 1; pure[1] = v; pure[2] = 0; break;\n\t\tcase 1:\n\t\t\tpure[0] = w; pure[1] = 1; pure[2] = 0; break;\n\t\tcase 2:\n\t\t\tpure[0] = 0; pure[1] = 1; pure[2] = v; break;\n\t\tcase 3:\n\t\t\tpure[0] = 0; pure[1] = w; pure[2] = 1; break;\n\t\tcase 4:\n\t\t\tpure[0] = v; pure[1] = 0; pure[2] = 1; break;\n\t\tdefault:\n\t\t\tpure[0] = 1; pure[1] = 0; pure[2] = w;\n\t}\n\n\tmg = (1.0 - c) * g;\n\n\treturn [\n\t\t(c * pure[0] + mg) * 255,\n\t\t(c * pure[1] + mg) * 255,\n\t\t(c * pure[2] + mg) * 255\n\t];\n};\n\nconvert.hcg.hsv = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar v = c + g * (1.0 - c);\n\tvar f = 0;\n\n\tif (v > 0.0) {\n\t\tf = c / v;\n\t}\n\n\treturn [hcg[0], f * 100, v * 100];\n};\n\nconvert.hcg.hsl = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\n\tvar l = g * (1.0 - c) + 0.5 * c;\n\tvar s = 0;\n\n\tif (l > 0.0 && l < 0.5) {\n\t\ts = c / (2 * l);\n\t} else\n\tif (l >= 0.5 && l < 1.0) {\n\t\ts = c / (2 * (1 - l));\n\t}\n\n\treturn [hcg[0], s * 100, l * 100];\n};\n\nconvert.hcg.hwb = function (hcg) {\n\tvar c = hcg[1] / 100;\n\tvar g = hcg[2] / 100;\n\tvar v = c + g * (1.0 - c);\n\treturn [hcg[0], (v - c) * 100, (1 - v) * 100];\n};\n\nconvert.hwb.hcg = function (hwb) {\n\tvar w = hwb[1] / 100;\n\tvar b = hwb[2] / 100;\n\tvar v = 1 - b;\n\tvar c = v - w;\n\tvar g = 0;\n\n\tif (c < 1) {\n\t\tg = (v - c) / (1 - c);\n\t}\n\n\treturn [hwb[0], c * 100, g * 100];\n};\n\nconvert.apple.rgb = function (apple) {\n\treturn [(apple[0] / 65535) * 255, (apple[1] / 65535) * 255, (apple[2] / 65535) * 255];\n};\n\nconvert.rgb.apple = function (rgb) {\n\treturn [(rgb[0] / 255) * 65535, (rgb[1] / 255) * 65535, (rgb[2] / 255) * 65535];\n};\n\nconvert.gray.rgb = function (args) {\n\treturn [args[0] / 100 * 255, args[0] / 100 * 255, args[0] / 100 * 255];\n};\n\nconvert.gray.hsl = convert.gray.hsv = function (args) {\n\treturn [0, 0, args[0]];\n};\n\nconvert.gray.hwb = function (gray) {\n\treturn [0, 100, gray[0]];\n};\n\nconvert.gray.cmyk = function (gray) {\n\treturn [0, 0, 0, gray[0]];\n};\n\nconvert.gray.lab = function (gray) {\n\treturn [gray[0], 0, 0];\n};\n\nconvert.gray.hex = function (gray) {\n\tvar val = Math.round(gray[0] / 100 * 255) & 0xFF;\n\tvar integer = (val << 16) + (val << 8) + val;\n\n\tvar string = integer.toString(16).toUpperCase();\n\treturn '000000'.substring(string.length) + string;\n};\n\nconvert.rgb.gray = function (rgb) {\n\tvar val = (rgb[0] + rgb[1] + rgb[2]) / 3;\n\treturn [val / 255 * 100];\n};\n", "'use strict';\n\nimport Colorpicker from './Colorpicker';\nimport $ from 'jquery';\n\nlet plugin = 'colorpicker';\n\n$[plugin] = Colorpicker;\n\n// Colorpicker jQuery Plugin API\n$.fn[plugin] = function (option) {\n  let fnArgs = Array.prototype.slice.call(arguments, 1),\n    isSingleElement = (this.length === 1),\n    returnValue = null;\n\n  let $elements = this.each(function () {\n    let $this = $(this),\n      inst = $this.data(plugin),\n      options = ((typeof option === 'object') ? option : {});\n\n    // Create instance if does not exist\n    if (!inst) {\n      inst = new Colorpicker(this, options);\n      $this.data(plugin, inst);\n    }\n\n    if (!isSingleElement) {\n      return;\n    }\n\n    returnValue = $this;\n\n    if (typeof option === 'string') {\n      if (option === 'colorpicker') {\n        // Return colorpicker instance: e.g. .colorpicker('colorpicker')\n        returnValue = inst;\n      } else if ($.isFunction(inst[option])) {\n        // Return method call return value: e.g. .colorpicker('isEnabled')\n        returnValue = inst[option].apply(inst, fnArgs);\n      } else {\n        // Return property value: e.g. .colorpicker('element')\n        returnValue = inst[option];\n      }\n    }\n  });\n\n  return isSingleElement ? returnValue : $elements;\n};\n\n$.fn[plugin].constructor = Colorpicker;\n", "'use strict';\n\nimport Extension from './Extension';\nimport defaults from './options';\nimport coreExtensions from 'extensions';\nimport $ from 'jquery';\nimport Slider<PERSON>andler from './SliderHandler';\nimport PopupHandler from './PopupHandler';\nimport InputHandler from './InputHandler';\nimport ColorHandler from './ColorHandler';\nimport PickerHandler from './PickerHandler';\nimport AddonHandler from './AddonHandler';\nimport ColorItem from './ColorItem';\n\nlet colorPickerIdCounter = 0;\n\nlet root = (typeof self !== 'undefined' ? self : this); // window\n\n/**\n * Colorpicker widget class\n */\nclass Colorpicker {\n  /**\n   * Color class\n   *\n   * @static\n   * @type {Color}\n   */\n  static get Color() {\n    return ColorItem;\n  }\n\n  /**\n   * Extension class\n   *\n   * @static\n   * @type {Extension}\n   */\n  static get Extension() {\n    return Extension;\n  }\n\n  /**\n   * Internal color object\n   *\n   * @type {Color|null}\n   */\n  get color() {\n    return this.colorHandler.color;\n  }\n\n  /**\n   * Internal color format\n   *\n   * @type {String|null}\n   */\n  get format() {\n    return this.colorHandler.format;\n  }\n\n  /**\n   * Getter of the picker element\n   *\n   * @returns {jQuery|HTMLElement}\n   */\n  get picker() {\n    return this.pickerHandler.picker;\n  }\n\n  /**\n   * @fires Colorpicker#colorpickerCreate\n   * @param {Object|String} element\n   * @param {Object} options\n   * @constructor\n   */\n  constructor(element, options) {\n    colorPickerIdCounter += 1;\n    /**\n     * The colorpicker instance number\n     * @type {number}\n     */\n    this.id = colorPickerIdCounter;\n\n    /**\n     * Latest colorpicker event\n     *\n     * @type {{name: String, e: *}}\n     */\n    this.lastEvent = {\n      alias: null,\n      e: null\n    };\n\n    /**\n     * The element that the colorpicker is bound to\n     *\n     * @type {*|jQuery}\n     */\n    this.element = $(element)\n      .addClass('colorpicker-element')\n      .attr('data-colorpicker-id', this.id);\n\n    /**\n     * @type {defaults}\n     */\n    this.options = $.extend(true, {}, defaults, options, this.element.data());\n\n    /**\n     * @type {boolean}\n     * @private\n     */\n    this.disabled = false;\n\n    /**\n     * Extensions added to this instance\n     *\n     * @type {Extension[]}\n     */\n    this.extensions = [];\n\n    /**\n     * The element where the\n     * @type {*|jQuery}\n     */\n    this.container = (\n      this.options.container === true ||\n      (this.options.container !== true && this.options.inline === true)\n    ) ? this.element : this.options.container;\n\n    this.container = (this.container !== false) ? $(this.container) : false;\n\n    /**\n     * @type {InputHandler}\n     */\n    this.inputHandler = new InputHandler(this);\n    /**\n     * @type {ColorHandler}\n     */\n    this.colorHandler = new ColorHandler(this);\n    /**\n     * @type {SliderHandler}\n     */\n    this.sliderHandler = new SliderHandler(this);\n    /**\n     * @type {PopupHandler}\n     */\n    this.popupHandler = new PopupHandler(this, root);\n    /**\n     * @type {PickerHandler}\n     */\n    this.pickerHandler = new PickerHandler(this);\n    /**\n     * @type {AddonHandler}\n     */\n    this.addonHandler = new AddonHandler(this);\n\n    this.init();\n\n    // Emit a create event\n    $($.proxy(function () {\n      /**\n       * (Colorpicker) When the Colorpicker instance has been created and the DOM is ready.\n       *\n       * @event Colorpicker#colorpickerCreate\n       */\n      this.trigger('colorpickerCreate');\n    }, this));\n  }\n\n  /**\n   * Initializes the plugin\n   * @private\n   */\n  init() {\n    // Init addon\n    this.addonHandler.bind();\n\n    // Init input\n    this.inputHandler.bind();\n\n    // Init extensions (before initializing the color)\n    this.initExtensions();\n\n    // Init color\n    this.colorHandler.bind();\n\n    // Init picker\n    this.pickerHandler.bind();\n\n    // Init sliders and popup\n    this.sliderHandler.bind();\n    this.popupHandler.bind();\n\n    // Inject into the DOM (this may make it visible)\n    this.pickerHandler.attach();\n\n    // Update all components\n    this.update();\n\n    if (this.inputHandler.isDisabled()) {\n      this.disable();\n    }\n  }\n\n  /**\n   * Initializes the plugin extensions\n   * @private\n   */\n  initExtensions() {\n    if (!Array.isArray(this.options.extensions)) {\n      this.options.extensions = [];\n    }\n\n    if (this.options.debug) {\n      this.options.extensions.push({name: 'debugger'});\n    }\n\n    // Register and instantiate extensions\n    this.options.extensions.forEach((ext) => {\n      this.registerExtension(Colorpicker.extensions[ext.name.toLowerCase()], ext.options || {});\n    });\n  }\n\n  /**\n   * Creates and registers the given extension\n   *\n   * @param {Extension} ExtensionClass The extension class to instantiate\n   * @param {Object} [config] Extension configuration\n   * @returns {Extension}\n   */\n  registerExtension(ExtensionClass, config = {}) {\n    let ext = new ExtensionClass(this, config);\n\n    this.extensions.push(ext);\n    return ext;\n  }\n\n  /**\n   * Destroys the current instance\n   *\n   * @fires Colorpicker#colorpickerDestroy\n   */\n  destroy() {\n    let color = this.color;\n\n    this.sliderHandler.unbind();\n    this.inputHandler.unbind();\n    this.popupHandler.unbind();\n    this.colorHandler.unbind();\n    this.addonHandler.unbind();\n    this.pickerHandler.unbind();\n\n    this.element\n      .removeClass('colorpicker-element')\n      .removeData('colorpicker', 'color')\n      .off('.colorpicker');\n\n    /**\n     * (Colorpicker) When the instance is destroyed with all events unbound.\n     *\n     * @event Colorpicker#colorpickerDestroy\n     */\n    this.trigger('colorpickerDestroy', color);\n  }\n\n  /**\n   * Shows the colorpicker widget if hidden.\n   * If the colorpicker is disabled this call will be ignored.\n   *\n   * @fires Colorpicker#colorpickerShow\n   * @param {Event} [e]\n   */\n  show(e) {\n    this.popupHandler.show(e);\n  }\n\n  /**\n   * Hides the colorpicker widget.\n   *\n   * @fires Colorpicker#colorpickerHide\n   * @param {Event} [e]\n   */\n  hide(e) {\n    this.popupHandler.hide(e);\n  }\n\n  /**\n   * Toggles the colorpicker between visible and hidden.\n   *\n   * @fires Colorpicker#colorpickerShow\n   * @fires Colorpicker#colorpickerHide\n   * @param {Event} [e]\n   */\n  toggle(e) {\n    this.popupHandler.toggle(e);\n  }\n\n  /**\n   * Returns the current color value as string\n   *\n   * @param {String|*} [defaultValue]\n   * @returns {String|*}\n   */\n  getValue(defaultValue = null) {\n    let val = this.colorHandler.color;\n\n    val = (val instanceof ColorItem) ? val : defaultValue;\n\n    if (val instanceof ColorItem) {\n      return val.string(this.format);\n    }\n\n    return val;\n  }\n\n  /**\n   * Sets the color manually\n   *\n   * @fires Colorpicker#colorpickerChange\n   * @param {String|Color} val\n   */\n  setValue(val) {\n    if (this.isDisabled()) {\n      return;\n    }\n    let ch = this.colorHandler;\n\n    if (\n      (ch.hasColor() && !!val && ch.color.equals(val)) ||\n      (!ch.hasColor() && !val)\n    ) {\n      // same color or still empty\n      return;\n    }\n\n    ch.color = val ? ch.createColor(val, this.options.autoInputFallback) : null;\n\n    /**\n     * (Colorpicker) When the color is set programmatically with setValue().\n     *\n     * @event Colorpicker#colorpickerChange\n     */\n    this.trigger('colorpickerChange', ch.color, val);\n\n    // force update if color has changed to empty\n    this.update();\n  }\n\n  /**\n   * Updates the UI and the input color according to the internal color.\n   *\n   * @fires Colorpicker#colorpickerUpdate\n   */\n  update() {\n    if (this.colorHandler.hasColor()) {\n      this.inputHandler.update();\n    } else {\n      this.colorHandler.assureColor();\n    }\n\n    this.addonHandler.update();\n    this.pickerHandler.update();\n\n    /**\n     * (Colorpicker) Fired when the widget is updated.\n     *\n     * @event Colorpicker#colorpickerUpdate\n     */\n    this.trigger('colorpickerUpdate');\n  }\n\n  /**\n   * Enables the widget and the input if any\n   *\n   * @fires Colorpicker#colorpickerEnable\n   * @returns {boolean}\n   */\n  enable() {\n    this.inputHandler.enable();\n    this.disabled = false;\n    this.picker.removeClass('colorpicker-disabled');\n\n    /**\n     * (Colorpicker) When the widget has been enabled.\n     *\n     * @event Colorpicker#colorpickerEnable\n     */\n    this.trigger('colorpickerEnable');\n    return true;\n  }\n\n  /**\n   * Disables the widget and the input if any\n   *\n   * @fires Colorpicker#colorpickerDisable\n   * @returns {boolean}\n   */\n  disable() {\n    this.inputHandler.disable();\n    this.disabled = true;\n    this.picker.addClass('colorpicker-disabled');\n\n    /**\n     * (Colorpicker) When the widget has been disabled.\n     *\n     * @event Colorpicker#colorpickerDisable\n     */\n    this.trigger('colorpickerDisable');\n    return true;\n  }\n\n  /**\n   * Returns true if this instance is enabled\n   * @returns {boolean}\n   */\n  isEnabled() {\n    return !this.isDisabled();\n  }\n\n  /**\n   * Returns true if this instance is disabled\n   * @returns {boolean}\n   */\n  isDisabled() {\n    return this.disabled === true;\n  }\n\n  /**\n   * Triggers a Colorpicker event.\n   *\n   * @param eventName\n   * @param color\n   * @param value\n   */\n  trigger(eventName, color = null, value = null) {\n    this.element.trigger({\n      type: eventName,\n      colorpicker: this,\n      color: color ? color : this.color,\n      value: value ? value : this.getValue()\n    });\n  }\n}\n\n/**\n * Colorpicker extension classes, indexed by extension name\n *\n * @static\n * @type {Object} a map between the extension name and its class\n */\nColorpicker.extensions = coreExtensions;\n\nexport default Colorpicker;\n", "import Debugger from './Debugger';\nimport Preview from './Preview';\nimport Swatches from './Swatches';\nimport Palette from './Palette';\n\nexport {\n  Debugger, Preview, Swatches, Palette\n};\n\nexport default {\n  'debugger': Debugger,\n  'preview': Preview,\n  'swatches': Swatches,\n  'palette': Palette\n};\n", "'use strict';\n\nimport Extension from 'Extension';\nimport $ from 'jquery';\n\n/**\n * Debugger extension class\n * @alias DebuggerExtension\n * @ignore\n */\nclass Debugger extends Extension {\n  constructor(colorpicker, options = {}) {\n    super(colorpicker, options);\n\n    /**\n     * @type {number}\n     */\n    this.eventCounter = 0;\n    if (this.colorpicker.inputHandler.hasInput()) {\n      this.colorpicker.inputHandler.input.on('change.colorpicker-ext', $.proxy(this.onChangeInput, this));\n    }\n  }\n\n  /**\n   * @fires DebuggerExtension#colorpickerDebug\n   * @param {string} eventName\n   * @param {*} args\n   */\n  log(eventName, ...args) {\n    this.eventCounter += 1;\n\n    let logMessage = `#${this.eventCounter}: Colorpicker#${this.colorpicker.id} [${eventName}]`;\n\n    console.debug(logMessage, ...args);\n\n    /**\n     * Whenever the debugger logs an event, this other event is emitted.\n     *\n     * @event DebuggerExtension#colorpickerDebug\n     * @type {object} The event object\n     * @property {Colorpicker} colorpicker The Colorpicker instance\n     * @property {ColorItem} color The color instance\n     * @property {{debugger: DebuggerExtension, eventName: String, logArgs: Array, logMessage: String}} debug\n     *  The debug info\n     */\n    this.colorpicker.element.trigger({\n      type: 'colorpickerDebug',\n      colorpicker: this.colorpicker,\n      color: this.color,\n      value: null,\n      debug: {\n        debugger: this,\n        eventName: eventName,\n        logArgs: args,\n        logMessage: logMessage\n      }\n    });\n  }\n\n  resolveColor(color, realColor = true) {\n    this.log('resolveColor()', color, realColor);\n    return false;\n  }\n\n  onCreate(event) {\n    this.log('colorpickerCreate');\n    return super.onCreate(event);\n  }\n\n  onDestroy(event) {\n    this.log('colorpickerDestroy');\n    this.eventCounter = 0;\n\n    if (this.colorpicker.inputHandler.hasInput()) {\n      this.colorpicker.inputHandler.input.off('.colorpicker-ext');\n    }\n\n    return super.onDestroy(event);\n  }\n\n  onUpdate(event) {\n    this.log('colorpickerUpdate');\n  }\n\n  /**\n   * @listens Colorpicker#change\n   * @param {Event} event\n   */\n  onChangeInput(event) {\n    this.log('input:change.colorpicker', event.value, event.color);\n  }\n\n  onChange(event) {\n    this.log('colorpickerChange', event.value, event.color);\n  }\n\n  onInvalid(event) {\n    this.log('colorpickerInvalid', event.value, event.color);\n  }\n\n  onHide(event) {\n    this.log('colorpickerHide');\n    this.eventCounter = 0;\n  }\n\n  onShow(event) {\n    this.log('colorpickerShow');\n  }\n\n  onDisable(event) {\n    this.log('colorpickerDisable');\n  }\n\n  onEnable(event) {\n    this.log('colorpickerEnable');\n  }\n}\n\nexport default Debugger;\n", "'use strict';\n\nimport Extension from 'Extension';\nimport $ from 'jquery';\n\n/**\n * Color preview extension\n * @ignore\n */\nclass Preview extends Extension {\n  constructor(colorpicker, options = {}) {\n    super(colorpicker, $.extend(true, {},\n      {\n        template: '<div class=\"colorpicker-bar colorpicker-preview\"><div /></div>',\n        showText: true,\n        format: colorpicker.format\n      },\n      options\n    ));\n\n    this.element = $(this.options.template);\n    this.elementInner = this.element.find('div');\n  }\n\n  onCreate(event) {\n    super.onCreate(event);\n    this.colorpicker.picker.append(this.element);\n  }\n\n  onUpdate(event) {\n    super.onUpdate(event);\n\n    if (!event.color) {\n      this.elementInner\n        .css('backgroundColor', null)\n        .css('color', null)\n        .html('');\n      return;\n    }\n\n    this.elementInner\n      .css('backgroundColor', event.color.toRgbString());\n\n    if (this.options.showText) {\n      this.elementInner\n        .html(event.color.string(this.options.format || this.colorpicker.format));\n\n      if (event.color.isDark() && (event.color.alpha > 0.5)) {\n        this.elementInner.css('color', 'white');\n      } else {\n        this.elementInner.css('color', 'black');\n      }\n    }\n  }\n}\n\nexport default Preview;\n", "'use strict';\n\nimport Palette from './Palette';\nimport $ from 'jquery';\n\nlet defaults = {\n  barTemplate: `<div class=\"colorpicker-bar colorpicker-swatches\">\n                    <div class=\"colorpicker-swatches--inner\"></div>\n                </div>`,\n  swatchTemplate: '<i class=\"colorpicker-swatch\"><i class=\"colorpicker-swatch--inner\"></i></i>'\n};\n\n/**\n * Color swatches extension\n * @ignore\n */\nclass Swatches extends Palette {\n  constructor(colorpicker, options = {}) {\n    super(colorpicker, $.extend(true, {}, defaults, options));\n    this.element = null;\n  }\n\n  isEnabled() {\n    return this.getLength() > 0;\n  }\n\n  onCreate(event) {\n    super.onCreate(event);\n\n    if (!this.isEnabled()) {\n      return;\n    }\n\n    this.element = $(this.options.barTemplate);\n    this.load();\n    this.colorpicker.picker.append(this.element);\n  }\n\n  load() {\n    let colorpicker = this.colorpicker,\n      swatchContainer = this.element.find('.colorpicker-swatches--inner'),\n      isAliased = (this.options.namesAsValues === true) && !Array.isArray(this.colors);\n\n    swatchContainer.empty();\n\n    $.each(this.colors, (name, value) => {\n      let $swatch = $(this.options.swatchTemplate)\n        .attr('data-name', name)\n        .attr('data-value', value)\n        .attr('title', isAliased ? `${name}: ${value}` : value)\n        .on('mousedown.colorpicker touchstart.colorpicker',\n          function (e) {\n            let $sw = $(this);\n\n            // e.preventDefault();\n\n            colorpicker.setValue(isAliased ? $sw.attr('data-name') : $sw.attr('data-value'));\n          }\n        );\n\n      $swatch.find('.colorpicker-swatch--inner')\n        .css('background-color', value);\n\n      swatchContainer.append($swatch);\n    });\n\n    swatchContainer.append($('<i class=\"colorpicker-clear\"></i>'));\n  }\n}\n\nexport default Swatches;\n", "'use strict';\n\nimport $ from 'jquery';\n\n/**\n * Class that handles all configured sliders on mouse or touch events.\n * @ignore\n */\nclass SliderHandler {\n  /**\n   * @param {Colorpicker} colorpicker\n   */\n  constructor(colorpicker) {\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * @type {*|String}\n     * @private\n     */\n    this.currentSlider = null;\n    /**\n     * @type {{left: number, top: number}}\n     * @private\n     */\n    this.mousePointer = {\n      left: 0,\n      top: 0\n    };\n\n    /**\n     * @type {Function}\n     */\n    this.onMove = $.proxy(this.defaultOnMove, this);\n  }\n\n  /**\n   * This function is called every time a slider guide is moved\n   * The scope of \"this\" is the SliderHandler object.\n   *\n   * @param {int} top\n   * @param {int} left\n   */\n  defaultOnMove(top, left) {\n    if (!this.currentSlider) {\n      return;\n    }\n\n    let slider = this.currentSlider, cp = this.colorpicker, ch = cp.colorHandler;\n\n    // Create a color object\n    let color = !ch.hasColor() ? ch.getFallbackColor() : ch.color.getClone();\n\n    // Adjust the guide position\n    slider.guideStyle.left = left + 'px';\n    slider.guideStyle.top = top + 'px';\n\n    // Adjust the color\n    if (slider.callLeft) {\n      color[slider.callLeft](left / slider.maxLeft);\n    }\n    if (slider.callTop) {\n      color[slider.callTop](top / slider.maxTop);\n    }\n\n    // Set the new color\n    cp.setValue(color);\n    cp.popupHandler.focus();\n  }\n\n  /**\n   * Binds the colorpicker sliders to the mouse/touch events\n   */\n  bind() {\n    let sliders = this.colorpicker.options.horizontal ? this.colorpicker\n      .options.slidersHorz : this.colorpicker.options.sliders;\n\n    let sliderClasses = [];\n\n    for (let sliderName in sliders) {\n      if (!sliders.hasOwnProperty(sliderName)) {\n        continue;\n      }\n\n      sliderClasses.push(sliders[sliderName].selector);\n    }\n\n    this.colorpicker.picker.find(sliderClasses.join(', '))\n      .on('mousedown.colorpicker touchstart.colorpicker', $.proxy(this.pressed, this));\n  }\n\n  /**\n   * Unbinds any event bound by this handler\n   */\n  unbind() {\n    $(this.colorpicker.picker).off({\n      'mousemove.colorpicker': $.proxy(this.moved, this),\n      'touchmove.colorpicker': $.proxy(this.moved, this),\n      'mouseup.colorpicker': $.proxy(this.released, this),\n      'touchend.colorpicker': $.proxy(this.released, this)\n    });\n  }\n\n  /**\n   * Function triggered when clicking in one of the color adjustment bars\n   *\n   * @private\n   * @fires Colorpicker#mousemove\n   * @param {Event} e\n   */\n  pressed(e) {\n    if (this.colorpicker.isDisabled()) {\n      return;\n    }\n    this.colorpicker.lastEvent.alias = 'pressed';\n    this.colorpicker.lastEvent.e = e;\n\n    if (!e.pageX && !e.pageY && e.originalEvent && e.originalEvent.touches) {\n      e.pageX = e.originalEvent.touches[0].pageX;\n      e.pageY = e.originalEvent.touches[0].pageY;\n    }\n    // e.stopPropagation();\n    // e.preventDefault();\n\n    let target = $(e.target);\n\n    // detect the slider and set the limits and callbacks\n    let zone = target.closest('div');\n\n    let sliders = this.colorpicker.options.horizontal ? this.colorpicker\n      .options.slidersHorz : this.colorpicker.options.sliders;\n\n    if (zone.is('.colorpicker')) {\n      return;\n    }\n\n    this.currentSlider = null;\n\n    for (let sliderName in sliders) {\n      if (!sliders.hasOwnProperty(sliderName)) {\n        continue;\n      }\n\n      let slider = sliders[sliderName];\n\n      if (zone.is(slider.selector)) {\n        this.currentSlider = $.extend({}, slider, {name: sliderName});\n        break;\n      } else if (slider.childSelector !== undefined && zone.is(slider.childSelector)) {\n        this.currentSlider = $.extend({}, slider, {name: sliderName});\n        zone = zone.parent(); // zone.parents(slider.selector).first() ?\n        break;\n      }\n    }\n\n    let guide = zone.find('.colorpicker-guide').get(0);\n\n    if (this.currentSlider === null || guide === null) {\n      return;\n    }\n\n    let offset = zone.offset();\n\n    // reference to guide's style\n    this.currentSlider.guideStyle = guide.style;\n    this.currentSlider.left = e.pageX - offset.left;\n    this.currentSlider.top = e.pageY - offset.top;\n    this.mousePointer = {\n      left: e.pageX,\n      top: e.pageY\n    };\n\n    // TODO: fix moving outside the picker makes the guides to keep moving. The event needs to be bound to the window.\n    /**\n     * (window.document) Triggered on mousedown for the document object,\n     * so the color adjustment guide is moved to the clicked position.\n     *\n     * @event Colorpicker#mousemove\n     */\n    $(this.colorpicker.picker).on({\n      'mousemove.colorpicker': $.proxy(this.moved, this),\n      'touchmove.colorpicker': $.proxy(this.moved, this),\n      'mouseup.colorpicker': $.proxy(this.released, this),\n      'touchend.colorpicker': $.proxy(this.released, this)\n    }).trigger('mousemove');\n  }\n\n  /**\n   * Function triggered when dragging a guide inside one of the color adjustment bars.\n   *\n   * @private\n   * @param {Event} e\n   */\n  moved(e) {\n    this.colorpicker.lastEvent.alias = 'moved';\n    this.colorpicker.lastEvent.e = e;\n\n    if (!e.pageX && !e.pageY && e.originalEvent && e.originalEvent.touches) {\n      e.pageX = e.originalEvent.touches[0].pageX;\n      e.pageY = e.originalEvent.touches[0].pageY;\n    }\n\n    // e.stopPropagation();\n    e.preventDefault(); // prevents scrolling on mobile\n\n    let left = Math.max(\n      0,\n      Math.min(\n        this.currentSlider.maxLeft,\n        this.currentSlider.left + ((e.pageX || this.mousePointer.left) - this.mousePointer.left)\n      )\n    );\n\n    let top = Math.max(\n      0,\n      Math.min(\n        this.currentSlider.maxTop,\n        this.currentSlider.top + ((e.pageY || this.mousePointer.top) - this.mousePointer.top)\n      )\n    );\n\n    this.onMove(top, left);\n  }\n\n  /**\n   * Function triggered when releasing the click in one of the color adjustment bars.\n   *\n   * @private\n   * @param {Event} e\n   */\n  released(e) {\n    this.colorpicker.lastEvent.alias = 'released';\n    this.colorpicker.lastEvent.e = e;\n\n    // e.stopPropagation();\n    // e.preventDefault();\n\n    $(this.colorpicker.picker).off({\n      'mousemove.colorpicker': this.moved,\n      'touchmove.colorpicker': this.moved,\n      'mouseup.colorpicker': this.released,\n      'touchend.colorpicker': this.released\n    });\n  }\n}\n\nexport default SliderHandler;\n", "'use strict';\n\nimport $ from 'jquery';\nimport _defaults from './options';\n\n/**\n * Handles everything related to the UI of the colorpicker popup: show, hide, position,...\n * @ignore\n */\nclass PopupHandler {\n  /**\n   * @param {Colorpicker} colorpicker\n   * @param {Window} root\n   */\n  constructor(colorpicker, root) {\n    /**\n     * @type {Window}\n     */\n    this.root = root;\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * @type {jQuery}\n     */\n    this.popoverTarget = null;\n    /**\n     * @type {jQuery}\n     */\n    this.popoverTip = null;\n\n    /**\n     * If true, the latest click was inside the popover\n     * @type {boolean}\n     */\n    this.clicking = false;\n    /**\n     * @type {boolean}\n     */\n    this.hidding = false;\n    /**\n     * @type {boolean}\n     */\n    this.showing = false;\n  }\n\n  /**\n   * @private\n   * @returns {jQuery|false}\n   */\n  get input() {\n    return this.colorpicker.inputHandler.input;\n  }\n\n  /**\n   * @private\n   * @returns {boolean}\n   */\n  get hasInput() {\n    return this.colorpicker.inputHandler.hasInput();\n  }\n\n  /**\n   * @private\n   * @returns {jQuery|false}\n   */\n  get addon() {\n    return this.colorpicker.addonHandler.addon;\n  }\n\n  /**\n   * @private\n   * @returns {boolean}\n   */\n  get hasAddon() {\n    return this.colorpicker.addonHandler.hasAddon();\n  }\n\n  /**\n   * @private\n   * @returns {boolean}\n   */\n  get isPopover() {\n    return !this.colorpicker.options.inline && !!this.popoverTip;\n  }\n\n  /**\n   * Binds the different colorpicker elements to the focus/mouse/touch events so it reacts in order to show or\n   * hide the colorpicker popup accordingly. It also adds the proper classes.\n   */\n  bind() {\n    let cp = this.colorpicker;\n\n    if (cp.options.inline) {\n      cp.picker.addClass('colorpicker-inline colorpicker-visible');\n      return; // no need to bind show/hide events for inline elements\n    }\n\n    cp.picker.addClass('colorpicker-popup colorpicker-hidden');\n\n    // there is no input or addon\n    if (!this.hasInput && !this.hasAddon) {\n      return;\n    }\n\n    // create Bootstrap 4 popover\n    if (cp.options.popover) {\n      this.createPopover();\n    }\n\n    // bind addon show/hide events\n    if (this.hasAddon) {\n      // enable focus on addons\n      if (!this.addon.attr('tabindex')) {\n        this.addon.attr('tabindex', 0);\n      }\n\n      this.addon.on({\n        'mousedown.colorpicker touchstart.colorpicker': $.proxy(this.toggle, this)\n      });\n\n      this.addon.on({\n        'focus.colorpicker': $.proxy(this.show, this)\n      });\n\n      this.addon.on({\n        'focusout.colorpicker': $.proxy(this.hide, this)\n      });\n    }\n\n    // bind input show/hide events\n    if (this.hasInput && !this.hasAddon) {\n      this.input.on({\n        'mousedown.colorpicker touchstart.colorpicker': $.proxy(this.show, this),\n        'focus.colorpicker': $.proxy(this.show, this)\n      });\n\n      this.input.on({\n        'focusout.colorpicker': $.proxy(this.hide, this)\n      });\n    }\n\n    // reposition popup on window resize\n    $(this.root).on('resize.colorpicker', $.proxy(this.reposition, this));\n  }\n\n  /**\n   * Unbinds any event bound by this handler\n   */\n  unbind() {\n    if (this.hasInput) {\n      this.input.off({\n        'mousedown.colorpicker touchstart.colorpicker': $.proxy(this.show, this),\n        'focus.colorpicker': $.proxy(this.show, this)\n      });\n      this.input.off({\n        'focusout.colorpicker': $.proxy(this.hide, this)\n      });\n    }\n\n    if (this.hasAddon) {\n      this.addon.off({\n        'mousedown.colorpicker touchstart.colorpicker': $.proxy(this.toggle, this)\n      });\n      this.addon.off({\n        'focus.colorpicker': $.proxy(this.show, this)\n      });\n      this.addon.off({\n        'focusout.colorpicker': $.proxy(this.hide, this)\n      });\n    }\n\n    if (this.popoverTarget) {\n      this.popoverTarget.popover('dispose');\n    }\n\n    $(this.root).off('resize.colorpicker', $.proxy(this.reposition, this));\n    $(this.root.document).off('mousedown.colorpicker touchstart.colorpicker', $.proxy(this.hide, this));\n    $(this.root.document).off('mousedown.colorpicker touchstart.colorpicker', $.proxy(this.onClickingInside, this));\n  }\n\n  isClickingInside(e) {\n    if (!e) {\n      return false;\n    }\n\n    return (\n      this.isOrIsInside(this.popoverTip, e.currentTarget) ||\n      this.isOrIsInside(this.popoverTip, e.target) ||\n      this.isOrIsInside(this.colorpicker.picker, e.currentTarget) ||\n      this.isOrIsInside(this.colorpicker.picker, e.target)\n    );\n  }\n\n  isOrIsInside(container, element) {\n    if (!container || !element) {\n      return false;\n    }\n\n    element = $(element);\n\n    return (\n      element.is(container) ||\n      container.find(element).length > 0\n    );\n  }\n\n  onClickingInside(e) {\n    this.clicking = this.isClickingInside(e);\n  }\n\n  createPopover() {\n    let cp = this.colorpicker;\n\n    this.popoverTarget = this.hasAddon ? this.addon : this.input;\n\n    cp.picker.addClass('colorpicker-bs-popover-content');\n\n    this.popoverTarget.popover(\n      $.extend(\n        true,\n        {},\n        _defaults.popover,\n        cp.options.popover,\n        {trigger: 'manual', content: cp.picker, html: true}\n      )\n    );\n\n    this.popoverTip = $(this.popoverTarget.popover('getTipElement').data('bs.popover').tip);\n    this.popoverTip.addClass('colorpicker-bs-popover');\n\n    this.popoverTarget.on('shown.bs.popover', $.proxy(this.fireShow, this));\n    this.popoverTarget.on('hidden.bs.popover', $.proxy(this.fireHide, this));\n  }\n\n  /**\n   * If the widget is not inside a container or inline, rearranges its position relative to its element offset.\n   *\n   * @param {Event} [e]\n   * @private\n   */\n  reposition(e) {\n    if (this.popoverTarget && this.isVisible()) {\n      this.popoverTarget.popover('update');\n    }\n  }\n\n  /**\n   * Toggles the colorpicker between visible or hidden\n   *\n   * @fires Colorpicker#colorpickerShow\n   * @fires Colorpicker#colorpickerHide\n   * @param {Event} [e]\n   */\n  toggle(e) {\n    if (this.isVisible()) {\n      this.hide(e);\n    } else {\n      this.show(e);\n    }\n  }\n\n  /**\n   * Shows the colorpicker widget if hidden.\n   *\n   * @fires Colorpicker#colorpickerShow\n   * @param {Event} [e]\n   */\n  show(e) {\n    if (this.isVisible() || this.showing || this.hidding) {\n      return;\n    }\n\n    this.showing = true;\n    this.hidding = false;\n    this.clicking = false;\n\n    let cp = this.colorpicker;\n\n    cp.lastEvent.alias = 'show';\n    cp.lastEvent.e = e;\n\n    // Prevent showing browser native HTML5 colorpicker\n    if (\n      (e && (!this.hasInput || this.input.attr('type') === 'color')) &&\n      (e && e.preventDefault)\n    ) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n\n    // If it's a popover, add event to the document to hide the picker when clicking outside of it\n    if (this.isPopover) {\n      $(this.root).on('resize.colorpicker', $.proxy(this.reposition, this));\n    }\n\n    // add visible class before popover is shown\n    cp.picker.addClass('colorpicker-visible').removeClass('colorpicker-hidden');\n\n    if (this.popoverTarget) {\n      this.popoverTarget.popover('show');\n    } else {\n      this.fireShow();\n    }\n  }\n\n  fireShow() {\n    this.hidding = false;\n    this.showing = false;\n\n    if (this.isPopover) {\n      // Add event to hide on outside click\n      $(this.root.document).on('mousedown.colorpicker touchstart.colorpicker', $.proxy(this.hide, this));\n      $(this.root.document).on('mousedown.colorpicker touchstart.colorpicker', $.proxy(this.onClickingInside, this));\n    }\n\n    /**\n     * (Colorpicker) When show() is called and the widget can be shown.\n     *\n     * @event Colorpicker#colorpickerShow\n     */\n    this.colorpicker.trigger('colorpickerShow');\n  }\n\n  /**\n   * Hides the colorpicker widget.\n   * Hide is prevented when it is triggered by an event whose target element has been clicked/touched.\n   *\n   * @fires Colorpicker#colorpickerHide\n   * @param {Event} [e]\n   */\n  hide(e) {\n    if (this.isHidden() || this.showing || this.hidding) {\n      return;\n    }\n\n    let cp = this.colorpicker, clicking = (this.clicking || this.isClickingInside(e));\n\n    this.hidding = true;\n    this.showing = false;\n    this.clicking = false;\n\n    cp.lastEvent.alias = 'hide';\n    cp.lastEvent.e = e;\n\n    // TODO: fix having to click twice outside when losing focus and last 2 clicks where inside the colorpicker\n\n    // Prevent hide if triggered by an event and an element inside the colorpicker has been clicked/touched\n    if (clicking) {\n      this.hidding = false;\n      return;\n    }\n\n    if (this.popoverTarget) {\n      this.popoverTarget.popover('hide');\n    } else {\n      this.fireHide();\n    }\n  }\n\n  fireHide() {\n    this.hidding = false;\n    this.showing = false;\n\n    let cp = this.colorpicker;\n\n    // add hidden class after popover is hidden\n    cp.picker.addClass('colorpicker-hidden').removeClass('colorpicker-visible');\n\n    // Unbind window and document events, since there is no need to keep them while the popup is hidden\n    $(this.root).off('resize.colorpicker', $.proxy(this.reposition, this));\n    $(this.root.document).off('mousedown.colorpicker touchstart.colorpicker', $.proxy(this.hide, this));\n    $(this.root.document).off('mousedown.colorpicker touchstart.colorpicker', $.proxy(this.onClickingInside, this));\n\n    /**\n     * (Colorpicker) When hide() is called and the widget can be hidden.\n     *\n     * @event Colorpicker#colorpickerHide\n     */\n    cp.trigger('colorpickerHide');\n  }\n\n  focus() {\n    if (this.hasAddon) {\n      return this.addon.focus();\n    }\n    if (this.hasInput) {\n      return this.input.focus();\n    }\n    return false;\n  }\n\n  /**\n   * Returns true if the colorpicker element has the colorpicker-visible class and not the colorpicker-hidden one.\n   * False otherwise.\n   *\n   * @returns {boolean}\n   */\n  isVisible() {\n    return this.colorpicker.picker.hasClass('colorpicker-visible') &&\n      !this.colorpicker.picker.hasClass('colorpicker-hidden');\n  }\n\n  /**\n   * Returns true if the colorpicker element has the colorpicker-hidden class and not the colorpicker-visible one.\n   * False otherwise.\n   *\n   * @returns {boolean}\n   */\n  isHidden() {\n    return this.colorpicker.picker.hasClass('colorpicker-hidden') &&\n      !this.colorpicker.picker.hasClass('colorpicker-visible');\n  }\n}\n\nexport default PopupHandler;\n", "'use strict';\n\nimport $ from 'jquery';\nimport ColorItem from './ColorItem';\n\n/**\n * Handles everything related to the colorpicker input\n * @ignore\n */\nclass InputHandler {\n  /**\n   * @param {Colorpicker} colorpicker\n   */\n  constructor(colorpicker) {\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * @type {jQuery|false}\n     */\n    this.input = this.colorpicker.element.is('input') ? this.colorpicker.element : (this.colorpicker.options.input ?\n      this.colorpicker.element.find(this.colorpicker.options.input) : false);\n\n    if (this.input && (this.input.length === 0)) {\n      this.input = false;\n    }\n\n    this._initValue();\n  }\n\n  bind() {\n    if (!this.hasInput()) {\n      return;\n    }\n    this.input.on({\n      'keyup.colorpicker': $.proxy(this.onkeyup, this)\n    });\n    this.input.on({\n      'change.colorpicker': $.proxy(this.onchange, this)\n    });\n  }\n\n  unbind() {\n    if (!this.hasInput()) {\n      return;\n    }\n    this.input.off('.colorpicker');\n  }\n\n  _initValue() {\n    if (!this.hasInput()) {\n      return;\n    }\n\n    let val = '';\n\n    [\n      // candidates:\n      this.input.val(),\n      this.input.data('color'),\n      this.input.attr('data-color')\n    ].map((item) => {\n      if (item && (val === '')) {\n        val = item;\n      }\n    });\n\n    if (val instanceof ColorItem) {\n      val = this.getFormattedColor(val.string(this.colorpicker.format));\n    } else if (!(typeof val === 'string' || val instanceof String)) {\n      val = '';\n    }\n\n    this.input.prop('value', val);\n  }\n\n  /**\n   * Returns the color string from the input value.\n   * If there is no input the return value is false.\n   *\n   * @returns {String|boolean}\n   */\n  getValue() {\n    if (!this.hasInput()) {\n      return false;\n    }\n\n    return this.input.val();\n  }\n\n  /**\n   * If the input element is present, it updates the value with the current color object color string.\n   * If the value is changed, this method fires a \"change\" event on the input element.\n   *\n   * @param {String} val\n   *\n   * @fires Colorpicker#change\n   */\n  setValue(val) {\n    if (!this.hasInput()) {\n      return;\n    }\n\n    let inputVal = this.input.prop('value');\n\n    val = val ? val : '';\n\n    if (val === (inputVal ? inputVal : '')) {\n      // No need to set value or trigger any event if nothing changed\n      return;\n    }\n\n    this.input.prop('value', val);\n\n    /**\n     * (Input) Triggered on the input element when a new color is selected.\n     *\n     * @event Colorpicker#change\n     */\n    this.input.trigger({\n      type: 'change',\n      colorpicker: this.colorpicker,\n      color: this.colorpicker.color,\n      value: val\n    });\n  }\n\n  /**\n   * Returns the formatted color string, with the formatting options applied\n   * (e.g. useHashPrefix)\n   *\n   * @param {String|null} val\n   *\n   * @returns {String}\n   */\n  getFormattedColor(val = null) {\n    val = val ? val : this.colorpicker.colorHandler.getColorString();\n\n    if (!val) {\n      return '';\n    }\n\n    val = this.colorpicker.colorHandler.resolveColorDelegate(val, false);\n\n    if (this.colorpicker.options.useHashPrefix === false) {\n      val = val.replace(/^#/g, '');\n    }\n\n    return val;\n  }\n\n  /**\n   * Returns true if the widget has an associated input element, false otherwise\n   * @returns {boolean}\n   */\n  hasInput() {\n    return (this.input !== false);\n  }\n\n  /**\n   * Returns true if the input exists and is disabled\n   * @returns {boolean}\n   */\n  isEnabled() {\n    return this.hasInput() && !this.isDisabled();\n  }\n\n  /**\n   * Returns true if the input exists and is disabled\n   * @returns {boolean}\n   */\n  isDisabled() {\n    return this.hasInput() && (this.input.prop('disabled') === true);\n  }\n\n  /**\n   * Disables the input if any\n   *\n   * @fires Colorpicker#colorpickerDisable\n   * @returns {boolean}\n   */\n  disable() {\n    if (this.hasInput()) {\n      this.input.prop('disabled', true);\n    }\n  }\n\n  /**\n   * Enables the input if any\n   *\n   * @fires Colorpicker#colorpickerEnable\n   * @returns {boolean}\n   */\n  enable() {\n    if (this.hasInput()) {\n      this.input.prop('disabled', false);\n    }\n  }\n\n  /**\n   * Calls setValue with the current internal color value\n   *\n   * @fires Colorpicker#change\n   */\n  update() {\n    if (!this.hasInput()) {\n      return;\n    }\n\n    if (\n      (this.colorpicker.options.autoInputFallback === false) &&\n      this.colorpicker.colorHandler.isInvalidColor()\n    ) {\n      // prevent update if color is invalid, autoInputFallback is disabled and the last event is keyup.\n      return;\n    }\n\n    this.setValue(this.getFormattedColor());\n  }\n\n  /**\n   * Function triggered when the input has changed, so the colorpicker gets updated.\n   *\n   * @private\n   * @param {Event} e\n   * @returns {boolean}\n   */\n  onchange(e) {\n    this.colorpicker.lastEvent.alias = 'input.change';\n    this.colorpicker.lastEvent.e = e;\n\n    let val = this.getValue();\n\n    if (val !== e.value) {\n      this.colorpicker.setValue(val);\n    }\n  }\n\n  /**\n   * Function triggered after a keyboard key has been released.\n   *\n   * @private\n   * @param {Event} e\n   * @returns {boolean}\n   */\n  onkeyup(e) {\n    this.colorpicker.lastEvent.alias = 'input.keyup';\n    this.colorpicker.lastEvent.e = e;\n\n    let val = this.getValue();\n\n    if (val !== e.value) {\n      this.colorpicker.setValue(val);\n    }\n  }\n}\n\nexport default InputHandler;\n", "'use strict';\n\nvar colorString = require('color-string');\nvar convert = require('color-convert');\n\nvar _slice = [].slice;\n\nvar skippedModels = [\n\t// to be honest, I don't really feel like keyword belongs in color convert, but eh.\n\t'keyword',\n\n\t// gray conflicts with some method names, and has its own method defined.\n\t'gray',\n\n\t// shouldn't really be in color-convert either...\n\t'hex'\n];\n\nvar hashedModelKeys = {};\nObject.keys(convert).forEach(function (model) {\n\thashedModelKeys[_slice.call(convert[model].labels).sort().join('')] = model;\n});\n\nvar limiters = {};\n\nfunction Color(obj, model) {\n\tif (!(this instanceof Color)) {\n\t\treturn new Color(obj, model);\n\t}\n\n\tif (model && model in skippedModels) {\n\t\tmodel = null;\n\t}\n\n\tif (model && !(model in convert)) {\n\t\tthrow new Error('Unknown model: ' + model);\n\t}\n\n\tvar i;\n\tvar channels;\n\n\tif (obj == null) { // eslint-disable-line no-eq-null,eqeqeq\n\t\tthis.model = 'rgb';\n\t\tthis.color = [0, 0, 0];\n\t\tthis.valpha = 1;\n\t} else if (obj instanceof Color) {\n\t\tthis.model = obj.model;\n\t\tthis.color = obj.color.slice();\n\t\tthis.valpha = obj.valpha;\n\t} else if (typeof obj === 'string') {\n\t\tvar result = colorString.get(obj);\n\t\tif (result === null) {\n\t\t\tthrow new Error('Unable to parse color from string: ' + obj);\n\t\t}\n\n\t\tthis.model = result.model;\n\t\tchannels = convert[this.model].channels;\n\t\tthis.color = result.value.slice(0, channels);\n\t\tthis.valpha = typeof result.value[channels] === 'number' ? result.value[channels] : 1;\n\t} else if (obj.length) {\n\t\tthis.model = model || 'rgb';\n\t\tchannels = convert[this.model].channels;\n\t\tvar newArr = _slice.call(obj, 0, channels);\n\t\tthis.color = zeroArray(newArr, channels);\n\t\tthis.valpha = typeof obj[channels] === 'number' ? obj[channels] : 1;\n\t} else if (typeof obj === 'number') {\n\t\t// this is always RGB - can be converted later on.\n\t\tobj &= 0xFFFFFF;\n\t\tthis.model = 'rgb';\n\t\tthis.color = [\n\t\t\t(obj >> 16) & 0xFF,\n\t\t\t(obj >> 8) & 0xFF,\n\t\t\tobj & 0xFF\n\t\t];\n\t\tthis.valpha = 1;\n\t} else {\n\t\tthis.valpha = 1;\n\n\t\tvar keys = Object.keys(obj);\n\t\tif ('alpha' in obj) {\n\t\t\tkeys.splice(keys.indexOf('alpha'), 1);\n\t\t\tthis.valpha = typeof obj.alpha === 'number' ? obj.alpha : 0;\n\t\t}\n\n\t\tvar hashedKeys = keys.sort().join('');\n\t\tif (!(hashedKeys in hashedModelKeys)) {\n\t\t\tthrow new Error('Unable to parse color from object: ' + JSON.stringify(obj));\n\t\t}\n\n\t\tthis.model = hashedModelKeys[hashedKeys];\n\n\t\tvar labels = convert[this.model].labels;\n\t\tvar color = [];\n\t\tfor (i = 0; i < labels.length; i++) {\n\t\t\tcolor.push(obj[labels[i]]);\n\t\t}\n\n\t\tthis.color = zeroArray(color);\n\t}\n\n\t// perform limitations (clamping, etc.)\n\tif (limiters[this.model]) {\n\t\tchannels = convert[this.model].channels;\n\t\tfor (i = 0; i < channels; i++) {\n\t\t\tvar limit = limiters[this.model][i];\n\t\t\tif (limit) {\n\t\t\t\tthis.color[i] = limit(this.color[i]);\n\t\t\t}\n\t\t}\n\t}\n\n\tthis.valpha = Math.max(0, Math.min(1, this.valpha));\n\n\tif (Object.freeze) {\n\t\tObject.freeze(this);\n\t}\n}\n\nColor.prototype = {\n\ttoString: function () {\n\t\treturn this.string();\n\t},\n\n\ttoJSON: function () {\n\t\treturn this[this.model]();\n\t},\n\n\tstring: function (places) {\n\t\tvar self = this.model in colorString.to ? this : this.rgb();\n\t\tself = self.round(typeof places === 'number' ? places : 1);\n\t\tvar args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n\t\treturn colorString.to[self.model](args);\n\t},\n\n\tpercentString: function (places) {\n\t\tvar self = this.rgb().round(typeof places === 'number' ? places : 1);\n\t\tvar args = self.valpha === 1 ? self.color : self.color.concat(this.valpha);\n\t\treturn colorString.to.rgb.percent(args);\n\t},\n\n\tarray: function () {\n\t\treturn this.valpha === 1 ? this.color.slice() : this.color.concat(this.valpha);\n\t},\n\n\tobject: function () {\n\t\tvar result = {};\n\t\tvar channels = convert[this.model].channels;\n\t\tvar labels = convert[this.model].labels;\n\n\t\tfor (var i = 0; i < channels; i++) {\n\t\t\tresult[labels[i]] = this.color[i];\n\t\t}\n\n\t\tif (this.valpha !== 1) {\n\t\t\tresult.alpha = this.valpha;\n\t\t}\n\n\t\treturn result;\n\t},\n\n\tunitArray: function () {\n\t\tvar rgb = this.rgb().color;\n\t\trgb[0] /= 255;\n\t\trgb[1] /= 255;\n\t\trgb[2] /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.push(this.valpha);\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tunitObject: function () {\n\t\tvar rgb = this.rgb().object();\n\t\trgb.r /= 255;\n\t\trgb.g /= 255;\n\t\trgb.b /= 255;\n\n\t\tif (this.valpha !== 1) {\n\t\t\trgb.alpha = this.valpha;\n\t\t}\n\n\t\treturn rgb;\n\t},\n\n\tround: function (places) {\n\t\tplaces = Math.max(places || 0, 0);\n\t\treturn new Color(this.color.map(roundToPlace(places)).concat(this.valpha), this.model);\n\t},\n\n\talpha: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(this.color.concat(Math.max(0, Math.min(1, val))), this.model);\n\t\t}\n\n\t\treturn this.valpha;\n\t},\n\n\t// rgb\n\tred: getset('rgb', 0, maxfn(255)),\n\tgreen: getset('rgb', 1, maxfn(255)),\n\tblue: getset('rgb', 2, maxfn(255)),\n\n\thue: getset(['hsl', 'hsv', 'hsl', 'hwb', 'hcg'], 0, function (val) { return ((val % 360) + 360) % 360; }), // eslint-disable-line brace-style\n\n\tsaturationl: getset('hsl', 1, maxfn(100)),\n\tlightness: getset('hsl', 2, maxfn(100)),\n\n\tsaturationv: getset('hsv', 1, maxfn(100)),\n\tvalue: getset('hsv', 2, maxfn(100)),\n\n\tchroma: getset('hcg', 1, maxfn(100)),\n\tgray: getset('hcg', 2, maxfn(100)),\n\n\twhite: getset('hwb', 1, maxfn(100)),\n\twblack: getset('hwb', 2, maxfn(100)),\n\n\tcyan: getset('cmyk', 0, maxfn(100)),\n\tmagenta: getset('cmyk', 1, maxfn(100)),\n\tyellow: getset('cmyk', 2, maxfn(100)),\n\tblack: getset('cmyk', 3, maxfn(100)),\n\n\tx: getset('xyz', 0, maxfn(100)),\n\ty: getset('xyz', 1, maxfn(100)),\n\tz: getset('xyz', 2, maxfn(100)),\n\n\tl: getset('lab', 0, maxfn(100)),\n\ta: getset('lab', 1),\n\tb: getset('lab', 2),\n\n\tkeyword: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(val);\n\t\t}\n\n\t\treturn convert[this.model].keyword(this.color);\n\t},\n\n\thex: function (val) {\n\t\tif (arguments.length) {\n\t\t\treturn new Color(val);\n\t\t}\n\n\t\treturn colorString.to.hex(this.rgb().round().color);\n\t},\n\n\trgbNumber: function () {\n\t\tvar rgb = this.rgb().color;\n\t\treturn ((rgb[0] & 0xFF) << 16) | ((rgb[1] & 0xFF) << 8) | (rgb[2] & 0xFF);\n\t},\n\n\tluminosity: function () {\n\t\t// http://www.w3.org/TR/WCAG20/#relativeluminancedef\n\t\tvar rgb = this.rgb().color;\n\n\t\tvar lum = [];\n\t\tfor (var i = 0; i < rgb.length; i++) {\n\t\t\tvar chan = rgb[i] / 255;\n\t\t\tlum[i] = (chan <= 0.03928) ? chan / 12.92 : Math.pow(((chan + 0.055) / 1.055), 2.4);\n\t\t}\n\n\t\treturn 0.2126 * lum[0] + 0.7152 * lum[1] + 0.0722 * lum[2];\n\t},\n\n\tcontrast: function (color2) {\n\t\t// http://www.w3.org/TR/WCAG20/#contrast-ratiodef\n\t\tvar lum1 = this.luminosity();\n\t\tvar lum2 = color2.luminosity();\n\n\t\tif (lum1 > lum2) {\n\t\t\treturn (lum1 + 0.05) / (lum2 + 0.05);\n\t\t}\n\n\t\treturn (lum2 + 0.05) / (lum1 + 0.05);\n\t},\n\n\tlevel: function (color2) {\n\t\tvar contrastRatio = this.contrast(color2);\n\t\tif (contrastRatio >= 7.1) {\n\t\t\treturn 'AAA';\n\t\t}\n\n\t\treturn (contrastRatio >= 4.5) ? 'AA' : '';\n\t},\n\n\tisDark: function () {\n\t\t// YIQ equation from http://24ways.org/2010/calculating-color-contrast\n\t\tvar rgb = this.rgb().color;\n\t\tvar yiq = (rgb[0] * 299 + rgb[1] * 587 + rgb[2] * 114) / 1000;\n\t\treturn yiq < 128;\n\t},\n\n\tisLight: function () {\n\t\treturn !this.isDark();\n\t},\n\n\tnegate: function () {\n\t\tvar rgb = this.rgb();\n\t\tfor (var i = 0; i < 3; i++) {\n\t\t\trgb.color[i] = 255 - rgb.color[i];\n\t\t}\n\t\treturn rgb;\n\t},\n\n\tlighten: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[2] += hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdarken: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[2] -= hsl.color[2] * ratio;\n\t\treturn hsl;\n\t},\n\n\tsaturate: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[1] += hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\tdesaturate: function (ratio) {\n\t\tvar hsl = this.hsl();\n\t\thsl.color[1] -= hsl.color[1] * ratio;\n\t\treturn hsl;\n\t},\n\n\twhiten: function (ratio) {\n\t\tvar hwb = this.hwb();\n\t\thwb.color[1] += hwb.color[1] * ratio;\n\t\treturn hwb;\n\t},\n\n\tblacken: function (ratio) {\n\t\tvar hwb = this.hwb();\n\t\thwb.color[2] += hwb.color[2] * ratio;\n\t\treturn hwb;\n\t},\n\n\tgrayscale: function () {\n\t\t// http://en.wikipedia.org/wiki/Grayscale#Converting_color_to_grayscale\n\t\tvar rgb = this.rgb().color;\n\t\tvar val = rgb[0] * 0.3 + rgb[1] * 0.59 + rgb[2] * 0.11;\n\t\treturn Color.rgb(val, val, val);\n\t},\n\n\tfade: function (ratio) {\n\t\treturn this.alpha(this.valpha - (this.valpha * ratio));\n\t},\n\n\topaquer: function (ratio) {\n\t\treturn this.alpha(this.valpha + (this.valpha * ratio));\n\t},\n\n\trotate: function (degrees) {\n\t\tvar hsl = this.hsl();\n\t\tvar hue = hsl.color[0];\n\t\thue = (hue + degrees) % 360;\n\t\thue = hue < 0 ? 360 + hue : hue;\n\t\thsl.color[0] = hue;\n\t\treturn hsl;\n\t},\n\n\tmix: function (mixinColor, weight) {\n\t\t// ported from sass implementation in C\n\t\t// https://github.com/sass/libsass/blob/0e6b4a2850092356aa3ece07c6b249f0221caced/functions.cpp#L209\n\t\tif (!mixinColor || !mixinColor.rgb) {\n\t\t\tthrow new Error('Argument to \"mix\" was not a Color instance, but rather an instance of ' + typeof mixinColor);\n\t\t}\n\t\tvar color1 = mixinColor.rgb();\n\t\tvar color2 = this.rgb();\n\t\tvar p = weight === undefined ? 0.5 : weight;\n\n\t\tvar w = 2 * p - 1;\n\t\tvar a = color1.alpha() - color2.alpha();\n\n\t\tvar w1 = (((w * a === -1) ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n\t\tvar w2 = 1 - w1;\n\n\t\treturn Color.rgb(\n\t\t\t\tw1 * color1.red() + w2 * color2.red(),\n\t\t\t\tw1 * color1.green() + w2 * color2.green(),\n\t\t\t\tw1 * color1.blue() + w2 * color2.blue(),\n\t\t\t\tcolor1.alpha() * p + color2.alpha() * (1 - p));\n\t}\n};\n\n// model conversion methods and static constructors\nObject.keys(convert).forEach(function (model) {\n\tif (skippedModels.indexOf(model) !== -1) {\n\t\treturn;\n\t}\n\n\tvar channels = convert[model].channels;\n\n\t// conversion methods\n\tColor.prototype[model] = function () {\n\t\tif (this.model === model) {\n\t\t\treturn new Color(this);\n\t\t}\n\n\t\tif (arguments.length) {\n\t\t\treturn new Color(arguments, model);\n\t\t}\n\n\t\tvar newAlpha = typeof arguments[channels] === 'number' ? channels : this.valpha;\n\t\treturn new Color(assertArray(convert[this.model][model].raw(this.color)).concat(newAlpha), model);\n\t};\n\n\t// 'static' construction methods\n\tColor[model] = function (color) {\n\t\tif (typeof color === 'number') {\n\t\t\tcolor = zeroArray(_slice.call(arguments), channels);\n\t\t}\n\t\treturn new Color(color, model);\n\t};\n});\n\nfunction roundTo(num, places) {\n\treturn Number(num.toFixed(places));\n}\n\nfunction roundToPlace(places) {\n\treturn function (num) {\n\t\treturn roundTo(num, places);\n\t};\n}\n\nfunction getset(model, channel, modifier) {\n\tmodel = Array.isArray(model) ? model : [model];\n\n\tmodel.forEach(function (m) {\n\t\t(limiters[m] || (limiters[m] = []))[channel] = modifier;\n\t});\n\n\tmodel = model[0];\n\n\treturn function (val) {\n\t\tvar result;\n\n\t\tif (arguments.length) {\n\t\t\tif (modifier) {\n\t\t\t\tval = modifier(val);\n\t\t\t}\n\n\t\t\tresult = this[model]();\n\t\t\tresult.color[channel] = val;\n\t\t\treturn result;\n\t\t}\n\n\t\tresult = this[model]().color[channel];\n\t\tif (modifier) {\n\t\t\tresult = modifier(result);\n\t\t}\n\n\t\treturn result;\n\t};\n}\n\nfunction maxfn(max) {\n\treturn function (v) {\n\t\treturn Math.max(0, Math.min(max, v));\n\t};\n}\n\nfunction assertArray(val) {\n\treturn Array.isArray(val) ? val : [val];\n}\n\nfunction zeroArray(arr, length) {\n\tfor (var i = 0; i < length; i++) {\n\t\tif (typeof arr[i] !== 'number') {\n\t\t\tarr[i] = 0;\n\t\t}\n\t}\n\n\treturn arr;\n}\n\nmodule.exports = Color;\n", "/* MIT license */\nvar colorNames = require('color-name');\nvar swizzle = require('simple-swizzle');\n\nvar reverseNames = {};\n\n// create a list of reverse color names\nfor (var name in colorNames) {\n\tif (colorNames.hasOwnProperty(name)) {\n\t\treverseNames[colorNames[name]] = name;\n\t}\n}\n\nvar cs = module.exports = {\n\tto: {},\n\tget: {}\n};\n\ncs.get = function (string) {\n\tvar prefix = string.substring(0, 3).toLowerCase();\n\tvar val;\n\tvar model;\n\tswitch (prefix) {\n\t\tcase 'hsl':\n\t\t\tval = cs.get.hsl(string);\n\t\t\tmodel = 'hsl';\n\t\t\tbreak;\n\t\tcase 'hwb':\n\t\t\tval = cs.get.hwb(string);\n\t\t\tmodel = 'hwb';\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tval = cs.get.rgb(string);\n\t\t\tmodel = 'rgb';\n\t\t\tbreak;\n\t}\n\n\tif (!val) {\n\t\treturn null;\n\t}\n\n\treturn {model: model, value: val};\n};\n\ncs.get.rgb = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar abbr = /^#([a-f0-9]{3,4})$/i;\n\tvar hex = /^#([a-f0-9]{6})([a-f0-9]{2})?$/i;\n\tvar rgba = /^rgba?\\(\\s*([+-]?\\d+)\\s*,\\s*([+-]?\\d+)\\s*,\\s*([+-]?\\d+)\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)$/;\n\tvar per = /^rgba?\\(\\s*([+-]?[\\d\\.]+)\\%\\s*,\\s*([+-]?[\\d\\.]+)\\%\\s*,\\s*([+-]?[\\d\\.]+)\\%\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)$/;\n\tvar keyword = /(\\D+)/;\n\n\tvar rgb = [0, 0, 0, 1];\n\tvar match;\n\tvar i;\n\tvar hexAlpha;\n\n\tif (match = string.match(hex)) {\n\t\thexAlpha = match[2];\n\t\tmatch = match[1];\n\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\t// https://jsperf.com/slice-vs-substr-vs-substring-methods-long-string/19\n\t\t\tvar i2 = i * 2;\n\t\t\trgb[i] = parseInt(match.slice(i2, i2 + 2), 16);\n\t\t}\n\n\t\tif (hexAlpha) {\n\t\t\trgb[3] = Math.round((parseInt(hexAlpha, 16) / 255) * 100) / 100;\n\t\t}\n\t} else if (match = string.match(abbr)) {\n\t\tmatch = match[1];\n\t\thexAlpha = match[3];\n\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = parseInt(match[i] + match[i], 16);\n\t\t}\n\n\t\tif (hexAlpha) {\n\t\t\trgb[3] = Math.round((parseInt(hexAlpha + hexAlpha, 16) / 255) * 100) / 100;\n\t\t}\n\t} else if (match = string.match(rgba)) {\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = parseInt(match[i + 1], 0);\n\t\t}\n\n\t\tif (match[4]) {\n\t\t\trgb[3] = parseFloat(match[4]);\n\t\t}\n\t} else if (match = string.match(per)) {\n\t\tfor (i = 0; i < 3; i++) {\n\t\t\trgb[i] = Math.round(parseFloat(match[i + 1]) * 2.55);\n\t\t}\n\n\t\tif (match[4]) {\n\t\t\trgb[3] = parseFloat(match[4]);\n\t\t}\n\t} else if (match = string.match(keyword)) {\n\t\tif (match[1] === 'transparent') {\n\t\t\treturn [0, 0, 0, 0];\n\t\t}\n\n\t\trgb = colorNames[match[1]];\n\n\t\tif (!rgb) {\n\t\t\treturn null;\n\t\t}\n\n\t\trgb[3] = 1;\n\n\t\treturn rgb;\n\t} else {\n\t\treturn null;\n\t}\n\n\tfor (i = 0; i < 3; i++) {\n\t\trgb[i] = clamp(rgb[i], 0, 255);\n\t}\n\trgb[3] = clamp(rgb[3], 0, 1);\n\n\treturn rgb;\n};\n\ncs.get.hsl = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar hsl = /^hsla?\\(\\s*([+-]?(?:\\d*\\.)?\\d+)(?:deg)?\\s*,\\s*([+-]?[\\d\\.]+)%\\s*,\\s*([+-]?[\\d\\.]+)%\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)$/;\n\tvar match = string.match(hsl);\n\n\tif (match) {\n\t\tvar alpha = parseFloat(match[4]);\n\t\tvar h = (parseFloat(match[1]) + 360) % 360;\n\t\tvar s = clamp(parseFloat(match[2]), 0, 100);\n\t\tvar l = clamp(parseFloat(match[3]), 0, 100);\n\t\tvar a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);\n\n\t\treturn [h, s, l, a];\n\t}\n\n\treturn null;\n};\n\ncs.get.hwb = function (string) {\n\tif (!string) {\n\t\treturn null;\n\t}\n\n\tvar hwb = /^hwb\\(\\s*([+-]?\\d*[\\.]?\\d+)(?:deg)?\\s*,\\s*([+-]?[\\d\\.]+)%\\s*,\\s*([+-]?[\\d\\.]+)%\\s*(?:,\\s*([+-]?[\\d\\.]+)\\s*)?\\)$/;\n\tvar match = string.match(hwb);\n\n\tif (match) {\n\t\tvar alpha = parseFloat(match[4]);\n\t\tvar h = ((parseFloat(match[1]) % 360) + 360) % 360;\n\t\tvar w = clamp(parseFloat(match[2]), 0, 100);\n\t\tvar b = clamp(parseFloat(match[3]), 0, 100);\n\t\tvar a = clamp(isNaN(alpha) ? 1 : alpha, 0, 1);\n\t\treturn [h, w, b, a];\n\t}\n\n\treturn null;\n};\n\ncs.to.hex = function () {\n\tvar rgba = swizzle(arguments);\n\n\treturn (\n\t\t'#' +\n\t\thexDouble(rgba[0]) +\n\t\thexDouble(rgba[1]) +\n\t\thexDouble(rgba[2]) +\n\t\t(rgba[3] < 1\n\t\t\t? (hexDouble(Math.round(rgba[3] * 255)))\n\t\t\t: '')\n\t);\n};\n\ncs.to.rgb = function () {\n\tvar rgba = swizzle(arguments);\n\n\treturn rgba.length < 4 || rgba[3] === 1\n\t\t? 'rgb(' + Math.round(rgba[0]) + ', ' + Math.round(rgba[1]) + ', ' + Math.round(rgba[2]) + ')'\n\t\t: 'rgba(' + Math.round(rgba[0]) + ', ' + Math.round(rgba[1]) + ', ' + Math.round(rgba[2]) + ', ' + rgba[3] + ')';\n};\n\ncs.to.rgb.percent = function () {\n\tvar rgba = swizzle(arguments);\n\n\tvar r = Math.round(rgba[0] / 255 * 100);\n\tvar g = Math.round(rgba[1] / 255 * 100);\n\tvar b = Math.round(rgba[2] / 255 * 100);\n\n\treturn rgba.length < 4 || rgba[3] === 1\n\t\t? 'rgb(' + r + '%, ' + g + '%, ' + b + '%)'\n\t\t: 'rgba(' + r + '%, ' + g + '%, ' + b + '%, ' + rgba[3] + ')';\n};\n\ncs.to.hsl = function () {\n\tvar hsla = swizzle(arguments);\n\treturn hsla.length < 4 || hsla[3] === 1\n\t\t? 'hsl(' + hsla[0] + ', ' + hsla[1] + '%, ' + hsla[2] + '%)'\n\t\t: 'hsla(' + hsla[0] + ', ' + hsla[1] + '%, ' + hsla[2] + '%, ' + hsla[3] + ')';\n};\n\n// hwb is a bit different than rgb(a) & hsl(a) since there is no alpha specific syntax\n// (hwb have alpha optional & 1 is default value)\ncs.to.hwb = function () {\n\tvar hwba = swizzle(arguments);\n\n\tvar a = '';\n\tif (hwba.length >= 4 && hwba[3] !== 1) {\n\t\ta = ', ' + hwba[3];\n\t}\n\n\treturn 'hwb(' + hwba[0] + ', ' + hwba[1] + '%, ' + hwba[2] + '%' + a + ')';\n};\n\ncs.to.keyword = function (rgb) {\n\treturn reverseNames[rgb.slice(0, 3)];\n};\n\n// helpers\nfunction clamp(num, min, max) {\n\treturn Math.min(Math.max(min, num), max);\n}\n\nfunction hexDouble(num) {\n\tvar str = num.toString(16).toUpperCase();\n\treturn (str.length < 2) ? '0' + str : str;\n}\n", "'use strict';\n\nvar isArrayish = require('is-arrayish');\n\nvar concat = Array.prototype.concat;\nvar slice = Array.prototype.slice;\n\nvar swizzle = module.exports = function swizzle(args) {\n\tvar results = [];\n\n\tfor (var i = 0, len = args.length; i < len; i++) {\n\t\tvar arg = args[i];\n\n\t\tif (isArrayish(arg)) {\n\t\t\t// http://jsperf.com/javascript-array-concat-vs-push/98\n\t\t\tresults = concat.call(results, slice.call(arg));\n\t\t} else {\n\t\t\tresults.push(arg);\n\t\t}\n\t}\n\n\treturn results;\n};\n\nswizzle.wrap = function (fn) {\n\treturn function () {\n\t\treturn fn(swizzle(arguments));\n\t};\n};\n", "'use strict';\n\nmodule.exports = function isArrayish(obj) {\n\tif (!obj) {\n\t\treturn false;\n\t}\n\n\treturn obj instanceof Array || Array.isArray(obj) ||\n\t\t(obj.length >= 0 && obj.splice instanceof Function);\n};\n", "var conversions = require('./conversions');\nvar route = require('./route');\n\nvar convert = {};\n\nvar models = Object.keys(conversions);\n\nfunction wrapRaw(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\treturn fn(args);\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nfunction wrapRounded(fn) {\n\tvar wrappedFn = function (args) {\n\t\tif (args === undefined || args === null) {\n\t\t\treturn args;\n\t\t}\n\n\t\tif (arguments.length > 1) {\n\t\t\targs = Array.prototype.slice.call(arguments);\n\t\t}\n\n\t\tvar result = fn(args);\n\n\t\t// we're assuming the result is an array here.\n\t\t// see notice in conversions.js; don't use box types\n\t\t// in conversion functions.\n\t\tif (typeof result === 'object') {\n\t\t\tfor (var len = result.length, i = 0; i < len; i++) {\n\t\t\t\tresult[i] = Math.round(result[i]);\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t};\n\n\t// preserve .conversion property if there is one\n\tif ('conversion' in fn) {\n\t\twrappedFn.conversion = fn.conversion;\n\t}\n\n\treturn wrappedFn;\n}\n\nmodels.forEach(function (fromModel) {\n\tconvert[fromModel] = {};\n\n\tObject.defineProperty(convert[fromModel], 'channels', {value: conversions[fromModel].channels});\n\tObject.defineProperty(convert[fromModel], 'labels', {value: conversions[fromModel].labels});\n\n\tvar routes = route(fromModel);\n\tvar routeModels = Object.keys(routes);\n\n\trouteModels.forEach(function (toModel) {\n\t\tvar fn = routes[toModel];\n\n\t\tconvert[fromModel][toModel] = wrapRounded(fn);\n\t\tconvert[fromModel][toModel].raw = wrapRaw(fn);\n\t});\n});\n\nmodule.exports = convert;\n", "var conversions = require('./conversions');\n\n/*\n\tthis function routes a model to all other models.\n\n\tall functions that are routed have a property `.conversion` attached\n\tto the returned synthetic function. This property is an array\n\tof strings, each with the steps in between the 'from' and 'to'\n\tcolor models (inclusive).\n\n\tconversions that are not possible simply are not included.\n*/\n\nfunction buildGraph() {\n\tvar graph = {};\n\t// https://jsperf.com/object-keys-vs-for-in-with-closure/3\n\tvar models = Object.keys(conversions);\n\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tgraph[models[i]] = {\n\t\t\t// http://jsperf.com/1-vs-infinity\n\t\t\t// micro-opt, but this is simple.\n\t\t\tdistance: -1,\n\t\t\tparent: null\n\t\t};\n\t}\n\n\treturn graph;\n}\n\n// https://en.wikipedia.org/wiki/Breadth-first_search\nfunction deriveBFS(fromModel) {\n\tvar graph = buildGraph();\n\tvar queue = [fromModel]; // unshift -> queue -> pop\n\n\tgraph[fromModel].distance = 0;\n\n\twhile (queue.length) {\n\t\tvar current = queue.pop();\n\t\tvar adjacents = Object.keys(conversions[current]);\n\n\t\tfor (var len = adjacents.length, i = 0; i < len; i++) {\n\t\t\tvar adjacent = adjacents[i];\n\t\t\tvar node = graph[adjacent];\n\n\t\t\tif (node.distance === -1) {\n\t\t\t\tnode.distance = graph[current].distance + 1;\n\t\t\t\tnode.parent = current;\n\t\t\t\tqueue.unshift(adjacent);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn graph;\n}\n\nfunction link(from, to) {\n\treturn function (args) {\n\t\treturn to(from(args));\n\t};\n}\n\nfunction wrapConversion(toModel, graph) {\n\tvar path = [graph[toModel].parent, toModel];\n\tvar fn = conversions[graph[toModel].parent][toModel];\n\n\tvar cur = graph[toModel].parent;\n\twhile (graph[cur].parent) {\n\t\tpath.unshift(graph[cur].parent);\n\t\tfn = link(conversions[graph[cur].parent][cur], fn);\n\t\tcur = graph[cur].parent;\n\t}\n\n\tfn.conversion = path;\n\treturn fn;\n}\n\nmodule.exports = function (fromModel) {\n\tvar graph = deriveBFS(fromModel);\n\tvar conversion = {};\n\n\tvar models = Object.keys(graph);\n\tfor (var len = models.length, i = 0; i < len; i++) {\n\t\tvar toModel = models[i];\n\t\tvar node = graph[toModel];\n\n\t\tif (node.parent === null) {\n\t\t\t// no possible conversion, or this node is the source model.\n\t\t\tcontinue;\n\t\t}\n\n\t\tconversion[toModel] = wrapConversion(toModel, graph);\n\t}\n\n\treturn conversion;\n};\n\n", "'use strict';\n\nimport $ from 'jquery';\nimport ColorItem from './ColorItem';\n\n/**\n * Handles everything related to the colorpicker color\n * @ignore\n */\nclass ColorHandler {\n  /**\n   * @param {Colorpicker} colorpicker\n   */\n  constructor(colorpicker) {\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n  }\n\n  /**\n   * @returns {*|String|ColorItem}\n   */\n  get fallback() {\n    return this.colorpicker.options.fallbackColor ?\n      this.colorpicker.options.fallbackColor : (this.hasColor() ? this.color : null);\n  }\n\n  /**\n   * @returns {String|null}\n   */\n  get format() {\n    if (this.colorpicker.options.format) {\n      return this.colorpicker.options.format;\n    }\n\n    if (this.hasColor() && this.color.hasTransparency() && this.color.format.match(/^hex/)) {\n      return this.isAlphaEnabled() ? 'rgba' : 'hex';\n    }\n\n    if (this.hasColor()) {\n      return this.color.format;\n    }\n\n    return 'rgb';\n  }\n\n  /**\n   * Internal color getter\n   *\n   * @type {ColorItem|null}\n   */\n  get color() {\n    return this.colorpicker.element.data('color');\n  }\n\n  /**\n   * Internal color setter\n   *\n   * @ignore\n   * @param {ColorItem|null} value\n   */\n  set color(value) {\n    this.colorpicker.element.data('color', value);\n\n    if ((value instanceof ColorItem) && (this.colorpicker.options.format === 'auto')) {\n      // If format is 'auto', use the first parsed one from now on\n      this.colorpicker.options.format = this.color.format;\n    }\n  }\n\n  bind() {\n    // if the color option is set\n    if (this.colorpicker.options.color) {\n      this.color = this.createColor(this.colorpicker.options.color);\n      return;\n    }\n\n    // if element[color] is empty and the input has a value\n    if (!this.color && !!this.colorpicker.inputHandler.getValue()) {\n      this.color = this.createColor(\n        this.colorpicker.inputHandler.getValue(), this.colorpicker.options.autoInputFallback\n      );\n    }\n  }\n\n  unbind() {\n    this.colorpicker.element.removeData('color');\n  }\n\n  /**\n   * Returns the color string from the input value or the 'data-color' attribute of the input or element.\n   * If empty, it returns the defaultValue parameter.\n   *\n   * @returns {String|*}\n   */\n  getColorString() {\n    if (!this.hasColor()) {\n      return '';\n    }\n\n    return this.color.string(this.format);\n  }\n\n  /**\n   * Sets the color value\n   *\n   * @param {String|ColorItem} val\n   */\n  setColorString(val) {\n    let color = val ? this.createColor(val) : null;\n\n    this.color = color ? color : null;\n  }\n\n  /**\n   * Creates a new color using the widget instance options (fallbackColor, format).\n   *\n   * @fires Colorpicker#colorpickerInvalid\n   * @param {*} val\n   * @param {boolean} fallbackOnInvalid\n   * @returns {ColorItem}\n   */\n  createColor(val, fallbackOnInvalid = true) {\n    let color = new ColorItem(this.resolveColorDelegate(val), this.format);\n\n    if (!color.isValid()) {\n      if (fallbackOnInvalid) {\n        color = this.getFallbackColor();\n      }\n\n      /**\n       * (Colorpicker) Fired when the color is invalid and the fallback color is going to be used.\n       *\n       * @event Colorpicker#colorpickerInvalid\n       */\n      this.colorpicker.trigger('colorpickerInvalid', color, val);\n    }\n\n    if (!this.isAlphaEnabled()) {\n      // Alpha is disabled\n      color.alpha = 1;\n    }\n\n    return color;\n  }\n\n  getFallbackColor() {\n    if (this.fallback && (this.fallback === this.color)) {\n      return this.color;\n    }\n\n    let fallback = this.resolveColorDelegate(this.fallback);\n\n    let color = new ColorItem(fallback, this.format);\n\n    if (!color.isValid()) {\n      console.warn('The fallback color is invalid. Falling back to the previous color or black if any.');\n      return this.color ? this.color : new ColorItem('#000000', this.format);\n    }\n\n    return color;\n  }\n\n  /**\n   * @returns {ColorItem}\n   */\n  assureColor() {\n    if (!this.hasColor()) {\n      this.color = this.getFallbackColor();\n    }\n\n    return this.color;\n  }\n\n  /**\n   * Delegates the color resolution to the colorpicker extensions.\n   *\n   * @param {String|*} color\n   * @param {boolean} realColor if true, the color should resolve into a real (not named) color code\n   * @returns {ColorItem|String|*|null}\n   */\n  resolveColorDelegate(color, realColor = true) {\n    let extResolvedColor = false;\n\n    $.each(this.colorpicker.extensions, function (name, ext) {\n      if (extResolvedColor !== false) {\n        // skip if resolved\n        return;\n      }\n      extResolvedColor = ext.resolveColor(color, realColor);\n    });\n\n    return extResolvedColor ? extResolvedColor : color;\n  }\n\n  /**\n   * Checks if there is a color object, that it is valid and it is not a fallback\n   * @returns {boolean}\n   */\n  isInvalidColor() {\n    return !this.hasColor() || !this.color.isValid();\n  }\n\n  /**\n   * Returns true if the useAlpha option is exactly true, false otherwise\n   * @returns {boolean}\n   */\n  isAlphaEnabled() {\n    return (this.colorpicker.options.useAlpha !== false);\n  }\n\n  /**\n   * Returns true if the current color object is an instance of Color, false otherwise.\n   * @returns {boolean}\n   */\n  hasColor() {\n    return this.color instanceof ColorItem;\n  }\n}\n\nexport default ColorHandler;\n", "'use strict';\n\nimport $ from 'jquery';\n\n/**\n * Handles everything related to the colorpicker UI\n * @ignore\n */\nclass PickerHandler {\n  /**\n   * @param {Colorpicker} colorpicker\n   */\n  constructor(colorpicker) {\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * @type {jQuery}\n     */\n    this.picker = null;\n  }\n\n  get options() {\n    return this.colorpicker.options;\n  }\n\n  get color() {\n    return this.colorpicker.colorHandler.color;\n  }\n\n  bind() {\n    /**\n     * @type {jQuery|HTMLElement}\n     */\n    let picker = this.picker = $(this.options.template);\n\n    if (this.options.customClass) {\n      picker.addClass(this.options.customClass);\n    }\n\n    if (this.options.horizontal) {\n      picker.addClass('colorpicker-horizontal');\n    }\n\n    if (this._supportsAlphaBar()) {\n      this.options.useAlpha = true;\n      picker.addClass('colorpicker-with-alpha');\n    } else {\n      this.options.useAlpha = false;\n    }\n  }\n\n  attach() {\n    // Inject the colorpicker element into the DOM\n    let pickerParent = this.colorpicker.container ? this.colorpicker.container : null;\n\n    if (pickerParent) {\n      this.picker.appendTo(pickerParent);\n    }\n  }\n\n  unbind() {\n    this.picker.remove();\n  }\n\n  _supportsAlphaBar() {\n    return (\n      (this.options.useAlpha || (this.colorpicker.colorHandler.hasColor() && this.color.hasTransparency())) &&\n      (this.options.useAlpha !== false) &&\n      (!this.options.format || (this.options.format && !this.options.format.match(/^hex([36])?$/i)))\n    );\n  }\n\n  /**\n   * Changes the color adjustment bars using the current color object information.\n   */\n  update() {\n    if (!this.colorpicker.colorHandler.hasColor()) {\n      return;\n    }\n\n    let vertical = (this.options.horizontal !== true),\n      slider = vertical ? this.options.sliders : this.options.slidersHorz;\n\n    let saturationGuide = this.picker.find('.colorpicker-saturation .colorpicker-guide'),\n      hueGuide = this.picker.find('.colorpicker-hue .colorpicker-guide'),\n      alphaGuide = this.picker.find('.colorpicker-alpha .colorpicker-guide');\n\n    let hsva = this.color.toHsvaRatio();\n\n    // Set guides position\n    if (hueGuide.length) {\n      hueGuide.css(vertical ? 'top' : 'left', (vertical ? slider.hue.maxTop : slider.hue.maxLeft) * (1 - hsva.h));\n    }\n    if (alphaGuide.length) {\n      alphaGuide.css(vertical ? 'top' : 'left', (vertical ? slider.alpha.maxTop : slider.alpha.maxLeft) * (1 - hsva.a));\n    }\n    if (saturationGuide.length) {\n      saturationGuide.css({\n        'top': slider.saturation.maxTop - hsva.v * slider.saturation.maxTop,\n        'left': hsva.s * slider.saturation.maxLeft\n      });\n    }\n\n    // Set saturation hue background\n    this.picker.find('.colorpicker-saturation')\n      .css('backgroundColor', this.color.getCloneHueOnly().toHexString()); // we only need hue\n\n    // Set alpha color gradient\n    let hexColor = this.color.toHexString();\n\n    let alphaBg = '';\n\n    if (this.options.horizontal) {\n      alphaBg = `linear-gradient(to right, ${hexColor} 0%, transparent 100%)`;\n    } else {\n      alphaBg = `linear-gradient(to bottom, ${hexColor} 0%, transparent 100%)`;\n    }\n\n    this.picker.find('.colorpicker-alpha-color').css('background', alphaBg);\n  }\n}\n\nexport default PickerHandler;\n", "'use strict';\n\n/**\n * Handles everything related to the colorpicker addon\n * @ignore\n */\nclass AddonHandler {\n  /**\n   * @param {Colorpicker} colorpicker\n   */\n  constructor(colorpicker) {\n    /**\n     * @type {Colorpicker}\n     */\n    this.colorpicker = colorpicker;\n    /**\n     * @type {jQuery}\n     */\n    this.addon = null;\n  }\n\n  hasAddon() {\n    return !!this.addon;\n  }\n\n  bind() {\n    /**\n     * @type {*|jQuery}\n     */\n    this.addon = this.colorpicker.options.addon ?\n      this.colorpicker.element.find(this.colorpicker.options.addon) : null;\n\n    if (this.addon && (this.addon.length === 0)) {\n      // not found\n      this.addon = null;\n    }\n  }\n\n  unbind() {\n    if (this.hasAddon()) {\n      this.addon.off('.colorpicker');\n    }\n  }\n\n  /**\n   * If the addon element is present, its background color is updated\n   */\n  update() {\n    if (!this.colorpicker.colorHandler.hasColor() || !this.hasAddon()) {\n      return;\n    }\n\n    let colorStr = this.colorpicker.colorHandler.getColorString();\n\n    let styles = {'background': colorStr};\n\n    let icn = this.addon.find('i').eq(0);\n\n    if (icn.length > 0) {\n      icn.css(styles);\n    } else {\n      this.addon.css(styles);\n    }\n  }\n}\n\nexport default AddonHandler;\n"], "sourceRoot": ""}