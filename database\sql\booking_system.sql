-- Auto-generated SQL file from database
-- Generated on: 2025-07-31 09:46:03

SET FOREIGN_KEY_CHECKS = 0;

DROP TABLE IF EXISTS `notifications`;
DROP TABLE IF EXISTS `customers`;
DROP TABLE IF EXISTS `bookings`;
DROP TABLE IF EXISTS `booking_status_logs`;
DROP TABLE IF EXISTS `booking_logs`;
DROP TABLE IF EXISTS `boats`;
DROP TABLE IF EXISTS `boat_reservations`;
DROP TABLE IF EXISTS `boat_availability_view`;
DROP TABLE IF EXISTS `boat_availability_dates`;
DROP TABLE IF EXISTS `admins`;
DROP TABLE IF EXISTS `activity_logs`;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

CREATE DATABASE IF NOT EXISTS `booking_system`;
USE `booking_system`;

CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `activity` varchar(255) NOT NULL,
  `activity_time` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('1', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('3', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('4', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('6', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('7', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('9', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('10', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('12', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('13', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('15', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('16', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('18', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('19', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('21', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('22', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('24', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('25', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('27', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('28', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('30', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('31', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('33', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('34', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('36', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('37', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('39', '3', 'Added new boat', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('40', '1', 'Uploaded profile image', '2025-05-01 05:46:00');
INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES ('42', '3', 'Added new boat', '2025-05-01 05:46:00');

CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `role` varchar(50) NOT NULL,
  `status` varchar(20) NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `last_activity` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `admins` (`admin_id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `role`, `status`, `last_login`, `last_activity`, `created_at`, `updated_at`) VALUES ('1', 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'Admin', '<EMAIL>', '09123456789', 'super_admin', 'active', '2025-04-28 17:28:04', '2025-07-27 17:32:08', '2025-04-14 14:37:35', '2025-04-28 17:28:04');
INSERT INTO `admins` (`admin_id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `role`, `status`, `last_login`, `last_activity`, `created_at`, `updated_at`) VALUES ('2', 'jhona', '$2y$10$abcdefghijABCDEFGHIJklmnopqrstuvwx/yz1234567890', 'Jhona Mae', 'Santander', '<EMAIL>', '09173456789', 'sub_admin', 'active', '2025-04-27 09:00:00', NULL, '2025-04-01 11:00:00', '2025-04-27 09:00:00');
INSERT INTO `admins` (`admin_id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `role`, `status`, `last_login`, `last_activity`, `created_at`, `updated_at`) VALUES ('4', 'jordan', '$2y$10$klmnopqrstuvwxABCDEFGHIJabcdefghij/yz1234567890', 'Jordan', 'Barcarlos', '<EMAIL>', '09183456789', 'sub_admin', 'active', '2025-04-25 14:00:00', NULL, '2025-04-03 13:00:00', '2025-04-25 14:00:00');
INSERT INTO `admins` (`admin_id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `role`, `status`, `last_login`, `last_activity`, `created_at`, `updated_at`) VALUES ('6', 'lhance', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Lhance', 'Montero', '<EMAIL>', '09123456789', 'sub_admin', 'active', '2025-04-28 18:00:00', NULL, '2025-04-14 15:00:00', '2025-04-28 18:00:00');

CREATE TABLE `boat_availability_dates` (
  `id` int(11) NOT NULL,
  `boat_id` int(11) NOT NULL,
  `available_date` date NOT NULL,
  `status` enum('available','not available','maintenance') NOT NULL DEFAULT 'available',
  `notes` text DEFAULT NULL,
  `added_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `boat_availability_view` (
  `boat_id` int(11) DEFAULT NULL,
  `boat_name` varchar(255) DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `capacity` int(11) DEFAULT NULL,
  `price_per_day` decimal(10,2) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `availability_status` enum('available','not available','maintenance') DEFAULT NULL,
  `scheduled_dates` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `boat_reservations` (
  `booking_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `boat_id` int(11) NOT NULL,
  `booking_code` varchar(50) NOT NULL,
  `booking_date` date NOT NULL,
  `start_time` time NOT NULL,
  `booking_status` enum('pending','confirmed','cancelled') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('1', '1', '1', 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 09:28:16');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('2', '2', '2', 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 10:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('3', '3', '3', 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 13:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('4', '4', '2', 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('5', '5', '3', 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('6', '6', '1', 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('7', '7', '2', 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('8', '8', '1', 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('9', '9', '3', 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('10', '10', '2', 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('11', '11', '1', 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('12', '12', '2', 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 20:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('13', '13', '1', 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('14', '14', '2', 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 15:30:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('15', '15', '3', 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 16:20:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('16', '16', '1', 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 15:10:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('17', '17', '2', 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 17:40:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('18', '1', '1', 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 14:55:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('19', '1', '1', 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 09:28:16');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('20', '2', '2', 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 10:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('21', '3', '3', 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 13:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('22', '4', '2', 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('23', '5', '3', 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('24', '6', '1', 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('25', '7', '2', 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('26', '8', '1', 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('27', '9', '3', 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('28', '10', '2', 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('29', '11', '1', 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('30', '12', '2', 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 20:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('31', '13', '1', 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('32', '14', '2', 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 15:30:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('33', '15', '3', 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 16:20:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('34', '16', '1', 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 15:10:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('35', '17', '2', 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 17:40:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('36', '1', '1', 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 14:55:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('37', '1', '1', 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 09:28:16');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('38', '2', '2', 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 10:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('39', '3', '3', 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 13:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('40', '4', '2', 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('41', '5', '3', 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('42', '6', '1', 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('43', '7', '2', 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('44', '8', '1', 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('45', '9', '3', 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('46', '10', '2', 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('47', '11', '1', 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('48', '12', '2', 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 20:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('49', '13', '1', 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('50', '14', '2', 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 15:30:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('51', '15', '3', 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 16:20:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('52', '16', '1', 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 15:10:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('53', '17', '2', 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 17:40:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('54', '1', '1', 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 14:55:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('55', '1', '1', 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 09:28:16');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('56', '2', '2', 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 10:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('57', '3', '3', 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 13:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('58', '4', '2', 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('59', '5', '3', 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('60', '6', '1', 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('61', '7', '2', 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('62', '8', '1', 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('63', '9', '3', 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('64', '10', '2', 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('65', '11', '1', 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('66', '12', '2', 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 20:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('67', '13', '1', 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('68', '14', '2', 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 15:30:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('69', '15', '3', 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 16:20:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('70', '16', '1', 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 15:10:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('71', '17', '2', 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 17:40:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('72', '1', '1', 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 14:55:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('73', '1', '1', 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 09:28:16');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('74', '2', '2', 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 10:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('75', '3', '3', 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 13:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('76', '4', '2', 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('77', '5', '3', 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('78', '6', '1', 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('79', '7', '2', 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('80', '8', '1', 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('81', '9', '3', 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('82', '10', '2', 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 16:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('83', '11', '1', 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 17:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('84', '12', '2', 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 20:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('85', '13', '1', 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 14:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('86', '14', '2', 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 15:30:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('87', '15', '3', 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 16:20:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('88', '16', '1', 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 15:10:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('89', '17', '2', 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 17:40:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('90', '1', '1', 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 14:55:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('91', '1', '1', 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 09:28:16');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('92', '2', '2', 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 10:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('93', '3', '3', 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 13:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('94', '4', '2', 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 15:00:00');
INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES ('95', '5', '3', 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 16:00:00');

CREATE TABLE `boats` (
  `boat_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` varchar(50) DEFAULT 'small',
  `capacity` int(11) DEFAULT 10,
  `price_per_day` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `status` varchar(20) DEFAULT 'Available',
  `destination` varchar(255) DEFAULT NULL,
  `availability_status` enum('available','not available','maintenance') DEFAULT 'available',
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `boats` (`boat_id`, `name`, `type`, `capacity`, `price_per_day`, `description`, `status`, `destination`, `availability_status`, `created_at`) VALUES ('1', 'Boat 1', 'small', '10', '2000.00', 'Description for Boat 1', 'Available', 'Carles', 'available', '2025-05-05 20:47:15');
INSERT INTO `boats` (`boat_id`, `name`, `type`, `capacity`, `price_per_day`, `description`, `status`, `destination`, `availability_status`, `created_at`) VALUES ('2', 'Boat 2', 'medium', '15', '3500.00', 'Description for Boat 2', 'Available', 'Carles', 'available', '2025-05-05 20:47:15');
INSERT INTO `boats` (`boat_id`, `name`, `type`, `capacity`, `price_per_day`, `description`, `status`, `destination`, `availability_status`, `created_at`) VALUES ('3', 'Boat 3', 'large', '20', '3500.00', 'Description for Boat 3', 'Available', 'Carles', 'available', '2025-05-05 20:47:15');
INSERT INTO `boats` (`boat_id`, `name`, `type`, `capacity`, `price_per_day`, `description`, `status`, `destination`, `availability_status`, `created_at`) VALUES ('4', 'Boat 4', 'special', '10', '3500.00', 'Description for Boat 4', 'Available', 'Carles', 'available', '2025-05-05 20:47:15');

CREATE TABLE `booking_logs` (
  `log_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `date_of_booking` date NOT NULL,
  `time` time NOT NULL,
  `booking_id` int(11) NOT NULL,
  `boat` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `admin_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `booking_logs` (`log_id`, `user_id`, `date_of_booking`, `time`, `booking_id`, `boat`, `price`, `admin_id`) VALUES ('1', '1', '2025-01-09', '09:28:16', '1', 'Boat 1', '2000.00', '1');
INSERT INTO `booking_logs` (`log_id`, `user_id`, `date_of_booking`, `time`, `booking_id`, `boat`, `price`, `admin_id`) VALUES ('2', '2', '2025-02-02', '10:00:00', '2', 'Boat 2', '3500.00', '3');

CREATE TABLE `booking_status_logs` (
  `log_id` int(11) NOT NULL,
  `booking_id` int(11) NOT NULL,
  `old_status` enum('pending','confirmed','cancelled') NOT NULL,
  `new_status` enum('pending','confirmed','cancelled') NOT NULL,
  `reason` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `bookings` (
  `booking_id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `age` int(11) DEFAULT NULL,
  `sex` varchar(10) DEFAULT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `emergency_name` varchar(255) DEFAULT NULL,
  `emergency_number` varchar(20) DEFAULT NULL,
  `boat_id` int(11) NOT NULL,
  `no_of_pax` int(11) NOT NULL,
  `regular_pax` int(11) NOT NULL DEFAULT 0,
  `discounted_pax` int(11) NOT NULL DEFAULT 0,
  `children_pax` int(11) NOT NULL DEFAULT 0,
  `infants_pax` int(11) NOT NULL DEFAULT 0,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `booking_time` datetime NOT NULL,
  `environmental_fee` decimal(10,2) NOT NULL,
  `payment_method` varchar(50) NOT NULL,
  `gcash_proof_filename` varchar(255) DEFAULT NULL COMMENT 'Filename of uploaded GCash proof of payment',
  `total` decimal(10,2) NOT NULL,
  `booking_status` enum('pending','confirmed','cancelled','accepted','rejected','verification_pending') NOT NULL DEFAULT 'pending',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `tour_destination` varchar(255) DEFAULT NULL,
  `drop_off_location` varchar(255) DEFAULT NULL,
  `booking_code` varchar(50) NOT NULL,
  `destination_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `gcash_proof_filename`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('1', '1', 'Ralph', 'Ramos', '21', 'male', '09345789658', '<EMAIL>', 'Carles, Iloilo', 'Jose Ramos', '09999000111', '1', '15', '12', '2', '1', '0', '2025-01-28 00:00:00', '2025-01-29 00:00:00', '2025-01-27 08:30:00', '1020.00', 'gcash', NULL, '4250.00', 'confirmed', '2025-01-26 14:20:00', 'Gigantes Sur Island', 'Carles Tourism Office', 'BOAT-20250126-12345', '1');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `gcash_proof_filename`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('2', '2', 'Maria', 'Santos', '25', 'female', '09181234567', '<EMAIL>', 'Estancia, Iloilo', 'Miguel Santos', '09000111222', '2', '8', '6', '1', '1', '0', '2025-01-30 00:00:00', '2025-01-31 00:00:00', '2025-01-27 09:15:00', '510.00', 'manual', NULL, '3200.00', 'pending', '2025-01-27 09:15:00', 'Sicogon Island', 'Estancia Port', 'BOAT-20250127-23456', '2');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `gcash_proof_filename`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('3', '3', 'John', 'Dela Cruz', '30', 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Jane Dela Cruz', '09111222333', '3', '12', '8', '2', '2', '0', '2025-02-05 00:00:00', '2025-02-07 00:00:00', '2025-01-25 16:45:00', '720.00', 'gcash', NULL, '5800.00', 'pending', '2025-01-25 16:45:00', 'Bantique Island', 'Balasan Port', 'BOAT-20250125-34567', '3');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `gcash_proof_filename`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('4', '4', 'Ana', 'Villanueva', '28', 'female', '09267891234', '<EMAIL>', 'Carles, Iloilo', 'Pedro Villanueva', '09222333444', '1', '6', '4', '1', '1', '0', '2025-01-29 00:00:00', '2025-01-30 00:00:00', '2025-01-26 11:20:00', '360.00', 'gcash', NULL, '2800.00', 'confirmed', '2025-01-26 11:20:00', 'Cabugao Gamay', 'Carles Main Port', 'BOAT-20250126-45678', '4');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `gcash_proof_filename`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('5', '5', 'Roberto', 'Fernandez', '35', 'male', '09356789012', '<EMAIL>', 'Iloilo City', 'Carmen Fernandez', '09333444555', '2', '20', '15', '3', '2', '0', '2025-02-10 00:00:00', '2025-02-12 00:00:00', '2025-01-24 13:30:00', '1305.00', 'manual', NULL, '8500.00', 'pending', '2025-01-24 13:30:00', 'Tangke Lagoon', 'Carles Tourism Port', 'BOAT-20250124-56789', '5');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `gcash_proof_filename`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('6', '6', 'Luz', 'Mercado', '32', 'female', '09445678901', '<EMAIL>', 'Sicogon Island, Carles', 'Rico Mercado', '09444555666', '3', '4', '2', '1', '1', '0', '2025-01-28 00:00:00', '2025-01-28 00:00:00', '2025-01-27 07:45:00', '210.00', 'gcash', NULL, '1800.00', 'confirmed', '2025-01-27 07:45:00', 'Antonia Beach', 'Sicogon Port', 'BOAT-20250127-67890', '6');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `gcash_proof_filename`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('7', '7', 'Carlos', 'Reyes', '29', 'male', '09534567890', '<EMAIL>', 'Passi City, Iloilo', 'Elena Reyes', '09555666777', '1', '10', '7', '2', '1', '0', '2025-02-01 00:00:00', '2025-02-02 00:00:00', '2025-01-26 15:10:00', '645.00', 'manual', NULL, '4200.00', 'pending', '2025-01-26 15:10:00', 'Agho Island', 'Estancia Main Port', 'BOAT-20250126-78901', '7');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `gcash_proof_filename`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('8', '8', 'Grace', 'Torres', '26', 'female', '09623456789', '<EMAIL>', 'Roxas City, Capiz', 'Mark Torres', '09666777888', '2', '7', '5', '1', '1', '0', '2025-01-25 00:00:00', '2025-01-26 00:00:00', '2025-01-23 10:30:00', '435.00', 'gcash', NULL, '3100.00', 'cancelled', '2025-01-23 10:30:00', 'Bayas Island', 'Carles Port Area', 'BOAT-20250123-89012', '8');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `gcash_proof_filename`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('9', '9', 'Miguel', 'Gonzales', '33', 'male', '09712345678', '<EMAIL>', 'Balasan, Iloilo', 'Rosa Gonzales', '09777888999', '3', '14', '10', '2', '2', '0', '2025-02-03 00:00:00', '2025-02-04 00:00:00', '2025-01-25 06:00:00', '870.00', 'manual', NULL, '6200.00', 'confirmed', '2025-01-25 06:00:00', 'Pan de Azucar', 'Balasan Main Port', 'BOAT-20250125-90123', '9');
INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `gcash_proof_filename`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES ('10', '10', 'Sofia', 'Castillo', '24', 'female', '09801234567', '<EMAIL>', 'Carles, Iloilo', 'Luis Castillo', '09888999000', '1', '5', '3', '1', '1', '0', '2025-01-31 00:00:00', '2025-02-01 00:00:00', '2025-01-27 18:45:00', '285.00', 'gcash', NULL, '2250.00', 'pending', '2025-01-27 18:45:00', 'Tumaquin Island', 'Carles Tourism Office', 'BOAT-20250127-01234', '10');

CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `age` int(11) DEFAULT NULL,
  `sex` varchar(10) DEFAULT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `notifications` (
  `notification_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(50) DEFAULT 'general',
  `reference_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`notification_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

