-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 27, 2025 at 07:33 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `booking_system`
--

-- --------------------------------------------------------

--
-- Table structure for table `activity_logs`
--

CREATE TABLE `activity_logs` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `activity` varchar(255) NOT NULL,
  `activity_time` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `activity_logs`
--

INSERT INTO `activity_logs` (`id`, `admin_id`, `activity`, `activity_time`) VALUES
(1, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(3, 3, 'Added new boat', '2025-05-01 05:46:00'),
(4, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(6, 3, 'Added new boat', '2025-05-01 05:46:00'),
(7, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(9, 3, 'Added new boat', '2025-05-01 05:46:00'),
(10, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(12, 3, 'Added new boat', '2025-05-01 05:46:00'),
(13, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(15, 3, 'Added new boat', '2025-05-01 05:46:00'),
(16, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(18, 3, 'Added new boat', '2025-05-01 05:46:00'),
(19, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(21, 3, 'Added new boat', '2025-05-01 05:46:00'),
(22, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(24, 3, 'Added new boat', '2025-05-01 05:46:00'),
(25, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(27, 3, 'Added new boat', '2025-05-01 05:46:00'),
(28, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(30, 3, 'Added new boat', '2025-05-01 05:46:00'),
(31, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(33, 3, 'Added new boat', '2025-05-01 05:46:00'),
(34, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(36, 3, 'Added new boat', '2025-05-01 05:46:00'),
(37, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(39, 3, 'Added new boat', '2025-05-01 05:46:00'),
(40, 1, 'Uploaded profile image', '2025-05-01 05:46:00'),
(42, 3, 'Added new boat', '2025-05-01 05:46:00');

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `role` varchar(50) NOT NULL,
  `status` varchar(20) NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `last_activity` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`admin_id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `role`, `status`, `last_login`, `last_activity`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'Admin', '<EMAIL>', '09123456789', 'super_admin', 'active', '2025-04-28 17:28:04', '2025-07-27 17:32:08', '2025-04-14 14:37:35', '2025-04-28 17:28:04'),
(2, 'jhona', '$2y$10$abcdefghijABCDEFGHIJklmnopqrstuvwx/yz1234567890', 'Jhona Mae', 'Santander', '<EMAIL>', '09173456789', 'sub_admin', 'active', '2025-04-27 09:00:00', NULL, '2025-04-01 11:00:00', '2025-04-27 09:00:00'),
(4, 'jordan', '$2y$10$klmnopqrstuvwxABCDEFGHIJabcdefghij/yz1234567890', 'Jordan', 'Barcarlos', '<EMAIL>', '09183456789', 'sub_admin', 'active', '2025-04-25 14:00:00', NULL, '2025-04-03 13:00:00', '2025-04-25 14:00:00'),
(6, 'lhance', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Lhance', 'Montero', '<EMAIL>', '09123456789', 'sub_admin', 'active', '2025-04-28 18:00:00', NULL, '2025-04-14 15:00:00', '2025-04-28 18:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `boats`
--

CREATE TABLE `boats` (
  `boat_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` varchar(50) DEFAULT 'small',
  `capacity` int(11) DEFAULT 10,
  `price_per_day` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `status` varchar(20) DEFAULT 'Available',
  `destination` varchar(255) DEFAULT NULL,
  `availability_status` enum('available','not available','maintenance') DEFAULT 'available',
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `boats`
--

INSERT INTO `boats` (`boat_id`, `name`, `type`, `capacity`, `price_per_day`, `description`, `status`, `destination`, `availability_status`, `created_at`) VALUES
(1, 'Boat 1', 'small', 10, 2000.00, 'Description for Boat 1', 'Available', 'Carles', 'available', '2025-05-05 20:47:15'),
(2, 'Boat 2', 'medium', 15, 3500.00, 'Description for Boat 2', 'Available', 'Carles', 'available', '2025-05-05 20:47:15'),
(3, 'Boat 3', 'large', 20, 3500.00, 'Description for Boat 3', 'Available', 'Carles', 'available', '2025-05-05 20:47:15'),
(4, 'Boat 4', 'special', 10, 3500.00, 'Description for Boat 4', 'Available', 'Carles', 'available', '2025-05-05 20:47:15');

-- --------------------------------------------------------

--
-- Table structure for table `boat_availability_dates`
--

CREATE TABLE `boat_availability_dates` (
  `id` int(11) NOT NULL,
  `boat_id` int(11) NOT NULL,
  `available_date` date NOT NULL,
  `status` enum('available','not available','maintenance') NOT NULL DEFAULT 'available',
  `notes` text DEFAULT NULL,
  `added_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `boat_availability_view`
--

CREATE TABLE `boat_availability_view` (
  `boat_id` int(11) DEFAULT NULL,
  `boat_name` varchar(255) DEFAULT NULL,
  `type` varchar(50) DEFAULT NULL,
  `capacity` int(11) DEFAULT NULL,
  `price_per_day` decimal(10,2) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `availability_status` enum('available','not available','maintenance') DEFAULT NULL,
  `scheduled_dates` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `boat_reservations`
--

CREATE TABLE `boat_reservations` (
  `booking_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `boat_id` int(11) NOT NULL,
  `booking_code` varchar(50) NOT NULL,
  `booking_date` date NOT NULL,
  `start_time` time NOT NULL,
  `booking_status` enum('pending','confirmed','cancelled') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `boat_reservations`
--

INSERT INTO `boat_reservations` (`booking_id`, `user_id`, `boat_id`, `booking_code`, `booking_date`, `start_time`, `booking_status`, `created_at`) VALUES
(1, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(2, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(3, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(4, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(5, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00'),
(6, 6, 1, 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 06:00:00'),
(7, 7, 2, 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 09:00:00'),
(8, 8, 1, 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 08:00:00'),
(9, 9, 3, 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 07:00:00'),
(10, 10, 2, 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 08:00:00'),
(11, 11, 1, 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 09:00:00'),
(12, 12, 2, 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 12:00:00'),
(13, 13, 1, 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 06:00:00'),
(14, 14, 2, 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 07:30:00'),
(15, 15, 3, 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 08:20:00'),
(16, 16, 1, 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 07:10:00'),
(17, 17, 2, 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 09:40:00'),
(18, 1, 1, 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 06:55:00'),
(19, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(20, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(21, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(22, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(23, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00'),
(24, 6, 1, 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 06:00:00'),
(25, 7, 2, 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 09:00:00'),
(26, 8, 1, 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 08:00:00'),
(27, 9, 3, 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 07:00:00'),
(28, 10, 2, 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 08:00:00'),
(29, 11, 1, 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 09:00:00'),
(30, 12, 2, 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 12:00:00'),
(31, 13, 1, 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 06:00:00'),
(32, 14, 2, 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 07:30:00'),
(33, 15, 3, 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 08:20:00'),
(34, 16, 1, 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 07:10:00'),
(35, 17, 2, 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 09:40:00'),
(36, 1, 1, 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 06:55:00'),
(37, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(38, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(39, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(40, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(41, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00'),
(42, 6, 1, 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 06:00:00'),
(43, 7, 2, 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 09:00:00'),
(44, 8, 1, 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 08:00:00'),
(45, 9, 3, 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 07:00:00'),
(46, 10, 2, 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 08:00:00'),
(47, 11, 1, 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 09:00:00'),
(48, 12, 2, 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 12:00:00'),
(49, 13, 1, 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 06:00:00'),
(50, 14, 2, 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 07:30:00'),
(51, 15, 3, 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 08:20:00'),
(52, 16, 1, 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 07:10:00'),
(53, 17, 2, 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 09:40:00'),
(54, 1, 1, 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 06:55:00'),
(55, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(56, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(57, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(58, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(59, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00'),
(60, 6, 1, 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 06:00:00'),
(61, 7, 2, 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 09:00:00'),
(62, 8, 1, 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 08:00:00'),
(63, 9, 3, 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 07:00:00'),
(64, 10, 2, 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 08:00:00'),
(65, 11, 1, 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 09:00:00'),
(66, 12, 2, 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 12:00:00'),
(67, 13, 1, 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 06:00:00'),
(68, 14, 2, 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 07:30:00'),
(69, 15, 3, 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 08:20:00'),
(70, 16, 1, 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 07:10:00'),
(71, 17, 2, 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 09:40:00'),
(72, 1, 1, 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 06:55:00'),
(73, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(74, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(75, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(76, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(77, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00'),
(78, 6, 1, 'BOAT1-20250311-68532', '2025-03-29', '07:30:00', 'pending', '2025-03-28 06:00:00'),
(79, 7, 2, 'BOAT2-20250314-12345', '2025-04-10', '10:30:00', 'cancelled', '2025-04-09 09:00:00'),
(80, 8, 1, 'BOAT1-20250406-77777', '2025-04-15', '09:00:00', 'confirmed', '2025-04-14 08:00:00'),
(81, 9, 3, 'BOAT3-20250403-67890', '2025-01-25', '08:15:00', 'pending', '2025-01-24 07:00:00'),
(82, 10, 2, 'BOAT2-20250404-88888', '2025-02-12', '09:45:00', 'confirmed', '2025-02-11 08:00:00'),
(83, 11, 1, 'BOAT1-20250408-68532', '2025-03-03', '10:30:00', 'cancelled', '2025-03-02 09:00:00'),
(84, 12, 2, 'BOAT2-20250409-12345', '2025-03-22', '14:00:00', 'confirmed', '2025-03-21 12:00:00'),
(85, 13, 1, 'BOAT1-20250410-88888', '2025-04-05', '07:45:00', 'pending', '2025-04-04 06:00:00'),
(86, 14, 2, 'BOAT2-20250114-12345', '2025-01-28', '08:45:00', 'pending', '2025-01-27 07:30:00'),
(87, 15, 3, 'BOAT3-20250209-67890', '2025-02-18', '10:20:00', 'pending', '2025-02-17 08:20:00'),
(88, 16, 1, 'BOAT1-20250311-68532', '2025-03-12', '09:10:00', 'pending', '2025-03-11 07:10:00'),
(89, 17, 2, 'BOAT2-20250314-12345', '2025-04-08', '11:40:00', 'pending', '2025-04-07 09:40:00'),
(90, 1, 1, 'BOAT1-20250406-77777', '2025-04-18', '07:55:00', 'pending', '2025-04-17 06:55:00'),
(91, 1, 1, 'BOAT1-20250109-68532', '2025-01-20', '09:28:16', 'pending', '2025-01-19 01:28:16'),
(92, 2, 2, 'BOAT2-20250114-12345', '2025-02-02', '10:00:00', 'confirmed', '2025-02-01 02:00:00'),
(93, 3, 3, 'BOAT3-20250209-67890', '2025-02-10', '13:00:00', 'cancelled', '2025-02-09 05:00:00'),
(94, 4, 2, 'BOAT2-20250224-88888', '2025-03-05', '08:00:00', 'pending', '2025-03-04 07:00:00'),
(95, 5, 3, 'BOAT3-20250303-77777', '2025-03-18', '11:00:00', 'confirmed', '2025-03-17 08:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `bookings`
--

CREATE TABLE `bookings` (
  `booking_id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `suffix` varchar(10) DEFAULT NULL COMMENT 'Name suffix like Jr., Sr., II, etc.',
  `age` int(11) DEFAULT NULL,
  `sex` varchar(10) DEFAULT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `emergency_name` varchar(255) DEFAULT NULL,
  `emergency_number` varchar(20) DEFAULT NULL,
  `boat_id` int(11) NOT NULL,
  `no_of_pax` int(11) NOT NULL,
  `total_passengers` int(11) NOT NULL DEFAULT 1 COMMENT 'Total number of passengers including main booker',
  `regular_pax` int(11) NOT NULL DEFAULT 0,
  `discounted_pax` int(11) NOT NULL DEFAULT 0,
  `children_pax` int(11) NOT NULL DEFAULT 0,
  `infants_pax` int(11) NOT NULL DEFAULT 0,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `booking_time` datetime NOT NULL,
  `environmental_fee` decimal(10,2) NOT NULL,
  `payment_method` varchar(50) NOT NULL,
  `gcash_proof_filename` varchar(255) DEFAULT NULL COMMENT 'Filename of uploaded GCash proof of payment',
  `total` decimal(10,2) NOT NULL,
  `booking_status` enum('pending','confirmed','cancelled','accepted','rejected','verification_pending') NOT NULL DEFAULT 'pending',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `tour_destination` varchar(255) DEFAULT NULL,
  `drop_off_location` varchar(255) DEFAULT NULL,
  `booking_code` varchar(50) NOT NULL,
  `destination_id` int(11) NOT NULL,
  `passenger_details` text DEFAULT NULL COMMENT 'JSON data of all passengers for backup',
  `is_today_booking` tinyint(1) DEFAULT 0 COMMENT 'Flag for same-day bookings',
  `payment_proof` varchar(255) DEFAULT NULL COMMENT 'Payment proof filename'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `bookings`
--

INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `suffix`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `total_passengers`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `gcash_proof_filename`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`, `passenger_details`, `is_today_booking`, `payment_proof`) VALUES
(1, 1, 'Ralph', 'Ramos', 'Jr.', 21, 'male', '09345789658', '<EMAIL>', 'Carles, Iloilo', 'Jose Ramos', '09999000111', 1, 15, 15, 12, 2, 1, 0, '2025-01-28 00:00:00', '2025-01-29 00:00:00', '2025-01-27 08:30:00', 1020.00, 'gcash', NULL, 4250.00, 'confirmed', '2025-01-26 14:20:00', 'Gigantes Sur Island', 'Carles Tourism Office', 'BOAT-20250126-12345', 1, NULL, 0, NULL),
(2, 2, 'Maria', 'Santos', NULL, 25, 'female', '09181234567', '<EMAIL>', 'Estancia, Iloilo', 'Miguel Santos', '09000111222', 2, 8, 8, 6, 1, 1, 0, '2025-01-30 00:00:00', '2025-01-31 00:00:00', '2025-01-27 09:15:00', 510.00, 'manual', NULL, 3200.00, 'pending', '2025-01-27 09:15:00', 'Sicogon Island', 'Estancia Port', 'BOAT-20250127-23456', 2, NULL, 0, NULL),
(3, 3, 'John', 'Dela Cruz', NULL, 30, 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Jane Dela Cruz', '09111222333', 3, 12, 12, 8, 2, 2, 0, '2025-02-05 00:00:00', '2025-02-07 00:00:00', '2025-01-25 16:45:00', 720.00, 'gcash', NULL, 5800.00, 'pending', '2025-01-25 16:45:00', 'Bantique Island', 'Balasan Port', 'BOAT-20250125-34567', 3, NULL, 0, NULL),
(4, 4, 'Ana', 'Villanueva', NULL, 28, 'female', '09267891234', '<EMAIL>', 'Carles, Iloilo', 'Pedro Villanueva', '09222333444', 1, 6, 6, 4, 1, 1, 0, '2025-01-29 00:00:00', '2025-01-30 00:00:00', '2025-01-26 11:20:00', 360.00, 'gcash', NULL, 2800.00, 'confirmed', '2025-01-26 11:20:00', 'Cabugao Gamay', 'Carles Main Port', 'BOAT-20250126-45678', 4, NULL, 0, NULL),
(5, 5, 'Roberto', 'Fernandez', NULL, 35, 'male', '09356789012', '<EMAIL>', 'Iloilo City', 'Carmen Fernandez', '09333444555', 2, 20, 20, 15, 3, 2, 0, '2025-02-10 00:00:00', '2025-02-12 00:00:00', '2025-01-24 13:30:00', 1305.00, 'manual', NULL, 8500.00, 'pending', '2025-01-24 13:30:00', 'Tangke Lagoon', 'Carles Tourism Port', 'BOAT-20250124-56789', 5, NULL, 0, NULL),
(6, 6, 'Luz', 'Mercado', NULL, 32, 'female', '09445678901', '<EMAIL>', 'Sicogon Island, Carles', 'Rico Mercado', '09444555666', 3, 4, 4, 2, 1, 1, 0, '2025-01-28 00:00:00', '2025-01-28 00:00:00', '2025-01-27 07:45:00', 210.00, 'gcash', NULL, 1800.00, 'confirmed', '2025-01-27 07:45:00', 'Antonia Beach', 'Sicogon Port', 'BOAT-20250127-67890', 6, NULL, 0, NULL),
(7, 7, 'Carlos', 'Reyes', NULL, 29, 'male', '09534567890', '<EMAIL>', 'Passi City, Iloilo', 'Elena Reyes', '09555666777', 1, 10, 10, 7, 2, 1, 0, '2025-02-01 00:00:00', '2025-02-02 00:00:00', '2025-01-26 15:10:00', 645.00, 'manual', NULL, 4200.00, 'pending', '2025-01-26 15:10:00', 'Agho Island', 'Estancia Main Port', 'BOAT-20250126-78901', 7, NULL, 0, NULL),
(8, 8, 'Grace', 'Torres', NULL, 26, 'female', '09623456789', '<EMAIL>', 'Roxas City, Capiz', 'Mark Torres', '09666777888', 2, 7, 7, 5, 1, 1, 0, '2025-01-25 00:00:00', '2025-01-26 00:00:00', '2025-01-23 10:30:00', 435.00, 'gcash', NULL, 3100.00, 'cancelled', '2025-01-23 10:30:00', 'Bayas Island', 'Carles Port Area', 'BOAT-20250123-89012', 8, NULL, 0, NULL),
(9, 9, 'Miguel', 'Gonzales', NULL, 33, 'male', '09712345678', '<EMAIL>', 'Balasan, Iloilo', 'Rosa Gonzales', '09777888999', 3, 14, 14, 10, 2, 2, 0, '2025-02-03 00:00:00', '2025-02-04 00:00:00', '2025-01-25 06:00:00', 870.00, 'manual', NULL, 6200.00, 'confirmed', '2025-01-25 06:00:00', 'Pan de Azucar', 'Balasan Main Port', 'BOAT-20250125-90123', 9, NULL, 0, NULL),
(10, 10, 'Sofia', 'Castillo', NULL, 24, 'female', '09801234567', '<EMAIL>', 'Carles, Iloilo', 'Luis Castillo', '09888999000', 1, 5, 5, 3, 1, 1, 0, '2025-01-31 00:00:00', '2025-02-01 00:00:00', '2025-01-27 18:45:00', 285.00, 'gcash', NULL, 2250.00, 'pending', '2025-01-27 18:45:00', 'Tumaquin Island', 'Carles Tourism Office', 'BOAT-20250127-01234', 10, NULL, 0, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `booking_passengers`
--

CREATE TABLE `booking_passengers` (
  `passenger_id` int(11) NOT NULL,
  `booking_id` int(11) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `passenger_name` varchar(255) DEFAULT NULL COMMENT 'Full name for compatibility',
  `age` int(11) NOT NULL,
  `passenger_age` int(11) DEFAULT NULL COMMENT 'Age field for compatibility',
  `sex` varchar(10) NOT NULL,
  `passenger_sex` varchar(10) DEFAULT NULL COMMENT 'Sex field for compatibility',
  `city` varchar(100) DEFAULT NULL,
  `province` varchar(100) DEFAULT NULL,
  `street_address` varchar(255) DEFAULT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `passenger_type` enum('main_booker','regular','discounted','children','infants') NOT NULL DEFAULT 'regular',
  `is_main_contact` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1 if this is the main contact person',
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `booking_passengers`
--

INSERT INTO `booking_passengers` (`passenger_id`, `booking_id`, `first_name`, `last_name`, `passenger_name`, `age`, `passenger_age`, `sex`, `passenger_sex`, `city`, `province`, `street_address`, `contact_number`, `passenger_type`, `is_main_contact`, `created_at`) VALUES
(1, 1, 'Ralph', 'Ramos', 'Ralph Ramos Jr.', 21, 21, 'Male', 'Male', 'Carles', 'Iloilo', 'Brgy. Poblacion', '09345789658', 'main_booker', 1, '2025-01-26 14:20:00'),
(2, 2, 'Maria', 'Santos', 'Maria Santos', 25, 25, 'Female', 'Female', 'Estancia', 'Iloilo', 'Brgy. Centro', '09181234567', 'main_booker', 1, '2025-01-27 09:15:00'),
(3, 3, 'John', 'Dela Cruz', 'John Dela Cruz', 30, 30, 'Male', 'Male', 'Balasan', 'Iloilo', 'Brgy. Poblacion', '09175553333', 'main_booker', 1, '2025-01-25 16:45:00'),
(4, 4, 'Ana', 'Villanueva', 'Ana Villanueva', 28, 28, 'Female', 'Female', 'Carles', 'Iloilo', 'Brgy. Centro', '09267891234', 'main_booker', 1, '2025-01-26 11:20:00'),
(5, 5, 'Roberto', 'Fernandez', 'Roberto Fernandez', 35, 35, 'Male', 'Male', 'Iloilo City', 'Iloilo', 'Jaro District', '09356789012', 'main_booker', 1, '2025-01-24 13:30:00');

-- --------------------------------------------------------

--
-- Table structure for table `booking_logs`
--

CREATE TABLE `booking_logs` (
  `log_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `date_of_booking` date NOT NULL,
  `time` time NOT NULL,
  `booking_id` int(11) NOT NULL,
  `boat` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `admin_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `booking_logs`
--

INSERT INTO `booking_logs` (`log_id`, `user_id`, `date_of_booking`, `time`, `booking_id`, `boat`, `price`, `admin_id`) VALUES
(1, 1, '2025-01-09', '09:28:16', 1, 'Boat 1', 2000.00, 1),
(2, 2, '2025-02-02', '10:00:00', 2, 'Boat 2', 3500.00, 3);

-- --------------------------------------------------------

--
-- Table structure for table `booking_status_logs`
--

CREATE TABLE `booking_status_logs` (
  `log_id` int(11) NOT NULL,
  `booking_id` int(11) NOT NULL,
  `old_status` enum('pending','confirmed','cancelled') NOT NULL,
  `new_status` enum('pending','confirmed','cancelled') NOT NULL,
  `reason` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `customer_id` int(11) NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `age` int(11) DEFAULT NULL,
  `sex` varchar(10) DEFAULT NULL,
  `contact_number` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `notification_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(50) DEFAULT 'general',
  `reference_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `is_read` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `activity_logs_ibfk_1` (`admin_id`);

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`admin_id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `boats`
--
ALTER TABLE `boats`
  ADD PRIMARY KEY (`boat_id`);

--
-- Indexes for table `bookings`
--
ALTER TABLE `bookings`
  ADD PRIMARY KEY (`booking_id`);

--
-- Indexes for table `booking_passengers`
--
ALTER TABLE `booking_passengers`
  ADD PRIMARY KEY (`passenger_id`),
  ADD KEY `booking_id` (`booking_id`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`customer_id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`notification_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `activity_logs`
--
ALTER TABLE `activity_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=43;

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `admin_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `boats`
--
ALTER TABLE `boats`
  MODIFY `boat_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `bookings`
--
ALTER TABLE `bookings`
  MODIFY `booking_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `booking_passengers`
--
ALTER TABLE `booking_passengers`
  MODIFY `passenger_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `customer_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `notification_id` int(11) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
