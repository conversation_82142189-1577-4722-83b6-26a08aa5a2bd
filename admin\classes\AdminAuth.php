<?php

class AdminAuth {
    private $db;
    private $security;

    public function __construct($db, $security) {
        $this->db = $db;
        $this->security = $security;
    }

    public function login($email, $password) {
        try {
            $stmt = $this->db->prepare("SELECT admin_id, first_name, last_name, email, password FROM admins WHERE email = ? AND status = 'active'");
            $stmt->execute([$email]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($admin && password_verify($password, $admin['password'])) {
                // Update last login
                $stmt = $this->db->prepare("UPDATE admins SET last_login = NOW() WHERE admin_id = ?");
                $stmt->execute([$admin['admin_id']]);

                // Set session variables
                $_SESSION['admin_id'] = $admin['admin_id'];
                $_SESSION['user_id'] = $admin['admin_id']; // For compatibility with existing code
                $_SESSION['full_name'] = $admin['first_name'] . ' ' . $admin['last_name'];
                $_SESSION['email'] = $admin['email'];
                $_SESSION['user_type'] = 'admin';
                $_SESSION['last_login'] = date('Y-m-d H:i:s');

                // Log successful login
                $this->security->logActivity($admin['admin_id'], 'login', 'Successful login');

                return true;
            }

            // Log failed login attempt
            $this->security->logActivity(null, 'login_failed', 'Failed login attempt for email: ' . $email);
            return false;
        } catch (PDOException $e) {
            error_log("Login error: " . $e->getMessage());
            return false;
        }
    }

    public function logout() {
        if (isset($_SESSION['admin_id'])) {
            $this->security->logActivity($_SESSION['admin_id'], 'logout', 'User logged out');
        }
        session_destroy();
        return true;
    }

    public function isLoggedIn() {
        return isset($_SESSION['admin_id']) && isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'admin';
    }

    public function getCurrentAdmin() {
        if (!$this->isLoggedIn()) {
            return null;
        }

        try {
            $stmt = $this->db->prepare("SELECT admin_id, first_name, last_name, email, phone, profile_image, last_login, created_at FROM admins WHERE admin_id = ?");
            $stmt->execute([$_SESSION['admin_id']]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting current admin: " . $e->getMessage());
            return null;
        }
    }

    public function changePassword($currentPassword, $newPassword) {
        if (!$this->isLoggedIn()) {
            return false;
        }

        try {
            // Verify current password
            $stmt = $this->db->prepare("SELECT password FROM admins WHERE admin_id = ?");
            $stmt->execute([$_SESSION['admin_id']]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($admin && password_verify($currentPassword, $admin['password'])) {
                // Update to new password
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                $stmt = $this->db->prepare("UPDATE admins SET password = ? WHERE admin_id = ?");
                $stmt->execute([$hashedPassword, $_SESSION['admin_id']]);

                // Log password change
                $this->security->logActivity($_SESSION['admin_id'], 'password_change', 'Password changed successfully');
                return true;
            }

            return false;
        } catch (PDOException $e) {
            error_log("Error changing password: " . $e->getMessage());
            return false;
        }
    }

    public function resetPassword($email) {
        try {
            $stmt = $this->db->prepare("SELECT admin_id FROM admins WHERE email = ? AND status = 'active'");
            $stmt->execute([$email]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($admin) {
                // Generate reset token
                $token = bin2hex(random_bytes(32));
                $expiry = date('Y-m-d H:i:s', strtotime('+1 hour'));

                // Add reset_token and reset_token_expiry columns if they don't exist
                $this->db->exec("ALTER TABLE admins ADD COLUMN IF NOT EXISTS reset_token VARCHAR(100) DEFAULT NULL");
                $this->db->exec("ALTER TABLE admins ADD COLUMN IF NOT EXISTS reset_token_expiry DATETIME DEFAULT NULL");

                $stmt = $this->db->prepare("UPDATE admins SET reset_token = ?, reset_token_expiry = ? WHERE admin_id = ?");
                $stmt->execute([$token, $expiry, $admin['admin_id']]);

                // Log password reset request
                $this->security->logActivity($admin['admin_id'], 'password_reset_request', 'Password reset requested');

                return $token;
            }

            return false;
        } catch (PDOException $e) {
            error_log("Error resetting password: " . $e->getMessage());
            return false;
        }
    }

    public function verifyResetToken($token) {
        try {
            $stmt = $this->db->prepare("SELECT admin_id FROM admins WHERE reset_token = ? AND reset_token_expiry > NOW() AND status = 'active'");
            $stmt->execute([$token]);
            return $stmt->fetch(PDO::FETCH_ASSOC) !== false;
        } catch (PDOException $e) {
            error_log("Error verifying reset token: " . $e->getMessage());
            return false;
        }
    }

    public function setNewPassword($token, $newPassword) {
        try {
            $stmt = $this->db->prepare("SELECT admin_id FROM admins WHERE reset_token = ? AND reset_token_expiry > NOW() AND status = 'active'");
            $stmt->execute([$token]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($admin) {
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                $stmt = $this->db->prepare("UPDATE admins SET password = ?, reset_token = NULL, reset_token_expiry = NULL WHERE admin_id = ?");
                $stmt->execute([$hashedPassword, $admin['admin_id']]);

                // Log password reset
                $this->security->logActivity($admin['admin_id'], 'password_reset', 'Password reset completed');

                return true;
            }

            return false;
        } catch (PDOException $e) {
            error_log("Error setting new password: " . $e->getMessage());
            return false;
        }
    }
} 