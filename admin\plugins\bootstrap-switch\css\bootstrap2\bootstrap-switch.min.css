/**
  * bootstrap-switch - Turn checkboxes and radio buttons into toggle switches.
  *
  * @version v3.3.4
  * @homepage https://bttstrp.github.io/bootstrap-switch
  * <AUTHOR> <<EMAIL>> (http://larentis.eu)
  * @license Apache-2.0
  */

.clearfix:after,.clearfix:before{display:table;content:"";line-height:0}.clearfix:after{clear:both}.hide-text{font:0/0 a;color:transparent;text-shadow:none;background-color:transparent;border:0}.input-block-level{display:block;width:100%;min-height:30px;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.bootstrap-switch{display:inline-block;direction:ltr;cursor:pointer;-webkit-border-radius:5px;-moz-border-radius:5px;border-radius:5px;border:1px solid;border-color:rgba(0,0,0,.1) rgba(0,0,0,.1) rgba(0,0,0,.25);position:relative;text-align:left;overflow:hidden;line-height:8px;z-index:0;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;-o-user-select:none;user-select:none;vertical-align:middle;-webkit-transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s;-moz-transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s;-o-transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s;transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s}.bootstrap-switch .bootstrap-switch-handle-on,.bootstrap-switch.bootstrap-switch-inverse .bootstrap-switch-handle-off{-webkit-border-top-left-radius:4px;-moz-border-radius-topleft:4px;-webkit-border-bottom-left-radius:4px;-moz-border-radius-bottomleft:4px}.bootstrap-switch .bootstrap-switch-container{display:inline-block;top:0;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;-webkit-transform:translate3d(0,0,0);-moz-transform:translate3d(0,0,0);-o-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}.bootstrap-switch .bootstrap-switch-handle-off,.bootstrap-switch .bootstrap-switch-handle-on,.bootstrap-switch .bootstrap-switch-label{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;cursor:pointer;display:inline-block!important;font-size:14px;line-height:20px;padding:4px 8px}.bootstrap-switch .bootstrap-switch-handle-off,.bootstrap-switch .bootstrap-switch-handle-on{text-align:center;z-index:1}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,.25);background-color:#005fcc;background-image:-moz-linear-gradient(top,#04c,#08c);background-image:-webkit-gradient(linear,0 0,0 100%,from(#04c),to(#08c));background-image:-webkit-linear-gradient(top,#04c,#08c);background-image:-o-linear-gradient(top,#04c,#08c);background-image:linear-gradient(to bottom,#04c,#08c);background-repeat:repeat-x;border-color:#08c #08c #005580;border-color:rgba(0,0,0,.1) rgba(0,0,0,.1) rgba(0,0,0,.25);filter:progid:DXImageTransform.Microsoft.gradient(enabled=false)}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary.active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary.disabled,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary:active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary:focus,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary:hover,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary[disabled],.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary.active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary.disabled,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary:active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary:focus,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary:hover,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary[disabled]{color:#fff;background-color:#08c}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary.active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary:active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary.active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary:active{background-color:#069\9}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,.25);background-color:#41a7c5;background-image:-moz-linear-gradient(top,#2f96b4,#5bc0de);background-image:-webkit-gradient(linear,0 0,0 100%,from(#2f96b4),to(#5bc0de));background-image:-webkit-linear-gradient(top,#2f96b4,#5bc0de);background-image:-o-linear-gradient(top,#2f96b4,#5bc0de);background-image:linear-gradient(to bottom,#2f96b4,#5bc0de);background-repeat:repeat-x;border-color:#5bc0de #5bc0de #28a1c5;border-color:rgba(0,0,0,.1) rgba(0,0,0,.1) rgba(0,0,0,.25);filter:progid:DXImageTransform.Microsoft.gradient(enabled=false)}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info.active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info.disabled,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info:active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info:focus,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info:hover,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info[disabled],.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info.active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info.disabled,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info:active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info:focus,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info:hover,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info[disabled]{color:#fff;background-color:#5bc0de}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info.active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info:active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info.active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info:active{background-color:#31b0d5\9}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,.25);background-color:#58b058;background-image:-moz-linear-gradient(top,#51a351,#62c462);background-image:-webkit-gradient(linear,0 0,0 100%,from(#51a351),to(#62c462));background-image:-webkit-linear-gradient(top,#51a351,#62c462);background-image:-o-linear-gradient(top,#51a351,#62c462);background-image:linear-gradient(to bottom,#51a351,#62c462);background-repeat:repeat-x;border-color:#62c462 #62c462 #3b9e3b;border-color:rgba(0,0,0,.1) rgba(0,0,0,.1) rgba(0,0,0,.25);filter:progid:DXImageTransform.Microsoft.gradient(enabled=false)}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success.active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success.disabled,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success:active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success:focus,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success:hover,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success[disabled],.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success.active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success.disabled,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success:active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success:focus,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success:hover,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success[disabled]{color:#fff;background-color:#62c462}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success.active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success:active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success.active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success:active{background-color:#42b142\9}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,.25);background-color:#f9a123;background-image:-moz-linear-gradient(top,#f89406,#fbb450);background-image:-webkit-gradient(linear,0 0,0 100%,from(#f89406),to(#fbb450));background-image:-webkit-linear-gradient(top,#f89406,#fbb450);background-image:-o-linear-gradient(top,#f89406,#fbb450);background-image:linear-gradient(to bottom,#f89406,#fbb450);background-repeat:repeat-x;border-color:#fbb450 #fbb450 #f89406;border-color:rgba(0,0,0,.1) rgba(0,0,0,.1) rgba(0,0,0,.25);filter:progid:DXImageTransform.Microsoft.gradient(enabled=false)}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning.active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning.disabled,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning:active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning:focus,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning:hover,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning[disabled],.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning.active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning.disabled,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning:active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning:focus,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning:hover,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning[disabled]{color:#fff;background-color:#fbb450}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning.active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning:active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning.active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning:active{background-color:#fa9f1e\9}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger{color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,.25);background-color:#d14641;background-image:-moz-linear-gradient(top,#bd362f,#ee5f5b);background-image:-webkit-gradient(linear,0 0,0 100%,from(#bd362f),to(#ee5f5b));background-image:-webkit-linear-gradient(top,#bd362f,#ee5f5b);background-image:-o-linear-gradient(top,#bd362f,#ee5f5b);background-image:linear-gradient(to bottom,#bd362f,#ee5f5b);background-repeat:repeat-x;border-color:#ee5f5b #ee5f5b #e51d18;border-color:rgba(0,0,0,.1) rgba(0,0,0,.1) rgba(0,0,0,.25);filter:progid:DXImageTransform.Microsoft.gradient(enabled=false)}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger.active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger.disabled,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger:active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger:focus,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger:hover,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger[disabled],.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger.active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger.disabled,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger:active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger:focus,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger:hover,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger[disabled]{color:#fff;background-color:#ee5f5b}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger.active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger:active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger.active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger:active{background-color:#e9322d\9}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default{color:#333;text-shadow:0 1px 1px rgba(255,255,255,.75);background-color:#f0f0f0;background-image:-moz-linear-gradient(top,#e6e6e6,#fff);background-image:-webkit-gradient(linear,0 0,0 100%,from(#e6e6e6),to(#fff));background-image:-webkit-linear-gradient(top,#e6e6e6,#fff);background-image:-o-linear-gradient(top,#e6e6e6,#fff);background-image:linear-gradient(to bottom,#e6e6e6,#fff);background-repeat:repeat-x;border-color:#fff #fff #d9d9d9;border-color:rgba(0,0,0,.1) rgba(0,0,0,.1) rgba(0,0,0,.25);filter:progid:DXImageTransform.Microsoft.gradient(enabled=false)}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default.active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default.disabled,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default:active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default:focus,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default:hover,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default[disabled],.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default.active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default.disabled,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default:active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default:focus,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default:hover,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default[disabled]{color:#333;background-color:#fff}.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default.active,.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default:active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default.active,.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default:active{background-color:#e6e6e6\9}.bootstrap-switch .bootstrap-switch-label{text-align:center;margin-top:-1px;margin-bottom:-1px;z-index:100;border-left:1px solid #ccc;border-right:1px solid #ccc;color:#333;text-shadow:0 -1px 0 rgba(0,0,0,.25);background-color:#f5f5f5;background-image:-moz-linear-gradient(top,#fff,#e6e6e6);background-image:-webkit-gradient(linear,0 0,0 100%,from(#fff),to(#e6e6e6));background-image:-webkit-linear-gradient(top,#fff,#e6e6e6);background-image:-o-linear-gradient(top,#fff,#e6e6e6);background-image:linear-gradient(to bottom,#fff,#e6e6e6);background-repeat:repeat-x;border-color:#e6e6e6 #e6e6e6 #bfbfbf;border-color:rgba(0,0,0,.1) rgba(0,0,0,.1) rgba(0,0,0,.25);filter:progid:DXImageTransform.Microsoft.gradient(enabled=false)}.bootstrap-switch .bootstrap-switch-label.active,.bootstrap-switch .bootstrap-switch-label.disabled,.bootstrap-switch .bootstrap-switch-label:active,.bootstrap-switch .bootstrap-switch-label:focus,.bootstrap-switch .bootstrap-switch-label:hover,.bootstrap-switch .bootstrap-switch-label[disabled]{color:#333;background-color:#e6e6e6}.bootstrap-switch .bootstrap-switch-label.active,.bootstrap-switch .bootstrap-switch-label:active{background-color:#ccc\9}.bootstrap-switch span::before{content:"\200b"}.bootstrap-switch .bootstrap-switch-handle-on{border-top-left-radius:4px;border-bottom-left-radius:4px}.bootstrap-switch .bootstrap-switch-handle-off{-webkit-border-top-right-radius:4px;-moz-border-radius-topright:4px;border-top-right-radius:4px;-webkit-border-bottom-right-radius:4px;-moz-border-radius-bottomright:4px;border-bottom-right-radius:4px}.bootstrap-switch input[type=radio],.bootstrap-switch input[type=checkbox]{position:absolute!important;top:0;left:0;opacity:0;filter:alpha(opacity=0);z-index:-1;visibility:hidden}.bootstrap-switch input[type=radio].form-control,.bootstrap-switch input[type=checkbox].form-control{height:auto}.bootstrap-switch.bootstrap-switch-mini{min-width:71px}.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-handle-off,.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-handle-on,.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-label{padding:3px 6px;font-size:10px;line-height:9px}.bootstrap-switch.bootstrap-switch-small{min-width:79px}.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-handle-off,.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-handle-on,.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-label{padding:3px 6px;font-size:12px;line-height:18px}.bootstrap-switch.bootstrap-switch-large{min-width:120px}.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-handle-off,.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-handle-on,.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-label{padding:9px 12px;font-size:16px;line-height:normal}.bootstrap-switch.bootstrap-switch-disabled,.bootstrap-switch.bootstrap-switch-indeterminate,.bootstrap-switch.bootstrap-switch-readonly{cursor:default!important}.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-handle-off,.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-handle-on,.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-label,.bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-handle-off,.bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-handle-on,.bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-label,.bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-handle-off,.bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-handle-on,.bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-label{opacity:.5;filter:alpha(opacity=50);cursor:default!important}.bootstrap-switch.bootstrap-switch-animate .bootstrap-switch-container{-webkit-transition:margin-left .5s;-moz-transition:margin-left .5s;-o-transition:margin-left .5s;transition:margin-left .5s}.bootstrap-switch.bootstrap-switch-inverse .bootstrap-switch-handle-on{-webkit-border-top-left-radius:0;-moz-border-radius-topleft:0;-webkit-border-bottom-left-radius:0;-moz-border-radius-bottomleft:0;-webkit-border-top-right-radius:4px;-moz-border-radius-topright:4px;-webkit-border-bottom-right-radius:4px;-moz-border-radius-bottomright:4px;border-radius:0 4px 4px 0}.bootstrap-switch.bootstrap-switch-inverse .bootstrap-switch-handle-off{-webkit-border-top-right-radius:0;-moz-border-radius-topright:0;-webkit-border-bottom-right-radius:0;-moz-border-radius-bottomright:0;border-radius:4px 0 0 4px}.bootstrap-switch.bootstrap-switch-focused{border-color:rgba(82,168,236,.8);outline:0;outline:dotted thin\9;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(82,168,236,.6);-moz-box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(82,168,236,.6);box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(82,168,236,.6)}.bootstrap-switch.bootstrap-switch-inverse.bootstrap-switch-off .bootstrap-switch-label,.bootstrap-switch.bootstrap-switch-on .bootstrap-switch-label{-webkit-border-top-right-radius:4px;-moz-border-radius-topright:4px;border-top-right-radius:4px;-webkit-border-bottom-right-radius:4px;-moz-border-radius-bottomright:4px;border-bottom-right-radius:4px}.bootstrap-switch.bootstrap-switch-inverse.bootstrap-switch-on .bootstrap-switch-label,.bootstrap-switch.bootstrap-switch-off .bootstrap-switch-label{-webkit-border-top-left-radius:4px;-moz-border-radius-topleft:4px;border-top-left-radius:4px;-webkit-border-bottom-left-radius:4px;-moz-border-radius-bottomleft:4px;border-bottom-left-radius:4px}