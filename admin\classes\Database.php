<?php
/**
 * Database Class
 *
 * A secure database connection and query handler class using PDO
 */
class Database {
    private $host;
    private $db_name;
    private $username;
    private $password;
    private $conn;

    /**
     * Constructor - initializes database connection parameters
     */
    public function __construct() {
        // Include the database configuration file
        $config_file = __DIR__ . '/../php/config/db_config.php';

        if (file_exists($config_file)) {
            require_once $config_file;

            // Use the constants defined in the config file
            if (defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER') && defined('DB_PASS')) {
                $this->host = DB_HOST;
                $this->db_name = DB_NAME;
                $this->username = DB_USER;
                $this->password = DB_PASS;
                return;
            }
        }

        // Fallback to default values if config file is not found or constants not defined
        $this->host = 'localhost';
        $this->db_name = 'booking_system';
        $this->username = 'root';
        $this->password = '';
    }

    /**
     * Establishes a connection to the database
     *
     * @return PDO The database connection
     * @throws Exception If connection fails
     */
    public function getConnection() {
        $this->conn = null;

        try {
            // First try to connect without database to check if MySQL is running
            $this->conn = new PDO(
                "mysql:host=" . $this->host,
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Check if database exists using prepared statement to prevent SQL injection
            $stmt = $this->conn->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
            $stmt->execute([$this->db_name]);

            if (!$stmt->fetch()) {
                // Database doesn't exist, create it
                $this->conn->exec("CREATE DATABASE IF NOT EXISTS `" . $this->db_name . "`");
            }

            // Now connect to the specific database
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false // Use real prepared statements
                ]
            );

            // Create tables if they don't exist
            $this->createTables();

            return $this->conn;
        } catch(PDOException $e) {
            error_log("Database connection error: " . $e->getMessage());
            throw new Exception("Database connection failed. Please try again later.");
        }
    }

    /**
     * Creates database tables if they don't exist
     */
    private function createTables() {
        // Create users table
        $this->conn->exec("CREATE TABLE IF NOT EXISTS users (
            user_id INT PRIMARY KEY AUTO_INCREMENT,
            full_name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            phone VARCHAR(20),
            address TEXT,
            user_type ENUM('admin', 'user') NOT NULL,
            is_verified BOOLEAN DEFAULT FALSE,
            profile_image VARCHAR(255),
            remember_token VARCHAR(100),
            remember_token_expiry DATETIME,
            reset_token VARCHAR(100),
            reset_token_expiry DATETIME,
            last_login DATETIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // Create customers table
        $this->conn->exec("CREATE TABLE IF NOT EXISTS customers (
            customer_id INT PRIMARY KEY AUTO_INCREMENT,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            email VARCHAR(100) NOT NULL UNIQUE,
            phone VARCHAR(20) NOT NULL,
            address TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // Create bookings table
        $this->conn->exec("CREATE TABLE IF NOT EXISTS bookings (
            booking_id INT PRIMARY KEY AUTO_INCREMENT,
            customer_id INT NOT NULL,
            booking_date DATE NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL,
            payment_status ENUM('pending', 'paid', 'cancelled', 'refunded') DEFAULT 'pending',
            booking_status ENUM('pending', 'confirmed', 'completed', 'cancelled') DEFAULT 'pending',
            special_requests TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
        )");

        // Create payments table
        $this->conn->exec("CREATE TABLE IF NOT EXISTS payments (
            payment_id INT PRIMARY KEY AUTO_INCREMENT,
            booking_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_date DATETIME NOT NULL,
            payment_method ENUM('cash', 'credit_card', 'bank_transfer', 'online') NOT NULL,
            transaction_id VARCHAR(100),
            status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (booking_id) REFERENCES bookings(booking_id)
        )");

        // Insert sample customer data if customers table is empty
        $stmt = $this->conn->query("SELECT COUNT(*) as count FROM customers");
        $row = $stmt->fetch();

        if ($row['count'] == 0) {
            $sampleData = [
                ['John', 'Doe', '<EMAIL>', '***********', '123 Main St, City'],
                ['Jane', 'Smith', '<EMAIL>', '***********', '456 Oak Ave, Town'],
                ['Robert', 'Johnson', '<EMAIL>', '***********', '789 Pine Rd, Village']
            ];

            $insertStmt = $this->conn->prepare("INSERT INTO customers (first_name, last_name, email, phone, address) VALUES (?, ?, ?, ?, ?)");

            foreach ($sampleData as $customer) {
                $insertStmt->execute($customer);
            }
        }
    }

    /**
     * Executes a SQL query with parameters
     *
     * @param string $sql The SQL query to execute
     * @param array $params Parameters for the query
     * @return PDOStatement|false The statement object or false on failure
     */
    public function query($sql, $params = []) {
        if (!$this->conn) {
            $this->conn = $this->getConnection();
        }

        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch(PDOException $e) {
            error_log("Query Error: " . $e->getMessage() . " - SQL: " . $sql);
            return false;
        }
    }

    /**
     * Fetches a single row from the database
     *
     * @param string $sql The SQL query
     * @param array $params Parameters for the query
     * @return array|false The result row or false on failure
     */
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetch() : false;
    }

    /**
     * Fetches all rows from the database
     *
     * @param string $sql The SQL query
     * @param array $params Parameters for the query
     * @return array|false The result rows or false on failure
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetchAll() : false;
    }

    /**
     * Inserts data into a table
     *
     * @param string $table The table name
     * @param array $data Associative array of column => value pairs
     * @return PDOStatement|false The statement object or false on failure
     */
    public function insert($table, $data) {
        $fields = array_keys($data);
        $values = array_values($data);
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';

        $sql = "INSERT INTO `{$table}` (`" . implode('`, `', $fields) . "`) VALUES ({$placeholders})";

        return $this->query($sql, $values);
    }

    /**
     * Updates data in a table
     *
     * @param string $table The table name
     * @param array $data Associative array of column => value pairs to update
     * @param string $where The WHERE clause
     * @param array $whereParams Parameters for the WHERE clause
     * @return PDOStatement|false The statement object or false on failure
     */
    public function update($table, $data, $where, $whereParams = []) {
        $fields = array_keys($data);
        $values = array_values($data);
        $setClause = implode(' = ?, `', $fields) . '` = ?';

        $sql = "UPDATE `{$table}` SET `{$setClause}` WHERE {$where}";

        $params = array_merge($values, $whereParams);
        return $this->query($sql, $params);
    }

    /**
     * Deletes data from a table
     *
     * @param string $table The table name
     * @param string $where The WHERE clause
     * @param array $params Parameters for the WHERE clause
     * @return PDOStatement|false The statement object or false on failure
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM `{$table}` WHERE {$where}";
        return $this->query($sql, $params);
    }

    /**
     * Gets the ID of the last inserted row
     *
     * @return string|null The last insert ID or null if not available
     */
    public function lastInsertId() {
        return $this->conn ? $this->conn->lastInsertId() : null;
    }
}