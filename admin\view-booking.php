<?php
session_start();
include('includes/config.php');
if(strlen($_SESSION['aid'])==0)  {
    header('location:../../../../php/pages/admin-login.php');
    exit();
}
// Get and validate booking ID
$booking_id_raw = isset($_GET['id']) ? $_GET['id'] : '';

// Check if booking ID is provided
if(!isset($_GET['id']) || $_GET['id'] === '') {
    echo '<div class="alert alert-danger">Please provide a booking ID.</div>';
    exit();
}

// Check if booking ID is numeric
if(!is_numeric($_GET['id'])) {
    echo '<div class="alert alert-danger">Booking ID must be a number.</div>';
    exit();
}

// Convert to integer after validation
$booking_id = intval($_GET['id']);

// Check if booking ID is positive
if($booking_id <= 0) {
    echo '<div class="alert alert-danger">Invalid booking ID: Value must be a positive number.</div>';
    exit();
}

// Check if required columns exist in the bookings table
$check_columns = [
    'drop_off_location' => false,
    'emergency_name' => false,
    'emergency_number' => false
];

foreach (array_keys($check_columns) as $column) {
    $check_column_query = "SHOW COLUMNS FROM bookings LIKE '$column'";
    $check_column_result = $con->query($check_column_query);
    $check_columns[$column] = $check_column_result->num_rows > 0;
}

// Build the query based on which columns exist
$sql = "SELECT
    b.booking_id,
    b.booking_code,
    b.customer_id,
    b.boat_id,
    b.destination_id,
    b.start_date,
    b.end_date,
    b.booking_time,
    b.no_of_pax,
    b.environmental_fee,
    b.payment_method,
    b.total,
    b.booking_status,";

// Add drop_off_location only if the column exists
if ($check_columns['drop_off_location']) {
    $sql .= "
    b.drop_off_location,";
} else {
    $sql .= "
    '' AS drop_off_location,";
}

// Add emergency_name only if the column exists
if ($check_columns['emergency_name']) {
    $sql .= "
    b.emergency_name,";
} else {
    $sql .= "
    '' AS emergency_name,";
}

// Add emergency_number only if the column exists
if ($check_columns['emergency_number']) {
    $sql .= "
    b.emergency_number,";
} else {
    $sql .= "
    '' AS emergency_number,";
}

$sql .= "
    b.first_name,
    b.last_name,
    b.age,
    b.sex,
    b.contact_number,
    b.email,
    b.address,
    bt.name AS boat_name,
    bt.price_per_day,
    d.name AS destination_name
FROM bookings b
LEFT JOIN boats bt ON b.boat_id = bt.boat_id
LEFT JOIN destinations d ON b.destination_id = d.destination_id
WHERE b.booking_id = ?";

$stmt = $con->prepare($sql);
$stmt->bind_param('i', $booking_id);
$stmt->execute();
$result = $stmt->get_result();

if($result->num_rows == 0) {
    echo '<div class="alert alert-danger">Booking not found.</div>';
    exit();
}

$row = $result->fetch_assoc();

// Get passenger manifest details (DEAN'S REQUIREMENT)
$passenger_query = "SELECT
    passenger_id, first_name, last_name, age, sex, complete_address,
    contact_number, passenger_type, is_main_contact
    FROM booking_passengers
    WHERE booking_id = ?
    ORDER BY is_main_contact DESC, passenger_id ASC";

$passenger_stmt = $con->prepare($passenger_query);
$passengers = [];
if ($passenger_stmt) {
    $passenger_stmt->bind_param("i", $booking_id);
    $passenger_stmt->execute();
    $passenger_result = $passenger_stmt->get_result();

    while ($passenger = $passenger_result->fetch_assoc()) {
        $passengers[] = $passenger;
    }
    $passenger_stmt->close();
} else {
    error_log("Failed to prepare passenger query: " . $con->error);
}

// Debug information
error_log("Booking Data: " . print_r($row, true));
error_log("Passengers Data: " . print_r($passengers, true));

function formatPeso($amt) {
    return '&#8369; ' . number_format($amt, 2);
}

// Format dates with better error handling
try {
    if (!empty($row['start_date']) && $row['start_date'] != '0000-00-00 00:00:00') {
        $start_timestamp = strtotime($row['start_date']);
        if ($start_timestamp !== false) {
            $start_date = date('F d, Y', $start_timestamp);
        } else {
            $start_date = 'Not Available';
        }
    } else {
        $start_date = 'Not Available';
    }
} catch (Exception $e) {
    error_log("Error formatting start_date: " . $e->getMessage());
    $start_date = 'Not Available';
}

try {
    if (!empty($row['end_date']) && $row['end_date'] != '0000-00-00 00:00:00') {
        $end_timestamp = strtotime($row['end_date']);
        if ($end_timestamp !== false) {
            $end_date = date('F d, Y', $end_timestamp);
        } else {
            $end_date = 'Not Available';
        }
    } else {
        $end_date = 'Not Available';
    }
} catch (Exception $e) {
    error_log("Error formatting end_date: " . $e->getMessage());
    $end_date = 'Not Available';
}

try {
    if (!empty($row['booking_time']) && $row['booking_time'] != '00:00:00') {
        $time_timestamp = strtotime($row['booking_time']);
        if ($time_timestamp !== false) {
            $booking_time = date('F d, Y \a\t h:i:s A', $time_timestamp);
        } else {
            $booking_time = 'Not Available';
        }
    } else {
        $booking_time = 'Not Available';
    }
} catch (Exception $e) {
    error_log("Error formatting booking_time: " . $e->getMessage());
    $booking_time = 'Not Available';
}

// Calculate duration
$duration = '';
if (!empty($row['start_date']) && !empty($row['end_date'])) {
    $start = new DateTime($row['start_date']);
    $end = new DateTime($row['end_date']);
    $interval = $start->diff($end);
    $duration = $interval->format('%d days %h hours %i minutes');
} else {
    $duration = 'Not set';
}

// Format boat price
$boat_price = !empty($row['price_per_day']) ? formatPeso($row['price_per_day']) . ' per day' : 'Not set';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Booking Details</title>
    <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
    <link rel="stylesheet" href="dist/css/adminlte.min.css">
    <link rel="stylesheet" href="plugins/bootstrap/css/bootstrap.min.css">
</head>
<body>
<?php include_once('includes/navbar.php'); ?>
<?php include_once('includes/sidebar.php'); ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Booking Details</h1>
                </div>
            </div>
        </div>
    </section>
    <section class="content">
        <div class="container-fluid">
            <div class="modal fade" id="bookingModal" tabindex="-1" role="dialog" aria-labelledby="bookingModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header bg-info">
                            <h5 class="modal-title" id="bookingModalLabel"><i class="fas fa-info-circle"></i> Booking Information</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="$('.modal-backdrop').remove();">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <!-- Main Booker Information -->
                            <div class="card mb-3">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-user"></i> Main Booker Information</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered table-striped">
                                        <tr><th>First Name:</th><td><?= htmlspecialchars($row['first_name'] ?? 'Not set') ?></td></tr>
                                        <tr><th>Last Name:</th><td><?= htmlspecialchars($row['last_name'] ?? 'Not set') ?></td></tr>
                                        <tr><th>Age:</th><td><?= htmlspecialchars($row['age'] ?? '25') ?></td></tr>
                                        <tr><th>Sex:</th><td><?= htmlspecialchars($row['sex'] ?? 'Not Specified') ?></td></tr>
                                        <tr><th>Contact Number:</th><td><?= htmlspecialchars($row['contact_number'] ?? 'Not set') ?></td></tr>
                                        <tr><th>Email:</th><td><?= htmlspecialchars($row['email'] ?? 'Not set') ?></td></tr>
                                        <tr><th>Address:</th><td><?= htmlspecialchars($row['address'] ?? 'Not set') ?></td></tr>
                                        <tr><th>Emergency Contact Name:</th><td><?= htmlspecialchars($row['emergency_name'] ?? 'Emergency Contact') ?></td></tr>
                                        <tr><th>Emergency Contact Number:</th><td><?= htmlspecialchars($row['emergency_number'] ?? '09123456789') ?></td></tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Tour Information -->
                            <div class="card mb-3">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-map-marker-alt"></i> Tour Information</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered table-striped">
                                        <tr><th>Tour Destination:</th><td><?= htmlspecialchars($row['destination_name'] ?? 'Gigantes Island') ?></td></tr>
                                        <tr><th>Drop-off Location:</th><td><?= htmlspecialchars($row['drop_off_location'] ?? 'Estancia') ?></td></tr>
                                        <tr><th>Number of Pax:</th><td><?= htmlspecialchars($row['no_of_pax'] ?? '1') ?></td></tr>
                                        <tr><th>Start Date:</th><td><?= $start_date ?></td></tr>
                                        <tr><th>End Date:</th><td><?= $end_date ?></td></tr>
                                        <tr><th>Duration Time:</th><td><?= $duration ?></td></tr>
                                        <tr>
                                            <th>Booking Time:</th>
                                            <td>
                                                <span id="bookingTimeDisplay"><?= $booking_time ?></span>
                                                <?php if($booking_time == 'Not Available' || $booking_time == 'Not set'): ?>
                                                <button type="button" class="btn btn-sm btn-primary ml-2" id="editBookingTimeBtn">
                                                    <i class="fas fa-edit"></i> Set Time
                                                </button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr><th>Selected Boat:</th><td><?= htmlspecialchars($row['boat_name'] ?? 'Assigned by Tourism Office') ?> <span class="text-muted small">(The Tourism Office will arrange the boat for you)</span></td></tr>
                                        <tr><th>Boat Price:</th><td><?= $boat_price ?></td></tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Passenger Manifest Section (DEAN'S REQUIREMENT) -->
                            <div class="card mb-3">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0"><i class="fas fa-users"></i> Passenger Manifest (Government Requirement)</h6>
                                    <small class="text-muted">Required by Tourism Office, Marina, and Coast Guard for safety and emergency response</small>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($passengers)): ?>
                                        <div class="table-responsive">
                                            <table class="table table-sm table-bordered">
                                                <thead class="thead-light">
                                                    <tr>
                                                        <th>#</th>
                                                        <th>Name</th>
                                                        <th>Age</th>
                                                        <th>Sex</th>
                                                        <th>Address</th>
                                                        <th>Contact</th>
                                                        <th>Type</th>
                                                        <th>Role</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($passengers as $index => $passenger): ?>
                                                        <?php
                                                        $rowClass = $passenger['is_main_contact'] == 1 ? 'table-primary' : '';
                                                        $passengerType = $passenger['passenger_type'] ?? 'regular';
                                                        $typeClass = '';

                                                        switch($passengerType) {
                                                            case 'regular': $typeClass = 'badge-success'; break;
                                                            case 'discounted': $typeClass = 'badge-info'; break;
                                                            case 'children': $typeClass = 'badge-warning'; break;
                                                            case 'infants': $typeClass = 'badge-secondary'; break;
                                                            default: $typeClass = 'badge-light';
                                                        }

                                                        // Use complete address from form
                                                        $fullAddress = $passenger['complete_address'] ?? 'N/A';
                                                        ?>
                                                        <tr class="<?= $rowClass ?>">
                                                            <td><?= $index + 1 ?></td>
                                                            <td><strong><?= htmlspecialchars(($passenger['first_name'] ?? '') . ' ' . ($passenger['last_name'] ?? '')) ?></strong></td>
                                                            <td><?= htmlspecialchars($passenger['age'] ?? 'N/A') ?></td>
                                                            <td><?= htmlspecialchars($passenger['sex'] ?? 'N/A') ?></td>
                                                            <td><?= htmlspecialchars($fullAddress) ?></td>
                                                            <td><?= htmlspecialchars($passenger['contact_number'] ?? 'N/A') ?></td>
                                                            <td><span class="badge <?= $typeClass ?>"><?= strtoupper($passengerType) ?></span></td>
                                                            <td>
                                                                <?php if ($passenger['is_main_contact'] == 1): ?>
                                                                    <span class="badge badge-primary"><i class="fas fa-star"></i> Main Contact</span>
                                                                <?php else: ?>
                                                                    <span class="badge badge-light">Passenger</span>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- Passenger Summary -->
                                        <div class="row mt-3">
                                            <div class="col-md-6">
                                                <h6><i class="fas fa-chart-pie"></i> Passenger Summary</h6>
                                                <?php
                                                $totalPassengers = count($passengers);
                                                $regularPax = count(array_filter($passengers, fn($p) => ($p['passenger_type'] ?? 'regular') === 'regular'));
                                                $discountedPax = count(array_filter($passengers, fn($p) => ($p['passenger_type'] ?? 'regular') === 'discounted'));
                                                $childrenPax = count(array_filter($passengers, fn($p) => ($p['passenger_type'] ?? 'regular') === 'children'));
                                                $infantsPax = count(array_filter($passengers, fn($p) => ($p['passenger_type'] ?? 'regular') === 'infants'));
                                                ?>
                                                <ul class="list-unstyled">
                                                    <li><strong>Total Passengers:</strong> <?= $totalPassengers ?></li>
                                                    <li><strong>Regular Adults:</strong> <?= $regularPax ?></li>
                                                    <li><strong>Senior/PWD:</strong> <?= $discountedPax ?></li>
                                                    <li><strong>Children:</strong> <?= $childrenPax ?></li>
                                                    <li><strong>Infants:</strong> <?= $infantsPax ?></li>
                                                </ul>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="alert alert-info">
                                                    <small><i class="fas fa-info-circle"></i> <strong>Government Compliance:</strong><br>
                                                    This passenger manifest is required by the Tourism Office, Marina, and Coast Guard for safety and emergency response purposes.</small>
                                                </div>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <p class="text-muted">No passenger details available.</p>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Payment Information -->
                            <div class="card mb-3">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-credit-card"></i> Payment Information</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered table-striped">
                                        <tr><th>Booking ID:</th><td><?= htmlspecialchars($row['booking_code'] ?? 'Not set') ?></td></tr>
                                        <tr><th>Booking Status:</th><td><?= htmlspecialchars(ucfirst($row['booking_status'] ?? 'pending')) ?></td></tr>
                                        <tr><th>Environmental Fee:</th><td><?= !empty($row['environmental_fee']) ? formatPeso($row['environmental_fee']) : formatPeso(75) ?></td></tr>
                                        <tr><th>Payment Method:</th><td><?= htmlspecialchars($row['payment_method'] ?? 'Cash') ?></td></tr>
                                        <tr><th>Total:</th><td><?php
                                            if (!empty($row['total']) && $row['total'] > 0) {
                                                echo formatPeso($row['total']);
                                            } else {
                                                // Calculate total based on number of pax
                                                $pax = intval($row['no_of_pax'] ?? 1);
                                                $total = $pax * 75; // Default calculation: regular fee per person
                                                echo formatPeso($total);
                                            }
                                        ?></td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="$('.modal-backdrop').remove();">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Edit Booking Time Modal -->
<div class="modal fade" id="editBookingTimeModal" tabindex="-1" role="dialog" aria-labelledby="editBookingTimeModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <h5 class="modal-title text-white" id="editBookingTimeModalLabel">
                    <i class="fas fa-clock"></i> Set Booking Time
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editBookingTimeForm">
                    <input type="hidden" name="booking_id" value="<?= $booking_id ?>">
                    <div class="form-group">
                        <label for="bookingTime">Booking Time</label>
                        <input type="time" class="form-control" id="bookingTime" name="booking_time" required>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Setting the booking time helps track when the booking was made.
                    </div>
                    <div class="form-group">
                        <label>Preview</label>
                        <p class="form-control-plaintext" id="timePreview">
                            <i class="fas fa-clock"></i> <span id="previewText">Select a time above</span>
                        </p>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveBookingTime">Save</button>
            </div>
        </div>
    </div>
</div>

<script src="plugins/jquery/jquery.min.js"></script>
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="dist/js/adminlte.min.js"></script>
<script>
$(document).ready(function() {
    $('#bookingModal').modal('show');

    $('#bookingModal').on('hidden.bs.modal', function () {
        // Remove any lingering backdrop
        $('.modal-backdrop').remove();

        // Get the referrer from URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const referrer = urlParams.get('ref');

        // Add a small delay before redirecting to ensure modal is fully closed
        setTimeout(function() {
            // Redirect based on referrer
            if (referrer === 'all-reservations') {
                window.location.href = 'all-reservations.php';
            } else if (referrer === 'all-booking') {
                window.location.href = 'all-booking.php';
            } else {
                window.location.href = 'pending-bookings.php';
            }
        }, 300);
    });

    // Edit Booking Time button click
    $('#editBookingTimeBtn').click(function() {
        // Set current time as default
        var now = new Date();
        var hours = String(now.getHours()).padStart(2, '0');
        var minutes = String(now.getMinutes()).padStart(2, '0');
        $('#bookingTime').val(hours + ':' + minutes);

        // Update preview
        updateTimePreview(hours + ':' + minutes);

        // Show the edit booking time modal
        $('#editBookingTimeModal').modal('show');
    });

    // Update preview when time changes
    $('#bookingTime').on('change', function() {
        updateTimePreview($(this).val());
    });

    // Function to update time preview
    function updateTimePreview(timeString) {
        if (!timeString) {
            $('#previewText').text('Select a time above');
            return;
        }

        try {
            // Parse time
            var timeParts = timeString.split(':');
            var hours = parseInt(timeParts[0]);
            var minutes = parseInt(timeParts[1]);

            // Format for 12-hour display with AM/PM
            var ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12;
            hours = hours ? hours : 12; // Convert 0 to 12

            // Format the time string
            var formattedTime = hours + ':' + (minutes < 10 ? '0' + minutes : minutes) + ' ' + ampm;

            $('#previewText').text(formattedTime);
        } catch (e) {
            console.error('Error formatting time preview:', e);
            $('#previewText').text('Invalid time format');
        }
    }

    // Save Booking Time button click
    $('#saveBookingTime').click(function() {
        // Validate form
        if (!$('#bookingTime').val()) {
            alert('Please enter a valid booking time');
            return;
        }

        // Get form data
        var formData = $('#editBookingTimeForm').serialize();

        // Show loading state
        $('#saveBookingTime').html('<i class="fas fa-spinner fa-spin"></i> Saving...');
        $('#saveBookingTime').prop('disabled', true);

        // Send AJAX request to update booking time
        $.ajax({
            url: 'update-booking-time.php',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Update the booking time display
                    $('#bookingTimeDisplay').text(response.formatted_time);

                    // Hide the edit button
                    $('#editBookingTimeBtn').hide();

                    // Show success message
                    alert('Booking time updated successfully');

                    // Close the modal
                    $('#editBookingTimeModal').modal('hide');
                } else {
                    // Show error message
                    alert('Error: ' + (response.error || 'Unknown error occurred'));
                }
            },
            error: function(xhr, status, error) {
                // Show error message
                alert('Error: ' + error);
                console.error('AJAX error:', xhr.responseText);
            },
            complete: function() {
                // Reset button state
                $('#saveBookingTime').html('Save');
                $('#saveBookingTime').prop('disabled', false);
            }
        });
    });
});
</script>
</body>
</html>
