<?php
// Simple admin account checker
require_once 'admin/includes/config.php';

echo "<h2>Admin Account Status Check</h2>\n";

if (!$con) {
    echo "<p style='color: red;'>❌ Database connection failed!</p>\n";
    exit;
}

echo "<p style='color: green;'>✅ Database connected successfully</p>\n";

try {
    // Check if admins table exists
    $result = $con->query("SHOW TABLES LIKE 'admins'");
    if ($result->num_rows == 0) {
        echo "<p style='color: red;'>❌ Admins table does not exist!</p>\n";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Admins table exists</p>\n";
    
    // Check table structure
    echo "<h3>Table Structure:</h3>\n";
    $result = $con->query("DESCRIBE admins");
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>\n";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Check admin records
    echo "<h3>Admin Records:</h3>\n";
    $result = $con->query("SELECT admin_id, username, first_name, last_name, email, status, created_at FROM admins");
    
    if ($result->num_rows == 0) {
        echo "<p style='color: red;'>❌ No admin records found!</p>\n";
        
        // Offer to create default admin
        echo "<h3>Create Default Admin Account</h3>\n";
        echo "<p>Would you like to create a default admin account?</p>\n";
        echo "<form method='post'>\n";
        echo "<input type='hidden' name='create_admin' value='1'>\n";
        echo "<button type='submit' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Create Default Admin</button>\n";
        echo "</form>\n";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>ID</th><th>Username</th><th>First Name</th><th>Last Name</th><th>Email</th><th>Status</th><th>Created</th></tr>\n";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['admin_id']}</td>";
            echo "<td>{$row['username']}</td>";
            echo "<td>{$row['first_name']}</td>";
            echo "<td>{$row['last_name']}</td>";
            echo "<td>{$row['email']}</td>";
            echo "<td>{$row['status']}</td>";
            echo "<td>{$row['created_at']}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        echo "<h3>Test Login Credentials</h3>\n";
        echo "<p>Try logging in with:</p>\n";
        echo "<ul>\n";
        echo "<li><strong>Username:</strong> admin</li>\n";
        echo "<li><strong>Password:</strong> admin123 (if this is the default)</li>\n";
        echo "</ul>\n";
        echo "<p><a href='admin/login.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Admin Login</a></p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>\n";
}

// Handle admin creation
if (isset($_POST['create_admin'])) {
    try {
        $username = 'admin';
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        $first_name = 'System';
        $last_name = 'Administrator';
        $email = '<EMAIL>';
        $status = 'active';
        
        $stmt = $con->prepare("INSERT INTO admins (username, password, first_name, last_name, email, status, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
        $stmt->bind_param("ssssss", $username, $password, $first_name, $last_name, $email, $status);
        
        if ($stmt->execute()) {
            echo "<p style='color: green;'>✅ Default admin account created successfully!</p>\n";
            echo "<p><strong>Username:</strong> admin</p>\n";
            echo "<p><strong>Password:</strong> admin123</p>\n";
            echo "<p><a href='admin/login.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Admin Login</a></p>\n";
        } else {
            echo "<p style='color: red;'>❌ Failed to create admin account: " . $stmt->error . "</p>\n";
        }
        $stmt->close();
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error creating admin: " . $e->getMessage() . "</p>\n";
    }
}

$con->close();
?>
