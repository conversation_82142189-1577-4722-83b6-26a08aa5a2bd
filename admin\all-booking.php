<?php session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

include('includes/config.php');
// Database connection is already included in config.php

if(strlen($_SESSION['aid'])==0)
  {
header('location:login.php');
}
else{

  ?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Boat Booking System | <?php echo isset($_GET['filter']) && $_GET['filter'] == 'today' ? "Today's Bookings" : "All Bookings"; ?></title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <!-- DataTables -->
  <link rel="stylesheet" href="plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <!-- Navbar -->
<?php include_once("includes/navbar.php");?>
  <!-- /.navbar -->

 <?php include_once("includes/sidebar.php");?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1><?php echo isset($_GET['filter']) && $_GET['filter'] == 'today' ? "Today's Bookings" : "All Bookings"; ?></h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
              <li class="breadcrumb-item active"><?php echo isset($_GET['filter']) && $_GET['filter'] == 'today' ? "Today's Bookings" : "All Bookings"; ?></li>
            </ol>
          </div>
        </div>
      </div><!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header bg-primary">
                <h3 class="card-title"><i class="fas fa-list"></i> <?php echo isset($_GET['filter']) && $_GET['filter'] == 'today' ? "Today's Bookings List" : "All Bookings List"; ?></h3>
                <div class="card-tools">
                  <button type="button" class="btn btn-tool" onclick="document.querySelector('.card-body').classList.toggle('collapse')">
                    <i class="fas fa-minus"></i>
                  </button>
                  <button type="button" class="btn btn-tool" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt"></i>
                  </button>
                </div>
              </div>
              <!-- /.card-header -->
              <div class="card-body">
                <div class="table-responsive">
                  <table id="example1" class="table table-bordered table-striped">
                    <thead class="thead-dark">
                      <tr>
                        <th>#</th>
                        <th>Reference No</th>
                        <th>Customer Name</th>
                        <th>Email</th>
                        <th>Contact No</th>
                        <th>Emergency Name</th>
                        <th>Emergency No</th>
                        <th>Boat</th>
                        <th>Destination</th>
                        <th>Drop-off Location</th>
                        <th>Date</th>
                        <th>Time</th>
                        <th>Total Amount</th>
                        <th>Status</th>
                        <th>Action</th>
                    </thead>
                    <tbody>
<?php
// Add error handling for database connection
if (!$con) {
    die('Database connection failed: ' . mysqli_connect_error());
}

// Check if filter parameter is set
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';

// First, let's check if there are any bookings with the name Ralph Ramos
$check_sql = "SELECT booking_id, first_name, last_name, booking_status FROM bookings WHERE first_name LIKE '%Ralph%' OR last_name LIKE '%Ramos%'";
$check_result = $con->query($check_sql);
if ($check_result && $check_result->num_rows > 0) {
    error_log("Found bookings for Ralph Ramos in all-booking.php:");
    while ($row = $check_result->fetch_assoc()) {
        error_log("ID: {$row['booking_id']}, Name: {$row['first_name']} {$row['last_name']}, Status: {$row['booking_status']}");
    }
} else {
    error_log("No bookings found for Ralph Ramos in all-booking.php");
}

// Now check all pending bookings
$pending_sql = "SELECT COUNT(*) as count FROM bookings WHERE booking_status = 'pending'";
$pending_result = $con->query($pending_sql);
if ($pending_result && $pending_result->num_rows > 0) {
    $pending_count = $pending_result->fetch_assoc()['count'];
    error_log("Total pending bookings in all-booking.php: " . $pending_count);
}

// Check if required columns exist in the bookings table
$check_columns = [
    'drop_off_location' => false,
    'emergency_name' => false,
    'emergency_number' => false
];

foreach (array_keys($check_columns) as $column) {
    $check_column_query = "SHOW COLUMNS FROM bookings LIKE '$column'";
    $check_column_result = $con->query($check_column_query);
    $check_columns[$column] = $check_column_result->num_rows > 0;
}

// Build the SQL query based on which columns exist
$sql = "SELECT b.booking_id, b.booking_code, b.customer_id, b.boat_id, b.destination_id, b.start_date, b.booking_time, b.total, b.booking_status, b.created_at,
    b.first_name,
    b.last_name,
    b.email,
    b.contact_number,
    COALESCE(bt.name, CONCAT('Boat ', b.boat_id)) as boat_name,
    COALESCE(d.name, 'Not set') as destination_name";

// Add emergency_name only if the column exists
if ($check_columns['emergency_name']) {
    $sql .= ", b.emergency_name";
} else {
    $sql .= ", '' as emergency_name";
}

// Add emergency_number only if the column exists
if ($check_columns['emergency_number']) {
    $sql .= ", b.emergency_number";
} else {
    $sql .= ", '' as emergency_number";
}

// Add drop_off_location only if the column exists
if ($check_columns['drop_off_location']) {
    $sql .= ", b.drop_off_location";
} else {
    $sql .= ", '' as drop_off_location";
}

$sql .= " FROM bookings b
    LEFT JOIN boats bt ON b.boat_id = bt.boat_id
    LEFT JOIN destinations d ON b.destination_id = d.destination_id";

// Log the SQL query for debugging
error_log("Base SQL query: " . $sql);

// Define where clauses based on filter
$where_clauses = [];

// Apply filter for Today's Bookings
if ($filter == 'today') {
    // Get today's date
    $today = date('Y-m-d');

    // Check if is_today_booking column exists
    $check_column = $con->query("SHOW COLUMNS FROM bookings LIKE 'is_today_booking'");
    $has_today_flag = $check_column->num_rows > 0;

    if ($has_today_flag) {
        // If the column exists, use it as the primary filter
        $where_clauses[] = "(b.is_today_booking = 1 OR DATE(b.created_at) = '$today' OR DATE(b.start_date) = '$today')";
        error_log("Using is_today_booking flag and date for filtering");
    } else {
        // Otherwise, use only date fields
        $where_clauses[] = "(DATE(b.created_at) = '$today' OR DATE(b.start_date) = '$today')";
        error_log("Using only date fields for filtering");
    }

    // Make sure we're selecting emergency contact and drop-off location fields
    if (!strpos($sql, 'emergency_name')) {
        $sql = str_replace("FROM bookings b", ", b.emergency_name, b.emergency_number, b.drop_off_location FROM bookings b", $sql);
    }

    // Log for debugging
    error_log("Filtering for today's date: $today");
    error_log("Today's Bookings Query: " . $sql);

    // Add a debug query to see what bookings exist in the system
    $debug_query = "SELECT booking_id, booking_code, DATE(created_at) as created_date,
                   DATE(booking_time) as booking_date, DATE(start_date) as start_date
                   FROM bookings";
    $debug_result = $con->query($debug_query);
    if ($debug_result && $debug_result->num_rows > 0) {
        error_log("All bookings in system:");
        while ($row = $debug_result->fetch_assoc()) {
            error_log("ID: {$row['booking_id']}, Code: {$row['booking_code']}, Created: {$row['created_date']}, Booking: {$row['booking_date']}, Start: {$row['start_date']}");
        }
    } else {
        error_log("No bookings found in the system or query failed: " . $con->error);
    }
}

// Apply the WHERE clause if we have any conditions
if (!empty($where_clauses)) {
    $sql .= " WHERE " . implode(" AND ", $where_clauses);
    error_log("Applied WHERE clause: " . implode(" AND ", $where_clauses));
}

// Add ordering - Latest booking should be at the bottom as requested by Dean
$sql .= " ORDER BY b.created_at ASC";

// Prepare the query
$query = $con->prepare($sql);

// Log the query for debugging
error_log("Executing query with filter: " . $filter);

if (!$query) {
    echo '<tr><td colspan="15" class="text-center text-danger"><i class="fas fa-exclamation-triangle"></i> Error preparing query: ' . $con->error . '</td></tr>';
} else {
    $query->execute();
    $result = $query->get_result();

    if ($result->num_rows > 0) {
        $cnt = 1;
        while ($row = $result->fetch_assoc()) {
            $status = $row['booking_status'];

            // Set status class based on booking status
            if ($status == 'confirmed') {
                $status_class = 'bg-success';
                $status_text = 'CONFIRMED';
            } else if ($status == 'accepted') {
                $status_class = 'bg-info';
                $status_text = 'ACCEPTED';
            } else if ($status == 'pending') {
                $status_class = 'bg-warning';
                $status_text = 'PENDING';
            } else if ($status == 'cancelled' || $status == 'rejected') {
                $status_class = 'bg-danger';
                $status_text = strtoupper($status);
            } else {
                $status_class = 'bg-secondary';
                $status_text = strtoupper($status);
            }
            ?>
            <tr>
                <td><?php echo $cnt++; ?></td>
                <td><?php echo htmlspecialchars($row['booking_code']); ?></td>
                <td><?php echo htmlspecialchars($row['first_name'] ?? '').' '.htmlspecialchars($row['last_name'] ?? ''); ?></td>
                <td><?php echo htmlspecialchars($row['email'] ?? ''); ?></td>
                <td><?php echo htmlspecialchars($row['contact_number'] ?? ''); ?></td>
                <td><?php echo htmlspecialchars($row['emergency_name'] ?? 'Not set'); ?></td>
                <td><?php echo htmlspecialchars($row['emergency_number'] ?? 'Not set'); ?></td>
                <td><?php echo htmlspecialchars($row['boat_name'] ?? ''); ?></td>
                <td><?php echo htmlspecialchars($row['destination_name'] ?? ''); ?></td>
                <td><?php echo htmlspecialchars($row['drop_off_location'] ?? 'Not set'); ?></td>
                <td><?php echo date('M d, Y', strtotime($row['start_date'])); ?></td>
                <td><?php echo date('H:i', strtotime($row['booking_time'])); ?></td>
                <td>&#8369; <?php echo number_format($row['total'], 2); ?></td>
                <td><span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span></td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="view-booking.php?id=<?php echo $row['booking_id']; ?>&ref=all-booking" class="btn btn-info btn-sm" title="View Details">
                            <i class="fas fa-eye"></i>
                        </a>
                        <button class="btn btn-warning btn-sm edit-btn" data-id="<?php echo $row['booking_id']; ?>" data-booking-id="<?php echo $row['booking_id']; ?>" title="Edit Booking">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-danger btn-sm delete-btn" data-id="<?php echo $row['booking_id']; ?>" data-booking-id="<?php echo $row['booking_id']; ?>" title="Delete Booking">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
            <?php
        }
    } else {
        echo '<tr><td colspan="15" class="text-center"><i class="fas fa-info-circle"></i> No bookings found.</td></tr>';
    }

    $query->close();
}
?>
                  </tbody>

                </table>
              </div>
              <!-- /.card-body -->
            </div>
            <!-- /.card -->
          </div>
          <!-- /.col -->
        </div>
        <!-- /.row -->
      </div>
      <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
<?php include_once('includes/footer.php');?>

  <!-- Control Sidebar -->
  <aside class="control-sidebar control-sidebar-dark">
    <!-- Control sidebar content goes here -->
  </aside>
  <!-- /.control-sidebar -->
<!-- Notification Modal -->
<div class="modal fade" id="notifModal" tabindex="-1" role="dialog" aria-labelledby="notifModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="notifModalLabel">Notifications</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" id="notifList">
        <!-- Notifications will be loaded here -->
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmationModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfirmationModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header bg-danger">
        <h5 class="modal-title text-white" id="deleteConfirmationModalLabel">
          <i class="fas fa-exclamation-triangle"></i> Confirm Delete
        </h5>
        <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="text-center">
          <i class="fas fa-trash-alt fa-3x mb-3 text-danger"></i>
          <div class="delete-confirmation-message">
            <p class="mb-0">Are you sure you want to delete this booking?</p>
          </div>
          <p class="text-muted small mt-3">This action cannot be undone.</p>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">
          <i class="fas fa-times"></i> Cancel
        </button>
        <button type="button" class="btn btn-danger" id="confirmDelete">
          <i class="fas fa-trash"></i> Delete
        </button>
      </div>
    </div>
  </div>
</div>



<!-- Edit Booking Modal -->
<div class="modal fade" id="editBookingModal" tabindex="-1" role="dialog" aria-labelledby="editBookingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header bg-primary">
        <h5 class="modal-title text-white" id="editBookingModalLabel">
          <i class="fas fa-edit"></i> Edit Booking Status
        </h5>
        <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="editBookingForm">
          <input type="hidden" name="booking_id" id="editBookingId">
          <div class="form-group">
            <label>Booking Code</label>
            <input type="text" class="form-control" id="editBookingCode" readonly>
          </div>
          <div class="form-group">
            <label>Customer Name</label>
            <input type="text" class="form-control" id="editCustomerName" readonly>
          </div>
          <div class="form-group">
            <label>Current Status</label>
            <input type="text" class="form-control" id="editCurrentStatus" readonly>
          </div>
          <div class="form-group">
            <label>New Status</label>
            <select class="form-control" id="editStatus" name="booking_status" required>
              <option value="pending">Pending</option>
              <option value="accepted">Accepted</option>
              <option value="confirmed">Confirmed</option>
              <option value="rejected">Rejected</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <div class="form-group">
            <label>Reason for Status Change (Optional)</label>
            <textarea class="form-control" id="editStatusReason" name="status_reason" rows="3" placeholder="Enter reason for status change..."></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="saveStatus">Save Changes</button>
      </div>
    </div>
  </div>
</div>

<!-- Booking Details Modal -->
<div class="modal fade" id="bookingDetailsModal" tabindex="-1" role="dialog" aria-labelledby="bookingDetailsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header bg-info">
        <h5 class="modal-title text-white" id="bookingDetailsModalLabel">
          <i class="fas fa-info-circle"></i> Booking Details
        </h5>
        <div>
          <button type="button" class="btn btn-light btn-sm mr-2" id="printBookingDetails">
            <i class="fas fa-print"></i> Print
          </button>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
      </div>
      <div class="modal-body">
        <!-- Booking Information Section -->
        <div class="card mb-3">
          <div class="card-header bg-primary text-white">
            <h6 class="mb-0"><i class="fas fa-user"></i> Main Booker Information</h6>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered table-striped" id="bookingDetailsTable">
                <tr><th>First Name:</th><td id="viewFirstName"></td></tr>
                <tr><th>Last Name:</th><td id="viewLastName"></td></tr>
                <tr><th>Age:</th><td id="viewAge"></td></tr>
                <tr><th>Sex:</th><td id="viewSex"></td></tr>
                <tr><th>Contact Number:</th><td id="viewContactNumber"></td></tr>
                <tr><th>Email:</th><td id="viewEmail"></td></tr>
                <tr><th>Address:</th><td id="viewAddress"></td></tr>
                <tr><th>Emergency Contact Name:</th><td id="viewEmergencyName"></td></tr>
                <tr><th>Emergency Contact Number:</th><td id="viewEmergencyNumber"></td></tr>
              </table>
            </div>
          </div>
        </div>

        <!-- Tour Information Section -->
        <div class="card mb-3">
          <div class="card-header bg-success text-white">
            <h6 class="mb-0"><i class="fas fa-map-marker-alt"></i> Tour Information</h6>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered table-striped">
                <tr><th>Tour Destination:</th><td id="viewDestination"></td></tr>
                <tr><th>Drop-off Location:</th><td id="viewDropOffLocation"></td></tr>
                <tr><th>Number of Pax:</th><td id="viewNoOfPax"></td></tr>
                <tr><th>Start Date:</th><td id="viewStartDate"></td></tr>
                <tr><th>End Date:</th><td id="viewEndDate"></td></tr>
                <tr><th>Booking Time:</th><td id="viewBookingTime"></td></tr>
                <tr><th>Selected Boat:</th><td id="viewBoat"></td></tr>
                <tr><th>Boat Price:</th><td id="viewBoatPrice"></td></tr>
              </table>
            </div>
          </div>
        </div>

        <!-- Passenger Manifest Section (DEAN'S REQUIREMENT) -->
        <div class="card mb-3">
          <div class="card-header bg-warning text-dark">
            <h6 class="mb-0"><i class="fas fa-users"></i> Passenger Manifest (Government Requirement)</h6>
            <small class="text-muted">Required by Tourism Office, Marina, and Coast Guard for safety and emergency response</small>
          </div>
          <div class="card-body">
            <div id="passengerManifest">
              <p class="text-muted">Loading passenger details...</p>
            </div>
          </div>
        </div>

        <!-- Payment Information Section -->
        <div class="card mb-3">
          <div class="card-header bg-info text-white">
            <h6 class="mb-0"><i class="fas fa-credit-card"></i> Payment Information</h6>
          </div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered table-striped">
                <tr><th>Booking Code:</th><td id="viewBookingCode"></td></tr>
                <tr><th>Environmental Fee:</th><td id="viewEnvironmentalFee"></td></tr>
                <tr><th>Payment Method:</th><td id="viewPaymentMethod"></td></tr>
                <tr><th>Total Amount:</th><td id="viewTotal"></td></tr>
                <tr><th>Status:</th><td id="viewStatus"></td></tr>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- jQuery -->
<script src="plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- DataTables  & Plugins -->
<script src="plugins/datatables/jquery.dataTables.min.js"></script>
<script src="plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
<script src="plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
<script src="plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
<script src="plugins/jszip/jszip.min.js"></script>
<script src="plugins/pdfmake/pdfmake.min.js"></script>
<script src="plugins/pdfmake/vfs_fonts.js"></script>
<script src="plugins/datatables-buttons/js/buttons.html5.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.print.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.colVis.min.js"></script>
<!-- AdminLTE App -->
<script src="dist/js/adminlte.min.js"></script>
<!-- AdminLTE for demo purposes -->
<script src="dist/js/demo.js"></script>
<!-- Page specific script -->
<script>
  $(document).ready(function () {
    // DataTable will be initialized at the end of this script
    // to avoid multiple initializations

    // We don't need to initialize the delete confirmation modal here anymore
    // The delete button click handler now handles showing the modal after verifying the booking exists

    // The confirmDelete click handler has been moved to a document.on event handler below

    // Edit booking - when edit button is clicked
    $('.edit-btn').click(function() {
        // Get booking ID from the button (try both data-booking-id and data-id attributes)
        var id = $(this).data('booking-id') || $(this).data('id');

        console.log('Edit button clicked with ID:', id);

        // Validate booking ID
        if (!id || isNaN(parseInt(id)) || parseInt(id) <= 0) {
            alert('Invalid booking ID. Please try again or contact support.');
            console.error('Invalid booking ID detected for edit:', id);
            return;
        }

        // Get booking details from server first, then show modal only if successful
        $.post('get-booking-details.php', { id: id }, function(response) {
            if(response.success && response.data) {
                var booking = response.data;

                // Fill in the form with booking data
                $('#editBookingId').val(booking.booking_id);
                $('#editBookingCode').val(booking.booking_code || 'N/A');
                $('#editCustomerName').val((booking.first_name || 'Unknown') + ' ' + (booking.last_name || ''));
                $('#editCurrentStatus').val(booking.booking_status || 'pending');
                $('#editStatus').val(booking.booking_status || 'pending');

                // Now show the edit modal after data is loaded
                $('#editBookingModal').modal('show');
            } else {
                // Show more detailed error message
                var errorMsg = response.error || 'Unknown error occurred';
                alert('Could not load booking details: ' + errorMsg);
                console.error('Edit booking error:', response);
            }
        }, 'json').fail(function(jqXHR, textStatus, errorThrown) {
            alert('Error connecting to server. Please try again later.');
            console.error('AJAX error during edit:', textStatus, errorThrown);
        });
    });

    // Save status changes - when save button is clicked
    $('#saveStatus').click(function() {
        // Get booking ID from the hidden field
        var bookingId = $('#editBookingId').val();

        // Validate booking ID
        if (!bookingId || isNaN(parseInt(bookingId)) || parseInt(bookingId) <= 0) {
            alert('Invalid booking ID. Cannot update this booking.');
            console.error('Invalid booking ID detected for status update:', bookingId);
            $('#editBookingModal').modal('hide');
            return;
        }

        // Get all form data as a serialized string
        var formData = $('#editBookingForm').serialize();

        // Log the data being sent for debugging
        console.log('Sending update request with data:', formData);

        // Send data to server
        $.post('update-booking-status.php', formData, function(response) {
            if(response.success) {
                $('#editBookingModal').modal('hide');
                alert('Booking status updated successfully');
                location.reload(); // Refresh the page
            } else {
                var errorMsg = response.error || 'Unknown error occurred';
                alert('Error updating booking status: ' + errorMsg);
                console.error('Status update error:', response);
            }
        }, 'json').fail(function(jqXHR, textStatus, errorThrown) {
            alert('Error connecting to server. Please try again later.');
            console.error('AJAX error during status update:', textStatus, errorThrown);
        });
    });

    // Function to view booking details
    function viewBookingDetails(bookingId) {
      // Validate booking ID
      if (!bookingId || isNaN(parseInt(bookingId)) || parseInt(bookingId) <= 0) {
        alert('Invalid booking ID. Please try again or contact support.');
        console.error('Invalid booking ID detected:', bookingId);
        return;
      }

      console.log("Fetching details for booking ID:", bookingId);

      // Get booking details from server
      $.post('get-booking-details.php', { id: bookingId }, function(response) {
        console.log("Server response:", response);
        if(response.success && response.data) {
          var booking = response.data;

          // Format dates for display - with validation
          try {
            var startDate = booking.start_date && booking.start_date !== 'N/A' ? new Date(booking.start_date) : null;
            var endDate = booking.end_date && booking.end_date !== 'N/A' ? new Date(booking.end_date) : null;

            // Calculate duration between dates if both dates are valid
            var durationStr = 'N/A';
            if (startDate && endDate && !isNaN(startDate) && !isNaN(endDate)) {
              var days = Math.floor((endDate - startDate) / (24 * 60 * 60 * 1000));
              durationStr = days + ' day(s)';
            }
          } catch (e) {
            console.error('Error processing dates:', e);
            startDate = null;
            endDate = null;
          }

          // Fill in the details in the modal with safe defaults
          $('#viewFirstName').text(booking.first_name || 'Not set');
          $('#viewLastName').text(booking.last_name || 'Not set');
          $('#viewEmail').text(booking.email || 'Not set');
          $('#viewContactNumber').text(booking.contact_number || 'Not set');
          $('#viewAddress').text(booking.address || 'Not set');
          $('#viewEmergencyName').text(booking.emergency_name || 'Not set');
          $('#viewEmergencyNumber').text(booking.emergency_number || 'Not set');
          $('#viewDropOffLocation').text(booking.drop_off_location || 'Not set');
          $('#viewNoOfPax').text(booking.no_of_pax || 'Not set');
          $('#viewBoat').text(booking.boat_name || 'Not set');
          $('#viewDestination').text(booking.destination_name || 'Not set');
          $('#viewStartDate').text(startDate && !isNaN(startDate) ? startDate.toLocaleDateString() : 'Not set');
          $('#viewEndDate').text(endDate && !isNaN(endDate) ? endDate.toLocaleDateString() : 'Not set');
          $('#viewBookingTime').text(booking.booking_time || 'Not set');
          $('#viewPaymentMethod').text(booking.payment_method || 'Not set');
          $('#viewTotal').text('₱ ' + (booking.total || '0.00'));
          $('#viewAge').text(booking.age || 'Not set');
          $('#viewSex').text(booking.sex || 'Not set');
          $('#viewEnvironmentalFee').text('₱ ' + (booking.environmental_fee || '0.00'));
          $('#viewBoatPrice').text('₱ ' + (booking.price_per_day || '0.00') + ' per day');
          $('#viewBookingCode').text(booking.booking_code || 'Not set');

          // Set status with appropriate color
          var statusClass = 'secondary';
          if(booking.booking_status == 'confirmed') statusClass = 'success';
          if(booking.booking_status == 'pending') statusClass = 'warning';
          if(booking.booking_status == 'cancelled') statusClass = 'danger';
          if(booking.booking_status == 'accepted') statusClass = 'info';
          if(booking.booking_status == 'rejected') statusClass = 'danger';

          $('#viewStatus').html('<span class="badge badge-' + statusClass + '">' +
                               (booking.booking_status ? booking.booking_status.toUpperCase() : 'UNKNOWN') + '</span>');

          // Display Passenger Manifest (DEAN'S REQUIREMENT)
          displayPassengerManifest(booking.passengers || []);

          // Show the modal
          $('#bookingDetailsModal').modal('show');
        } else {
          // Show more detailed error message
          var errorMsg = response.error || 'Unknown error occurred';
          alert('Error loading booking details: ' + errorMsg);
          console.error('Booking details error:', response);
        }
      }, 'json').fail(function(jqXHR, textStatus, errorThrown) {
        alert('Error connecting to server. Please try again later.');
        console.error('AJAX error:', textStatus, errorThrown);
      });
    }

    // Function to display passenger manifest (DEAN'S REQUIREMENT)
    function displayPassengerManifest(passengers) {
        var manifestContainer = $('#passengerManifest');

        if (!passengers || passengers.length === 0) {
            manifestContainer.html('<p class="text-muted">No passenger details available.</p>');
            return;
        }

        var manifestHTML = '<div class="table-responsive">';
        manifestHTML += '<table class="table table-sm table-bordered">';
        manifestHTML += '<thead class="thead-light">';
        manifestHTML += '<tr>';
        manifestHTML += '<th>#</th>';
        manifestHTML += '<th>Name</th>';
        manifestHTML += '<th>Age</th>';
        manifestHTML += '<th>Sex</th>';
        manifestHTML += '<th>Address</th>';
        manifestHTML += '<th>Contact</th>';
        manifestHTML += '<th>Type</th>';
        manifestHTML += '<th>Role</th>';
        manifestHTML += '</tr>';
        manifestHTML += '</thead>';
        manifestHTML += '<tbody>';

        passengers.forEach(function(passenger, index) {
            var rowClass = passenger.is_main_contact == 1 ? 'table-primary' : '';
            var passengerType = passenger.passenger_type || 'regular';
            var typeClass = '';

            switch(passengerType) {
                case 'regular': typeClass = 'badge-success'; break;
                case 'discounted': typeClass = 'badge-info'; break;
                case 'children': typeClass = 'badge-warning'; break;
                case 'infants': typeClass = 'badge-secondary'; break;
                default: typeClass = 'badge-light';
            }

            var fullAddress = '';
            if (passenger.street_address) {
                fullAddress = passenger.street_address + ', ';
            }
            fullAddress += (passenger.city || '') + ', ' + (passenger.province || '');

            manifestHTML += '<tr class="' + rowClass + '">';
            manifestHTML += '<td>' + (index + 1) + '</td>';
            manifestHTML += '<td><strong>' + (passenger.first_name || '') + ' ' + (passenger.last_name || '') + '</strong></td>';
            manifestHTML += '<td>' + (passenger.age || 'N/A') + '</td>';
            manifestHTML += '<td>' + (passenger.sex || 'N/A') + '</td>';
            manifestHTML += '<td>' + fullAddress + '</td>';
            manifestHTML += '<td>' + (passenger.contact_number || 'N/A') + '</td>';
            manifestHTML += '<td><span class="badge ' + typeClass + '">' + passengerType.toUpperCase() + '</span></td>';
            manifestHTML += '<td>';
            if (passenger.is_main_contact == 1) {
                manifestHTML += '<span class="badge badge-primary"><i class="fas fa-star"></i> Main Contact</span>';
            } else {
                manifestHTML += '<span class="badge badge-light">Passenger</span>';
            }
            manifestHTML += '</td>';
            manifestHTML += '</tr>';
        });

        manifestHTML += '</tbody>';
        manifestHTML += '</table>';
        manifestHTML += '</div>';

        // Add summary information
        var totalPassengers = passengers.length;
        var mainContact = passengers.filter(p => p.is_main_contact == 1).length;
        var regularPax = passengers.filter(p => p.passenger_type === 'regular').length;
        var discountedPax = passengers.filter(p => p.passenger_type === 'discounted').length;
        var childrenPax = passengers.filter(p => p.passenger_type === 'children').length;
        var infantsPax = passengers.filter(p => p.passenger_type === 'infants').length;

        manifestHTML += '<div class="row mt-3">';
        manifestHTML += '<div class="col-md-6">';
        manifestHTML += '<h6><i class="fas fa-chart-pie"></i> Passenger Summary</h6>';
        manifestHTML += '<ul class="list-unstyled">';
        manifestHTML += '<li><strong>Total Passengers:</strong> ' + totalPassengers + '</li>';
        manifestHTML += '<li><strong>Regular Adults:</strong> ' + regularPax + '</li>';
        manifestHTML += '<li><strong>Senior/PWD:</strong> ' + discountedPax + '</li>';
        manifestHTML += '<li><strong>Children:</strong> ' + childrenPax + '</li>';
        manifestHTML += '<li><strong>Infants:</strong> ' + infantsPax + '</li>';
        manifestHTML += '</ul>';
        manifestHTML += '</div>';
        manifestHTML += '<div class="col-md-6">';
        manifestHTML += '<div class="alert alert-info">';
        manifestHTML += '<small><i class="fas fa-info-circle"></i> <strong>Government Compliance:</strong><br>';
        manifestHTML += 'This passenger manifest is required by the Tourism Office, Marina, and Coast Guard for safety and emergency response purposes.</small>';
        manifestHTML += '</div>';
        manifestHTML += '</div>';
        manifestHTML += '</div>';

        manifestContainer.html(manifestHTML);
    }

    // Initialize view booking details modal
    $('.view-btn').click(function() {
        var id = $(this).data('id');
        console.log("View button clicked for booking ID:", id);
        viewBookingDetails(id);
    });

    // Delete booking - when delete button is clicked
    $(document).on('click', '.delete-btn', function() {
        // Get booking ID from the button (try both data-booking-id and data-id attributes)
        var id = $(this).data('booking-id') || $(this).data('id');

        console.log('Delete button clicked with ID:', id);

        // Validate booking ID
        if (!id || isNaN(parseInt(id)) || parseInt(id) <= 0) {
            alert('Invalid booking ID. Cannot delete this booking.');
            console.error('Invalid booking ID detected for delete:', id);
            return;
        }

        // First verify the booking exists before showing delete confirmation
        $.ajax({
            url: 'get-booking-details.php',
            type: 'POST',
            data: { id: id },
            dataType: 'json',
            success: function(response) {
                if(response.success && response.data) {
                    // Booking exists, show confirmation modal
                    var booking = response.data;

                    // Store the ID in the confirm delete button
                    $('#confirmDelete').attr('data-booking-id', id);
                    $('#confirmDelete').attr('data-id', id);

                    // Add booking info to confirmation message
                    var confirmMsg = 'Are you sure you want to delete this booking?<br><br>';
                    confirmMsg += '<strong>Booking ID:</strong> ' + (booking.booking_code || id) + '<br>';
                    confirmMsg += '<strong>Customer:</strong> ' + (booking.first_name || '') + ' ' + (booking.last_name || '') + '<br>';
                    confirmMsg += '<strong>Status:</strong> ' + (booking.booking_status ? booking.booking_status.toUpperCase() : 'UNKNOWN');

                    $('.delete-confirmation-message').html(confirmMsg);

                    // Show the modal
                    $('#deleteConfirmationModal').modal('show');
                } else {
                    // Booking doesn't exist or error occurred
                    var errorMsg = response.error || 'Booking not found';
                    alert('Cannot delete booking: ' + errorMsg);
                    console.error('Delete booking verification error:', response);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error during delete verification:', status, error);
                console.error('Response text:', xhr.responseText);
                alert('Error connecting to server. Please try again later.');
            }
        });
    });

    // When delete is confirmed
    $(document).on('click', '#confirmDelete', function() {
        // Try both data attributes and attr for maximum compatibility
        var id = $(this).attr('data-booking-id') || $(this).attr('data-id') || $(this).data('booking-id') || $(this).data('id');

        console.log('Confirm delete clicked with ID:', id);

        // Validate booking ID again as a safeguard
        if (!id || isNaN(parseInt(id)) || parseInt(id) <= 0) {
            alert('Invalid booking ID. Cannot delete this booking.');
            console.error('Invalid booking ID detected in confirmation:', id);
            $('#deleteConfirmationModal').modal('hide');
            return;
        }

        // Show loading indicator
        $('.delete-confirmation-message').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Deleting booking...</div>');

        // Send delete request to server
        $.ajax({
            url: 'delete-booking.php',
            type: 'POST',
            data: {
                id: id,
                booking_id: id // Send both parameters for maximum compatibility
            },
            dataType: 'json',
            success: function(response) {
                console.log('Delete response:', response);
                if(response.success) {
                    $('#deleteConfirmationModal').modal('hide');
                    alert('Booking deleted successfully');
                    location.reload(); // Refresh the page
                } else {
                    $('.delete-confirmation-message').html('<div class="alert alert-danger">Error: ' + (response.error || 'Unknown error') + '</div>');
                    console.error('Delete booking error:', response);
                }
            },
            error: function(xhr, status, error) {
                console.error('Delete request failed:', status, error);
                console.error('Response text:', xhr.responseText);

                // Try to parse the response if it's JSON
                var errorMessage = 'An error occurred while deleting the booking.';
                try {
                    if (xhr.responseText) {
                        var jsonResponse = JSON.parse(xhr.responseText);
                        if (jsonResponse.error) {
                            errorMessage = jsonResponse.error;
                        }
                    }
                } catch (e) {
                    console.error('Error parsing JSON response:', e);
                }

                $('.delete-confirmation-message').html('<div class="alert alert-danger">Error: ' + errorMessage + '</div>');

                // Don't hide the modal so the user can see the error
            }
        });
    });

    // Check if DataTable is already initialized
    if ($.fn.dataTable.isDataTable('#example1')) {
      // If already initialized, destroy it first
      $('#example1').DataTable().destroy();
    }

    // Initialize DataTable with basic options
    $("#example1").DataTable({
      "responsive": true,
      "lengthChange": true,
      "pageLength": 10,
      "buttons": ["copy", "excel", "pdf", "print"]
    }).buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');
  });

// Simple function to load notifications
$(document).ready(function() {
  // When notification modal is shown
  $('#notifModal').on('show.bs.modal', function() {
    // Get notifications from server
    $.get('notifications_fetch.php', function(data) {
      var notifications = JSON.parse(data);
      var notifList = $('#notifList');

      // Update notification count
      $('#notifCount').text(notifications.length > 0 ? notifications.length : '');

      // Clear previous notifications
      notifList.empty();

      // Show message if no notifications
      if (notifications.length === 0) {
        notifList.append('<div class="text-center">No new notifications</div>');
      } else {
        // Add each notification to the list
        notifications.forEach(function(notif) {
          notifList.append(
            '<div class="alert alert-info" style="cursor:pointer;" onclick="markAsRead(' +
            notif.id + ')">' + notif.message + '</div>'
          );
        });
      }
    });
  });
});

// Mark notification as read and go to pending bookings
function markAsRead(id) {
  $.get('notifications_mark_read.php?id=' + id, function() {
    window.location.href = 'pending-bookings.php';
  });
}
</script>
</body>
</html>
<?php } ?>