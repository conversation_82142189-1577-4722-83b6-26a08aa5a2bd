<?php
session_start();
echo "<h2>Debug Test for Pending Bookings</h2>";

echo "<h3>Session Info:</h3>";
echo "Session ID: " . session_id() . "<br>";
echo "Admin Login: " . (isset($_SESSION['alogin']) ? $_SESSION['alogin'] : 'NOT SET') . "<br>";
echo "Session Length: " . (isset($_SESSION['alogin']) ? strlen($_SESSION['alogin']) : '0') . "<br>";

echo "<h3>File Check:</h3>";
echo "Current file: " . __FILE__ . "<br>";
echo "Pending bookings file exists: " . (file_exists('pending-bookings.php') ? 'YES' : 'NO') . "<br>";

echo "<h3>Config Test:</h3>";
try {
    include('includes/config.php');
    echo "Config loaded successfully<br>";
    echo "Database connection: " . (isset($con) ? 'Connected' : 'Not connected') . "<br>";
} catch (Exception $e) {
    echo "Config error: " . $e->getMessage() . "<br>";
}

echo "<h3>Test Links:</h3>";
echo '<a href="pending-bookings.php">Go to Pending Bookings</a><br>';
echo '<a href="dashboard.php">Go to Dashboard</a><br>';
?>
