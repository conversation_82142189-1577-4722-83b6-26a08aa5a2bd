<?php
// Debug logging function
function debug_log($message, $data = []) {
    $log_file = __DIR__ . '/../logs/booking_debug.log';
    $log_dir = dirname($log_file);

    // Create logs directory if it doesn't exist
    if (!file_exists($log_dir)) {
        mkdir($log_dir, 0777, true);
    }

    $timestamp = date('Y-m-d H:i:s');
    $data_string = !empty($data) ? ' - Data: ' . json_encode($data) : '';
    $log_message = "[$timestamp] $message$data_string" . PHP_EOL;

    file_put_contents($log_file, $log_message, FILE_APPEND);
}

// Log database errors
function log_db_error($source, $message, $data = []) {
    debug_log("DB ERROR in $source: $message", $data);
}

// Log booking ID issues
function log_booking_id_issue($booking_id, $message, $data = []) {
    debug_log("Booking ID issue: $message", array_merge(['booking_id' => $booking_id], $data));
}

//Import PHPMailer classes into the global namespace
//These must be at the top of your script, not inside a function
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

//Require the PHPMailer files directly
require '../vendor/phpmailer/phpmailer/src/Exception.php';
require '../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require '../vendor/phpmailer/phpmailer/src/SMTP.php';

// Include database connection
require_once '../config/db_connect.php';

// Get booking data from POST
$booking = $_POST;

// Debug log the booking data
debug_log('Received booking data', $booking);

try {
    // Create a new PHPMailer instance
    $mail = new PHPMailer(true);

    // Server settings
    $mail->isSMTP();
    $mail->Host = 'smtp.gmail.com';
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>';
    $mail->Password = 'rvxm rvxm rvxm rvxm'; // Replace with actual password
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port = 587;

    // Recipients
    $mail->setFrom('<EMAIL>', 'Booking System');
    $mail->addAddress($booking['emailAddress'], $booking['firstName'] . ' ' . $booking['lastName']);

    // Content
    $mail->isHTML(true);
    $mail->Subject = 'Booking Confirmation';

    // Generate a unique booking code
    $booking_code = 'BOOKING-' . date('Ymd') . '-' . rand(10000, 99999);

    // Set customer ID (default to 0 if not provided)
    $customer_id = isset($booking['customerId']) ? intval($booking['customerId']) : 0;

    // Set boat ID
    $boat_id = isset($booking['boatId']) ? intval($booking['boatId']) : 0;

    // Set destination ID
    $destination_id = isset($booking['destinationId']) ? intval($booking['destinationId']) : 0;

    // Set destination name
    $destination_name = isset($booking['destinationName']) ? $booking['destinationName'] : 'Not specified';

    // Set number of passengers
    $no_of_pax = isset($booking['noOfPax']) ? intval($booking['noOfPax']) : 0;

    // Set dates and times
    $today = date('Y-m-d');
    $now = date('Y-m-d H:i:s');

    // ALWAYS set start_date to today to ensure it appears in Today's Bookings
    $start_date = $today . ' 00:00:00';
    $end_date = isset($booking['endDate']) ? $booking['endDate'] : $today . ' 00:00:00';
    $booking_time = $now;
    $created_at = $now;

    // Set fees and payment
    $environmental_fee = isset($booking['environmentalFee']) ? floatval($booking['environmentalFee']) : 0;
    $payment_method = isset($booking['paymentMethod']) ? $booking['paymentMethod'] : 'Manual Payment';
    $total = isset($booking['total']) ? floatval($booking['total']) : 0;

    // Set booking status
    $booking_status = 'pending';

    // EMERGENCY FIX: Direct SQL insert to ensure bookings work
    $insert_sql = "INSERT INTO bookings (
        booking_code, first_name, last_name, email, contact_number, boat_id,
        start_date, booking_status, created_at, is_today_booking, tour_destination,
        drop_off_location, no_of_pax
    ) VALUES (
        '$booking_code', '{$booking['firstName']}', '{$booking['lastName']}',
        '{$booking['emailAddress']}', '{$booking['contactNumber']}', $boat_id,
        '$start_date', '$booking_status', '$created_at', 1, '$destination_name',
        '{$booking['dropOffLocation']}', $no_of_pax
    )";

    debug_log('EMERGENCY FIX: Using direct SQL insert: ' . $insert_sql);

    $result = $con->query($insert_sql);

    if (!$result) {
        debug_log('Direct SQL insert failed: ' . $con->error);
        throw new Exception('Database error: ' . $con->error);
    } else {
        debug_log('Direct SQL insert successful');

        // Get the booking ID of the newly inserted booking
        $new_booking_id = $con->insert_id;
        debug_log('New booking ID: ' . $new_booking_id);

        debug_log('Booking inserted successfully: ' . $booking_code);
    }

    // Build email content
    $mail->Body = "
    <h2>Booking Confirmation</h2>
    <p>Dear {$booking['firstName']} {$booking['lastName']},</p>
    <p>Thank you for your booking. Your booking has been received and is pending confirmation.</p>
    <p><strong>Booking Code:</strong> $booking_code</p>
    <p><strong>Destination:</strong> $destination_name</p>
    <p><strong>Date:</strong> $start_date</p>
    <p><strong>Number of Passengers:</strong> $no_of_pax</p>
    <p><strong>Total Amount:</strong> ₱" . number_format($total, 2) . "</p>
    <p>We will contact you shortly to confirm your booking.</p>
    <p>Thank you for choosing our service!</p>
    ";

    // Send email
    $mail->send();
    debug_log('Email sent successfully to ' . $booking['emailAddress']);

    echo json_encode(['success' => true, 'message' => 'Verification email sent successfully']);
} catch (Exception $e) {
    debug_log('Exception caught: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => "Message could not be sent. Mailer Error: {$mail->ErrorInfo}"]);
}
