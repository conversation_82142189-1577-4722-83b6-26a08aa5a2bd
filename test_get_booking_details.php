<?php
// Test the get-booking-details.php endpoint directly
session_start();

// Simulate admin login
$_SESSION['aid'] = 1; // Set admin ID

// First, get a valid booking ID
require_once 'admin/includes/config.php';
$result = $con->query("SELECT booking_id FROM bookings LIMIT 1");
if ($result->num_rows > 0) {
    $booking = $result->fetch_assoc();
    $test_booking_id = $booking['booking_id'];
    echo "Testing with booking ID: $test_booking_id\n\n";
} else {
    echo "No bookings found in database!\n";
    exit;
}

// Simulate POST request
$_POST['id'] = $test_booking_id;
$_SERVER['REQUEST_METHOD'] = 'POST';

// Capture output
ob_start();

// Include the get-booking-details.php file
include 'admin/get-booking-details.php';

$output = ob_get_clean();

echo "=== GET-BOOKING-DETAILS.PHP OUTPUT ===\n";
echo $output . "\n";

// Try to decode JSON
$json_data = json_decode($output, true);

if ($json_data) {
    echo "\n=== PARSED JSON DATA ===\n";
    echo "Success: " . ($json_data['success'] ? 'true' : 'false') . "\n";
    
    if ($json_data['success']) {
        echo "Booking found!\n";
        echo "Name: " . $json_data['data']['first_name'] . " " . $json_data['data']['last_name'] . "\n";
        echo "Email: " . $json_data['data']['email'] . "\n";
        echo "Status: " . $json_data['data']['booking_status'] . "\n";
        echo "Passengers: " . count($json_data['data']['passengers']) . "\n";
    } else {
        echo "Error: " . $json_data['error'] . "\n";
    }
} else {
    echo "\n=== JSON DECODE FAILED ===\n";
    echo "Raw output: " . $output . "\n";
}
?>
